# Linux虚拟机防病毒客户端Makefile - 修复版本

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g -D_GNU_SOURCE

# 检查是否有Xen库
XEN_LIBS_AVAILABLE := $(shell pkg-config --exists xencontrol xenstore 2>/dev/null && echo yes || echo no)

ifeq ($(XEN_LIBS_AVAILABLE),yes)
    # 添加Xen相关的宏定义
    CFLAGS += -DHAVE_XEN_LIBS -D__XEN_TOOLS__=1
    # 获取Xen库的编译和链接标志
    XEN_CFLAGS := $(shell pkg-config --cflags xencontrol xenstore 2>/dev/null)
    XEN_LDFLAGS := $(shell pkg-config --libs xencontrol xenstore 2>/dev/null)
    CFLAGS += $(XEN_CFLAGS)
    LDFLAGS = $(XEN_LDFLAGS) -lpthread
    SOURCES_SUFFIX = _real
else
    LDFLAGS = -lpthread
    SOURCES_SUFFIX = _mock
endif

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj
BINDIR = bin

# 源文件和目标文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
ifeq ($(XEN_LIBS_AVAILABLE),yes)
    CLIENT_SOURCES = av_linux_client.c main.c
else
    CLIENT_SOURCES = av_linux_client.c xen_mock_impl.c main.c
endif

SOURCES = $(COMMON_SOURCES) $(CLIENT_SOURCES)
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/av_linux_client

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I.
ifeq ($(XEN_LIBS_AVAILABLE),yes)
    INCLUDES += -I/usr/include/xen
endif

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Linking $@..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Build complete: $@"
ifeq ($(XEN_LIBS_AVAILABLE),yes)
	@echo "Built with real Xen libraries"
else
	@echo "Built with mock Xen implementation"
endif

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $<..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning build files..."
	rm -rf $(OBJDIR) $(BINDIR)

# 检查依赖
check-deps:
	@echo "Checking dependencies..."
ifeq ($(XEN_LIBS_AVAILABLE),yes)
	@echo "✓ Xen libraries found:"
	@pkg-config --modversion xencontrol xenstore
	@echo "✓ Xen headers found at: /usr/include/xen"
else
	@echo "⚠ Xen libraries not found, will use mock implementation"
	@echo "To install Xen libraries on Ubuntu/Debian:"
	@echo "  sudo apt install libxen-dev"
endif

# 显示编译信息
info:
	@echo "Compilation settings:"
	@echo "  XEN_LIBS_AVAILABLE: $(XEN_LIBS_AVAILABLE)"
	@echo "  CFLAGS: $(CFLAGS)"
	@echo "  LDFLAGS: $(LDFLAGS)"
	@echo "  INCLUDES: $(INCLUDES)"
	@echo "  SOURCES: $(SOURCES)"

.PHONY: all clean directories check-deps info