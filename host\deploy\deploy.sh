#!/bin/bash
# XenServer Antivirus Deployment Script
# Generated on: Fri Sep  5 10:54:51 CST 2025

echo '=== XenServer Antivirus Deployment ==='
echo 'Checking target environment...'

# Check if running as root
if [ $(id -u) -ne 0 ]; then
    echo 'ERROR: Must run as root'
    exit 1
fi

# Check Xen environment
if ! xl info >/dev/null 2>&1; then
    echo 'ERROR: Xen environment not detected'
    exit 1
fi

echo 'Installing XenServer Antivirus Host Service...'
install -m 755 xenserver-antivirus-cross /usr/local/bin/
install -m 755 xenserver-antivirus-cross-static /usr/local/bin/

echo 'Testing installation...'
/usr/local/bin/xenserver-antivirus-cross-static --test

echo 'Installation complete!'
echo 'Usage: /usr/local/bin/xenserver-antivirus-cross-static [options]'
