# XenServer防病毒系统 - glibc 2.17兼容版本部署指南

## 概述
此版本专门针对Citrix Hypervisor 8.2.1 (glibc 2.17)优化，解决了从WSL编译的程序在老版本Xen环境中的兼容性问题。

## 问题背景
- **问题**: 从WSL编译的程序无法在Citrix Hypervisor 8.2.1上运行
- **原因**: glibc版本不兼容（WSL使用glibc 2.39，目标环境使用glibc 2.17）
- **解决方案**: 使用静态链接和兼容性编译选项

## 部署步骤

### 1. 准备部署包
```bash
# 在WSL中构建
./build_glibc217.sh

# 或使用Windows批处理
.\build_glibc217.bat
```

### 2. 传输到目标环境
```bash
# 方法1: 使用压缩包
scp host/xenserver-antivirus-glibc217-*.tar.gz root@your-xenserver:/tmp/

# 方法2: 直接传输部署目录
scp -r host/deploy_glibc217/ root@your-xenserver:/tmp/
```

### 3. 在Citrix Hypervisor上部署
```bash
# 解压（如果使用压缩包）
cd /tmp
tar -xzf xenserver-antivirus-glibc217-*.tar.gz

# 运行部署脚本
sudo ./deploy_glibc217.sh

# 或手动安装
sudo install -m 755 xenserver-antivirus-glibc217-static /usr/local/bin/
```

### 4. 验证安装
```bash
# 检查程序
/usr/local/bin/xenserver-antivirus-glibc217-static --help

# 测试运行
sudo /usr/local/bin/xenserver-antivirus-glibc217-static --test

# 正常运行
sudo /usr/local/bin/xenserver-antivirus-glibc217-static -v
```

## 技术特性

### 编译优化
- **静态链接**: 避免动态库依赖问题
- **glibc兼容**: 针对glibc 2.17优化
- **交叉编译**: WSL环境编译，Xen环境运行

### 兼容性保证
- **目标系统**: Citrix Hypervisor 8.2.1
- **Xen版本**: 4.13.x - 4.17.x
- **架构**: x86_64

### 文件说明
- `xenserver-antivirus-glibc217-static`: 主程序（静态链接）
- `deploy_glibc217.sh`: 自动部署脚本
- `README.md`: 部署说明文档

## 故障排除

### 常见问题
1. **权限错误**: 确保以root权限运行
2. **Xen环境检测失败**: 确保在Xen宿主机上运行
3. **glibc版本警告**: 正常现象，程序仍可运行

### 调试命令
```bash
# 检查程序依赖
ldd /usr/local/bin/xenserver-antivirus-glibc217-static

# 检查Xen环境
xl info

# 检查glibc版本
ldd --version
```

## 构建信息
- **构建时间**: $(date)
- **编译环境**: WSL (Ubuntu)
- **编译器**: gcc 13.3.0
- **目标glibc**: 2.17
- **程序大小**: ~1.3MB
