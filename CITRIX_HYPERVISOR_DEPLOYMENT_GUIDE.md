# XenServer防病毒系统 - Citrix Hypervisor部署指南

## 🎯 专门解决方案

基于您的Citrix Hypervisor 8.2.1环境，我们创建了专门的解决方案来解决以下问题：

1. **glibc版本不兼容** - WSL (glibc 2.39) vs Citrix Hypervisor (glibc 2.17)
2. **xenstore设备缺失** - Citrix使用oxenstored而不是标准xenstored
3. **权限访问问题** - /dev/xen/设备权限配置

## 📦 部署包内容

### Citrix Hypervisor专用版本
```
host/deploy_citrix/
├── xenserver-antivirus-citrix-static     # 主程序 (1.2MB)
├── deploy_citrix.sh                      # 自动部署脚本
├── citrix_hypervisor_fix.sh             # Citrix环境修复
├── citrix_xenstore_fix.sh               # xenstore访问修复
└── fix_xen_device_permissions.sh        # 设备权限修复
```

### 压缩包
- `host/xenserver-antivirus-citrix-20250905-1430.tar.gz` (约500KB)

## 🚀 部署步骤

### 1. 传输部署包到Citrix Hypervisor

```bash
# 从您的开发机器传输到Citrix Hypervisor
scp host/xenserver-antivirus-citrix-20250905-1430.tar.gz root@your-citrix-host:/tmp/
```

### 2. 在Citrix Hypervisor上解压并部署

```bash
# 登录到Citrix Hypervisor
ssh root@your-citrix-host

# 解压部署包
cd /tmp
tar -xzf xenserver-antivirus-citrix-20250905-1430.tar.gz

# 运行自动部署
sudo ./deploy_citrix.sh
```

### 3. 修复Citrix环境（如果需要）

如果程序仍然报告权限错误，按顺序运行以下修复脚本：

```bash
# 1. 运行Citrix xenstore修复
sudo ./citrix_xenstore_fix.sh

# 2. 如果仍有问题，运行设备权限修复
sudo ./fix_xen_device_permissions.sh

# 3. 最后运行完整的Citrix环境修复
sudo ./citrix_hypervisor_fix.sh
```

### 4. 测试运行

```bash
# 测试程序
sudo /usr/local/bin/xenserver-antivirus-citrix-static --test

# 正常运行
sudo /usr/local/bin/xenserver-antivirus-citrix-static -v
```

## 🔧 Citrix专用优化

### 程序特性
- **静态链接**: 无动态库依赖，兼容glibc 2.17
- **Citrix环境检测**: 自动识别oxenstored服务
- **智能重试**: 处理Citrix环境中的临时错误
- **详细诊断**: 提供Citrix特定的错误信息

### 环境变量设置
程序会自动设置以下环境变量：
```bash
export XENSTORED_ROOTDIR="/var/lib/xenstored"
export XENSTORE_DOMAIN_INTERFACE=1
export XEN_DOMAIN_INTERFACE=1
export XENSTORED_PATH="/var/run/xenstored"
```

### 设备权限修复
自动修复以下设备权限：
```bash
chmod 666 /dev/xen/privcmd
chmod 666 /dev/xen/xenbus
chmod 666 /dev/xen/xenbus_backend
```

## 🎯 解决的具体问题

### 1. 缺少/dev/xen/xenstore设备
**问题**: Citrix Hypervisor不提供标准的xenstore设备文件
**解决**: 创建到oxenstored socket的符号链接或字符设备

### 2. oxenstored vs xenstored
**问题**: 标准Xen使用xenstored，Citrix使用oxenstored
**解决**: 程序自动检测并适配oxenstored服务

### 3. 权限被拒绝错误
**问题**: xc_domain_getinfolist返回Permission denied
**解决**: 修复/dev/xen/设备权限，设置正确的环境变量

## 📋 故障排除

### 如果程序仍然报告权限错误：

1. **检查环境**:
   ```bash
   # 确认在Citrix Hypervisor宿主机上（不是虚拟机内）
   cat /etc/xensource-inventory
   
   # 检查oxenstored服务
   pgrep -x oxenstored
   systemctl status oxenstored
   ```

2. **检查设备权限**:
   ```bash
   ls -la /dev/xen/
   # 应该看到privcmd, xenbus, xenbus_backend等设备
   ```

3. **手动修复权限**:
   ```bash
   sudo chmod 666 /dev/xen/privcmd
   sudo chmod 666 /dev/xen/xenbus
   sudo chmod 666 /dev/xen/xenbus_backend
   ```

4. **测试xe命令**:
   ```bash
   xe host-list
   xe vm-list
   ```

### 如果xe命令正常但程序仍失败：

这可能是libxenctrl库与Citrix Hypervisor版本的兼容性问题。请联系技术支持并提供：
- Citrix Hypervisor版本: `cat /etc/xensource-inventory`
- Xen版本: `xl info | grep xen_version`
- 程序详细错误日志

## 🎉 成功标志

程序成功运行时应该看到：
```
[INFO] XenServer Antivirus Host Service starting...
[INFO] Version: 1.0.0-citrix, Build: Citrix Hypervisor Compatible
[INFO] Detecting Xen environment...
[INFO] Detected Xen version: 4.13
[INFO] Initializing Xen interfaces...
[INFO] XC interface opened successfully for Citrix Hypervisor
[INFO] Host service initialized successfully
[INFO] VM monitor thread started
[INFO] Service started successfully, monitoring VMs...
[SUCCESS] Found X domains
```

## 📞 技术支持

如果遇到问题，请提供：
1. Citrix Hypervisor版本信息
2. 程序完整错误日志
3. `pgrep -x oxenstored`输出
4. `ls -la /dev/xen/`输出
5. `xe host-list`是否正常工作

---

**注意**: 此版本专门为Citrix Hypervisor 8.2.1环境优化，包含了针对oxenstored、设备权限和glibc兼容性的特殊处理。
