#include "../common/av_common.h"
#include <stdio.h>
#include <stdlib.h>
#include <getopt.h>
#include <signal.h>
#include <unistd.h>

/* 全局变量 */
static volatile int g_shutdown_requested = 0;

/* 信号处理函数 */
void signal_handler(int signum) {
    switch (signum) {
        case SIGINT:
        case SIGTERM:
            av_log(AV_LOG_INFO, "Received shutdown signal (%d), initiating graceful shutdown", signum);
            g_shutdown_requested = 1;
            break;
        default:
            av_log(AV_LOG_WARN, "Received unexpected signal: %d", signum);
            break;
    }
}

/* 打印使用帮助 */
void print_usage(const char* program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("XenServer Antivirus Linux Client (Simple Build)\n\n");
    printf("Options:\n");
    printf("  -h, --help           Show this help message\n");
    printf("  -v, --verbose        Enable verbose logging\n");
    printf("  -l, --log-level LEVEL Set log level (0=ERROR, 1=WARNING, 2=INFO, 3=DEBUG)\n");
    printf("  -f, --log-file FILE  Write logs to file instead of stdout\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s                   Run with default settings\n", program_name);
    printf("  %s -v -l 3           Run with verbose debug logging\n", program_name);
    printf("  %s -f /tmp/av_client.log  Run with file logging\n", program_name);
}

/* 模拟客户端功能 */
int simulate_client_functionality() {
    av_log(AV_LOG_INFO, "Starting XenServer Antivirus Linux Client simulation...");
    
    /* 模拟UUID获取 */
    av_log(AV_LOG_INFO, "Simulating VM UUID retrieval...");
    char mock_uuid[] = "12345678-1234-1234-1234-123456789abc";
    
    if (av_validate_uuid(mock_uuid) == AV_SUCCESS) {
        av_log(AV_LOG_INFO, "UUID validation successful: %s", mock_uuid);
    } else {
        av_log(AV_LOG_ERROR, "UUID validation failed");
        return AV_ERROR_INVALID_PARAM;
    }
    
    /* 模拟XenStore路径构建 */
    char xenstore_path[256];
    int ret = av_build_xenstore_path(mock_uuid, AV_XENSTORE_SHM_ID_KEY, xenstore_path, sizeof(xenstore_path));
    if (ret == AV_SUCCESS) {
        av_log(AV_LOG_INFO, "XenStore path built successfully: %s", xenstore_path);
    } else {
        av_log(AV_LOG_ERROR, "Failed to build XenStore path: %s", av_error_string(ret));
        return ret;
    }
    
    /* 模拟通信循环 */
    av_log(AV_LOG_INFO, "Starting communication simulation loop...");
    int cycle = 0;
    while (!g_shutdown_requested && cycle < 10) {
        av_log(AV_LOG_INFO, "Communication cycle %d: Sending SCAN_REQ...", cycle + 1);
        
        /* 模拟发送请求 */
        av_log(AV_LOG_DEBUG, "Simulating request: %s", AV_SCAN_REQUEST_CMD);
        
        /* 模拟等待响应 */
        sleep(1);
        av_log(AV_LOG_DEBUG, "Simulating response: %s", AV_SCAN_RESPONSE_CMD);
        
        av_log(AV_LOG_INFO, "Communication cycle %d completed successfully", cycle + 1);
        cycle++;
        
        /* 休眠2秒 */
        sleep(2);
    }
    
    if (g_shutdown_requested) {
        av_log(AV_LOG_INFO, "Communication loop interrupted by shutdown signal");
    } else {
        av_log(AV_LOG_INFO, "Communication simulation completed (%d cycles)", cycle);
    }
    
    return AV_SUCCESS;
}

/* 主函数 */
int main(int argc, char* argv[]) {
    int ret = AV_SUCCESS;
    int log_level = AV_LOG_INFO;
    char* log_file = NULL;
    
    /* 命令行参数解析 */
    static struct option long_options[] = {
        {"help",      no_argument,       0, 'h'},
        {"verbose",   no_argument,       0, 'v'},
        {"log-level", required_argument, 0, 'l'},
        {"log-file",  required_argument, 0, 'f'},
        {0, 0, 0, 0}
    };
    
    int option_index = 0;
    int c;
    
    while ((c = getopt_long(argc, argv, "hvl:f:", long_options, &option_index)) != -1) {
        switch (c) {
            case 'h':
                print_usage(argv[0]);
                return 0;
            case 'v':
                log_level = AV_LOG_DEBUG;
                break;
            case 'l':
                log_level = atoi(optarg);
                if (log_level < AV_LOG_ERROR || log_level > AV_LOG_DEBUG) {
                    fprintf(stderr, "Invalid log level: %s\n", optarg);
                    return 1;
                }
                break;
            case 'f':
                log_file = optarg;
                break;
            case '?':
                print_usage(argv[0]);
                return 1;
            default:
                break;
        }
    }
    
    /* 初始化日志系统 */
    av_log_init(log_level, log_file);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Linux Client starting...");
    av_log(AV_LOG_INFO, "Version: %s, Build: %s %s", AV_VERSION, __DATE__, __TIME__);
    av_log(AV_LOG_INFO, "Running in simulation mode (no Xen dependencies)");
    
    /* 设置信号处理 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGPIPE, SIG_IGN);  /* 忽略SIGPIPE信号 */
    
    /* 运行客户端功能模拟 */
    ret = simulate_client_functionality();
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Client simulation failed: %s", av_error_string(ret));
    }
    
    /* 清理资源 */
    av_log(AV_LOG_INFO, "XenServer Antivirus Linux Client shutdown complete");
    av_log_cleanup();
    
    return (ret == AV_SUCCESS) ? 0 : 1;
}