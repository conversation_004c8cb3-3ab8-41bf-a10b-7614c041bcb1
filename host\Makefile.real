# XenServer宿主机防病毒服务Makefile - 真实Xen环境版本

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE

# 检查是否有Xen库
XEN_LIBS_AVAILABLE := $(shell pkg-config --exists xencontrol xenstore 2>/dev/null && echo yes || echo no)

ifneq ($(XEN_LIBS_AVAILABLE),yes)
    $(error Xen libraries not found. Please install libxen-dev package)
endif

# 获取Xen库的编译和链接标志
XEN_CFLAGS := $(shell pkg-config --cflags xencontrol xenstore 2>/dev/null)
XEN_LDFLAGS := $(shell pkg-config --libs xencontrol xenstore 2>/dev/null)
CFLAGS += $(XEN_CFLAGS)
LDFLAGS = $(XEN_LDFLAGS) -lxengnttab -lpthread

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj_real
BINDIR = bin

# 源文件和目标文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
HOST_SOURCES = main_real.c
SOURCES = $(COMMON_SOURCES) $(HOST_SOURCES)
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/xenserver-antivirus-host-real

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I.

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Linking $@..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Build complete: $@"
	@echo "Built with real Xen libraries (version: $(shell pkg-config --modversion xencontrol))"

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $<..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning build files..."
	rm -rf $(OBJDIR) $(BINDIR)/xenserver-antivirus-host-real

# 检查依赖
check-deps:
	@echo "Checking dependencies..."
	@echo "✓ Xen libraries found:"
	@pkg-config --modversion xencontrol xenstore
	@echo "✓ Xen headers found at: /usr/include/xen"
	@echo "✓ Compilation flags:"
	@echo "  CFLAGS: $(CFLAGS)"
	@echo "  LDFLAGS: $(LDFLAGS)"

# 安装程序
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/xenserver-antivirus-host
	sudo chmod +x /usr/local/bin/xenserver-antivirus-host
	
	# 创建配置目录
	sudo mkdir -p /etc/xenserver-antivirus
	
	# 创建日志目录
	sudo mkdir -p /var/log/xenserver-antivirus
	
	@echo "Installation complete"
	@echo "To run the service:"
	@echo "  sudo /usr/local/bin/xenserver-antivirus-host --help"

# 运行程序（需要root权限）
run: $(TARGET)
	@echo "Running $(TARGET) (requires root privileges)..."
	sudo $(TARGET)

# 测试运行
test: $(TARGET)
	@echo "Testing $(TARGET)..."
	sudo $(TARGET) --help
	@echo "Test completed"

.PHONY: all clean directories check-deps install run test