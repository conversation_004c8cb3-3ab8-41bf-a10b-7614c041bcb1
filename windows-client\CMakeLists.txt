# Windows虚拟机防病毒客户端CMake配置

cmake_minimum_required(VERSION 3.10)
project(XenServerAntivirusClient)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4 /EHsc")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi /DDEBUG")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -DDEBUG")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
endif()

# 包含目录
include_directories(../common)

# 查找Windows SDK
find_package(PkgConfig QUIET)

# 源文件
set(COMMON_SOURCES
    ../common/av_common.c
)

set(CLIENT_SOURCES
    av_windows_client.cpp
    av_service_base.cpp
    av_antivirus_service.cpp
    av_xenstore_monitor.cpp
    av_shared_memory_mapper.cpp
    main.cpp
)

# 创建可执行文件
add_executable(av_windows_client ${COMMON_SOURCES} ${CLIENT_SOURCES})

# 链接库
target_link_libraries(av_windows_client
    advapi32    # Windows服务API
    kernel32    # Windows内核API
    user32      # Windows用户API
    # xenbus      # Xen PV驱动库（需要安装Xen PV驱动）
)

# 设置输出目录
set_target_properties(av_windows_client PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/bin/Debug
    RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/bin/Release
)

# 安装配置
install(TARGETS av_windows_client
    RUNTIME DESTINATION bin
)

# 创建服务安装脚本
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/install_service.bat.in
    ${CMAKE_BINARY_DIR}/install_service.bat
    @ONLY
)

configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/uninstall_service.bat.in
    ${CMAKE_BINARY_DIR}/uninstall_service.bat
    @ONLY
)

# 自定义目标
add_custom_target(install-service
    COMMAND ${CMAKE_BINARY_DIR}/install_service.bat
    DEPENDS av_windows_client
    COMMENT "Installing Windows service"
)

add_custom_target(uninstall-service
    COMMAND ${CMAKE_BINARY_DIR}/uninstall_service.bat
    COMMENT "Uninstalling Windows service"
)

# 打包配置
set(CPACK_PACKAGE_NAME "XenServerAntivirusClient")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "XenServer Agentless Antivirus Client")
set(CPACK_PACKAGE_VENDOR "Your Company")
set(CPACK_GENERATOR "NSIS")

include(CPack)