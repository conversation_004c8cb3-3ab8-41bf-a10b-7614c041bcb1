#include "av_linux_client.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <time.h>
#include <sys/select.h>
#include <sys/time.h>

/* UUID获取函数 */
int av_get_vm_uuid(av_linux_client_t* client, char* uuid_buffer, size_t buffer_size) {
    if (!client || !uuid_buffer || buffer_size < 37) {
        av_log(AV_LOG_ERROR, "Invalid parameters for UUID retrieval");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 从XenStore读取虚拟机UUID */
    char* uuid_path = "/vm";
    char* uuid_value = xs_read(client->xs_handle, XBT_NULL, uuid_path, NULL);
    
    if (!uuid_value) {
        av_log(AV_LOG_ERROR, "Failed to read VM UUID from XenStore path: %s", uuid_path);
        return AV_ERROR_XENSTORE_READ;
    }

    /* 检查UUID格式并复制 */
    if (strlen(uuid_value) >= buffer_size) {
        av_log(AV_LOG_ERROR, "UUID buffer too small");
        free(uuid_value);
        return AV_ERROR_BUFFER_TOO_SMALL;
    }

    strncpy(uuid_buffer, uuid_value, buffer_size - 1);
    uuid_buffer[buffer_size - 1] = '\0';
    
    free(uuid_value);
    av_log(AV_LOG_INFO, "Successfully retrieved VM UUID: %s", uuid_buffer);
    
    return AV_SUCCESS;
}

/* XenStore监听线程函数 */
void* av_xenstore_monitor_thread(void* arg) {
    av_linux_client_t* client = (av_linux_client_t*)arg;
    fd_set readfds;
    int xs_fd;
    struct timeval timeout;
    char** watch_paths;
    unsigned int num_paths;
    
    if (!client || !client->xs_handle) {
        av_log(AV_LOG_ERROR, "Invalid client context in monitor thread");
        return NULL;
    }

    xs_fd = xs_fileno(client->xs_handle);
    if (xs_fd < 0) {
        av_log(AV_LOG_ERROR, "Failed to get XenStore file descriptor");
        return NULL;
    }

    av_log(AV_LOG_INFO, "XenStore monitor thread started");

    while (!client->shutdown_requested) {
        FD_ZERO(&readfds);
        FD_SET(xs_fd, &readfds);
        
        timeout.tv_sec = 1;  /* 1秒超时 */
        timeout.tv_usec = 0;

        int ret = select(xs_fd + 1, &readfds, NULL, NULL, &timeout);
        
        if (ret < 0) {
            if (errno == EINTR) {
                continue;  /* 被信号中断，继续监听 */
            }
            av_log(AV_LOG_ERROR, "select() failed in XenStore monitor: %s", strerror(errno));
            break;
        }
        
        if (ret == 0) {
            continue;  /* 超时，继续监听 */
        }

        if (FD_ISSET(xs_fd, &readfds)) {
            /* 读取XenStore事件 */
            watch_paths = xs_read_watch(client->xs_handle, &num_paths);
            if (watch_paths && num_paths >= 2) {
                char* path = watch_paths[XS_WATCH_PATH];
                char* token = watch_paths[XS_WATCH_TOKEN];
                
                av_log(AV_LOG_DEBUG, "XenStore watch triggered: path=%s, token=%s", path, token);
                
                /* 检查是否是共享内存ID路径 */
                if (strstr(path, "/antivirus/shm_id")) {
                    char* value = xs_read(client->xs_handle, XBT_NULL, path, NULL);
                    if (value) {
                        av_xenstore_shm_callback(path, value, client);
                        free(value);
                    }
                }
                
                free(watch_paths);
            }
        }
    }

    av_log(AV_LOG_INFO, "XenStore monitor thread exiting");
    return NULL;
}

/* XenStore共享内存回调函数 */
void av_xenstore_shm_callback(const char* path, const char* value, void* user_data) {
    av_linux_client_t* client = (av_linux_client_t*)user_data;
    
    if (!client || !path || !value) {
        av_log(AV_LOG_ERROR, "Invalid parameters in XenStore callback");
        return;
    }

    av_log(AV_LOG_INFO, "Received shared memory ID from XenStore: %s", value);
    
    pthread_mutex_lock(&client->context_mutex);
    
    /* 解析共享内存ID */
    client->context.shm_id = (uint32_t)strtoul(value, NULL, 10);
    if (client->context.shm_id == 0) {
        av_log(AV_LOG_ERROR, "Invalid shared memory ID: %s", value);
        pthread_mutex_unlock(&client->context_mutex);
        return;
    }
    
    /* 标记共享内存就绪 */
    client->shm_ready = 1;
    pthread_cond_signal(&client->shm_ready_cond);
    
    pthread_mutex_unlock(&client->context_mutex);
    
    av_log(AV_LOG_INFO, "Shared memory ID updated: %u", client->context.shm_id);
}

/* XenStore路径监听函数 */
int av_monitor_xenstore_path(av_linux_client_t* client, const char* path, xenstore_callback_t callback) {
    if (!client || !client->xs_handle || !path) {
        av_log(AV_LOG_ERROR, "Invalid parameters for XenStore monitoring");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 设置XenStore监听 */
    if (!xs_watch(client->xs_handle, path, "antivirus_watch")) {
        av_log(AV_LOG_ERROR, "Failed to set XenStore watch on path: %s", path);
        return AV_ERROR_XENSTORE_WATCH;
    }

    av_log(AV_LOG_INFO, "Successfully set XenStore watch on path: %s", path);
    return AV_SUCCESS;
}/* 内存映射函数
 */
void* av_map_grant_memory(av_linux_client_t* client, grant_ref_t grant_ref, size_t size) {
    if (!client || !client->xc_handle || grant_ref == 0 || size == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for grant memory mapping");
        return NULL;
    }

    /* 使用libxenctrl映射grant页面 */
    void* addr = xc_map_foreign_range(client->xc_handle, 0, size, PROT_READ | PROT_WRITE, grant_ref);
    
    if (addr == MAP_FAILED || addr == NULL) {
        av_log(AV_LOG_ERROR, "Failed to map grant memory: grant_ref=%u, size=%zu", grant_ref, size);
        return NULL;
    }

    av_log(AV_LOG_INFO, "Successfully mapped grant memory: grant_ref=%u, addr=%p, size=%zu", 
           grant_ref, addr, size);
    
    return addr;
}

/* 内存解映射函数 */
int av_unmap_grant_memory(av_linux_client_t* client, void* addr, size_t size) {
    if (!client || !addr || size == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for grant memory unmapping");
        return AV_ERROR_INVALID_PARAM;
    }

    if (munmap(addr, size) != 0) {
        av_log(AV_LOG_ERROR, "Failed to unmap grant memory: addr=%p, size=%zu, error=%s", 
               addr, size, strerror(errno));
        return AV_ERROR_MEMORY_UNMAP;
    }

    av_log(AV_LOG_INFO, "Successfully unmapped grant memory: addr=%p, size=%zu", addr, size);
    return AV_SUCCESS;
}

/* 发送扫描请求函数 */
int av_send_scan_request(av_linux_client_t* client, void* shm_addr) {
    if (!client || !shm_addr) {
        av_log(AV_LOG_ERROR, "Invalid parameters for scan request");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 写入扫描请求命令 */
    memcpy(shm_addr, AV_SCAN_REQUEST_CMD, strlen(AV_SCAN_REQUEST_CMD));
    
    /* 确保数据写入内存 */
    __sync_synchronize();
    
    av_log(AV_LOG_INFO, "Scan request sent: %s", AV_SCAN_REQUEST_CMD);
    return AV_SUCCESS;
}

/* 等待扫描响应函数 */
int av_wait_for_scan_response(av_linux_client_t* client, void* shm_addr, char* response, int timeout_ms) {
    if (!client || !shm_addr || !response || timeout_ms <= 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for scan response wait");
        return AV_ERROR_INVALID_PARAM;
    }

    struct timeval start_time, current_time;
    gettimeofday(&start_time, NULL);
    
    char buffer[AV_COMMAND_SIZE];
    
    while (1) {
        /* 读取共享内存中的响应 */
        memcpy(buffer, shm_addr, AV_COMMAND_SIZE - 1);
        buffer[AV_COMMAND_SIZE - 1] = '\0';
        
        /* 检查是否收到响应 */
        if (strncmp(buffer, AV_SCAN_RESPONSE_CMD, strlen(AV_SCAN_RESPONSE_CMD)) == 0) {
            strncpy(response, buffer, AV_COMMAND_SIZE - 1);
            response[AV_COMMAND_SIZE - 1] = '\0';
            av_log(AV_LOG_INFO, "Received scan response: %s", response);
            return AV_SUCCESS;
        }
        
        /* 检查超时 */
        gettimeofday(&current_time, NULL);
        long elapsed_ms = (current_time.tv_sec - start_time.tv_sec) * 1000 + 
                         (current_time.tv_usec - start_time.tv_usec) / 1000;
        
        if (elapsed_ms >= timeout_ms) {
            av_log(AV_LOG_WARN, "Timeout waiting for scan response after %d ms", timeout_ms);
            return AV_ERROR_TIMEOUT;
        }
        
        /* 使用可中断的短暂休眠 */
        struct timespec sleep_time = {0, 1000000};  /* 1ms */
        struct timespec remaining;
        
        if (nanosleep(&sleep_time, &remaining) == -1) {
            if (errno == EINTR) {
                /* 被信号中断，检查是否需要退出 */
                if (client->shutdown_requested) {
                    return AV_ERROR_TIMEOUT;  /* 提前退出 */
                }
            }
        }
    }
}/* 
客户端初始化函数 */
int av_linux_client_init(av_linux_client_t* client) {
    if (!client) {
        av_log(AV_LOG_ERROR, "Invalid client parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    memset(client, 0, sizeof(av_linux_client_t));
    
    /* 初始化libxenctrl */
    client->xc_handle = xc_interface_open(NULL, NULL, 0);
    if (!client->xc_handle) {
        av_log(AV_LOG_ERROR, "Failed to open xenctrl interface");
        return AV_ERROR_XC_INTERFACE;
    }

    /* 初始化XenStore连接 */
    client->xs_handle = xs_open(0);
    if (!client->xs_handle) {
        av_log(AV_LOG_ERROR, "Failed to open XenStore connection");
        xc_interface_close(client->xc_handle);
        return AV_ERROR_XENSTORE_OPEN;
    }

    /* 初始化互斥锁和条件变量 */
    if (pthread_mutex_init(&client->context_mutex, NULL) != 0) {
        av_log(AV_LOG_ERROR, "Failed to initialize context mutex");
        xs_close(client->xs_handle);
        xc_interface_close(client->xc_handle);
        return AV_ERROR_MUTEX_INIT;
    }

    if (pthread_cond_init(&client->shm_ready_cond, NULL) != 0) {
        av_log(AV_LOG_ERROR, "Failed to initialize condition variable");
        pthread_mutex_destroy(&client->context_mutex);
        xs_close(client->xs_handle);
        xc_interface_close(client->xc_handle);
        return AV_ERROR_COND_INIT;
    }

    /* 初始化客户端上下文 */
    client->context.client_type = AV_CLIENT_LINUX;
    client->shutdown_requested = 0;
    client->shm_ready = 0;

    av_log(AV_LOG_INFO, "Linux client initialized successfully");
    return AV_SUCCESS;
}

/* 客户端启动函数 */
int av_linux_client_start(av_linux_client_t* client) {
    if (!client) {
        av_log(AV_LOG_ERROR, "Invalid client parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 获取虚拟机UUID */
    int ret = av_get_vm_uuid(client, client->context.vm_uuid, sizeof(client->context.vm_uuid));
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to get VM UUID");
        return ret;
    }

    /* 构建XenStore监听路径 */
    char xenstore_path[256];
    snprintf(xenstore_path, sizeof(xenstore_path), "/local/domain/0/antivirus/%s/shm_id", 
             client->context.vm_uuid);

    /* 设置XenStore监听 */
    ret = av_monitor_xenstore_path(client, xenstore_path, av_xenstore_shm_callback);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to set up XenStore monitoring");
        return ret;
    }

    /* 启动XenStore监听线程 */
    if (pthread_create(&client->monitor_thread, NULL, av_xenstore_monitor_thread, client) != 0) {
        av_log(AV_LOG_ERROR, "Failed to create XenStore monitor thread");
        return AV_ERROR_THREAD_CREATE;
    }

    av_log(AV_LOG_INFO, "Linux client started successfully");
    return AV_SUCCESS;
}

/* 客户端停止函数 */
int av_linux_client_stop(av_linux_client_t* client) {
    if (!client) {
        av_log(AV_LOG_ERROR, "Invalid client parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 设置关闭标志 */
    client->shutdown_requested = 1;

    /* 通知等待的条件变量 */
    pthread_mutex_lock(&client->context_mutex);
    pthread_cond_broadcast(&client->shm_ready_cond);
    pthread_mutex_unlock(&client->context_mutex);

    /* 等待监听线程结束 */
    if (client->monitor_thread != 0) {
        pthread_join(client->monitor_thread, NULL);
        client->monitor_thread = 0;
    }

    av_log(AV_LOG_INFO, "Linux client stopped successfully");
    return AV_SUCCESS;
}

/* 客户端清理函数 */
void av_linux_client_cleanup(av_linux_client_t* client) {
    if (!client) {
        return;
    }

    /* 停止客户端 */
    av_linux_client_stop(client);

    /* 清理XenStore连接 */
    if (client->xs_handle) {
        xs_close(client->xs_handle);
        client->xs_handle = NULL;
    }

    /* 清理libxenctrl接口 */
    if (client->xc_handle) {
        xc_interface_close(client->xc_handle);
        client->xc_handle = NULL;
    }

    /* 清理同步对象 */
    pthread_cond_destroy(&client->shm_ready_cond);
    pthread_mutex_destroy(&client->context_mutex);

    av_log(AV_LOG_INFO, "Linux client cleanup completed");
}/* 客户端通
信流程函数 */
int av_linux_client_run_communication(av_linux_client_t* client) {
    if (!client) {
        av_log(AV_LOG_ERROR, "Invalid client parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    void* shm_addr = NULL;
    char response[AV_COMMAND_SIZE];
    int ret = AV_SUCCESS;

    av_log(AV_LOG_INFO, "Starting communication flow for VM: %s", client->context.vm_uuid);

    /* 等待共享内存就绪 */
    pthread_mutex_lock(&client->context_mutex);
    while (!client->shm_ready && !client->shutdown_requested) {
        av_log(AV_LOG_INFO, "Waiting for shared memory to be ready...");
        struct timespec timeout;
        timeout.tv_sec = time(NULL) + 1;  /* 1秒超时 */
        timeout.tv_nsec = 0;
        pthread_cond_timedwait(&client->shm_ready_cond, &client->context_mutex, &timeout);
    }
    
    if (client->shutdown_requested) {
        pthread_mutex_unlock(&client->context_mutex);
        av_log(AV_LOG_INFO, "Shutdown requested, exiting communication flow");
        return AV_SUCCESS;
    }

    uint32_t shm_id = client->context.shm_id;
    pthread_mutex_unlock(&client->context_mutex);

    /* 映射共享内存 */
    shm_addr = av_map_grant_memory(client, shm_id, AV_SHARED_MEMORY_SIZE);
    if (!shm_addr) {
        av_log(AV_LOG_ERROR, "Failed to map shared memory");
        return AV_ERROR_MEMORY_MAP;
    }

    av_log(AV_LOG_INFO, "Shared memory mapped successfully, starting communication loop");

    /* 通信循环 */
    while (!client->shutdown_requested) {
        /* 发送扫描请求 */
        ret = av_send_scan_request(client, shm_addr);
        if (ret != AV_SUCCESS) {
            av_log(AV_LOG_ERROR, "Failed to send scan request");
            break;
        }

        /* 等待扫描响应 */
        ret = av_wait_for_scan_response(client, shm_addr, response, AV_RESPONSE_TIMEOUT_MS);
        if (ret == AV_SUCCESS) {
            av_log(AV_LOG_INFO, "Communication cycle completed successfully");
        } else if (ret == AV_ERROR_TIMEOUT) {
            av_log(AV_LOG_WARN, "Response timeout, retrying...");
            continue;
        } else {
            av_log(AV_LOG_ERROR, "Failed to receive scan response");
            break;
        }

        /* 使用可中断的sleep */
        struct timespec sleep_time = {5, 0};
        struct timespec remaining;
        
        if (nanosleep(&sleep_time, &remaining) == -1) {
            if (errno == EINTR) {
                av_log(AV_LOG_DEBUG, "Communication sleep interrupted by signal");
                /* 检查是否需要退出 */
                if (client->shutdown_requested) {
                    break;
                }
            }
        }
    }

    /* 清理共享内存映射 */
    if (shm_addr) {
        av_unmap_grant_memory(client, shm_addr, AV_SHARED_MEMORY_SIZE);
    }

    av_log(AV_LOG_INFO, "Communication flow completed");
    return ret;
}