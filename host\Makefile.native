# Makefile for XenServer Antivirus Host Service - Native Xen 4.13 Version
# Uses command-line tools instead of direct API calls

CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE
INCLUDES = -I../common -I.

# 最小库依赖 - 只需要pthread
LIBS = -lpthread

# 目录
OBJDIR = obj_native
BINDIR = bin
COMMONDIR = ../common

# 源文件
MAIN_SOURCE = main_xen413_native.c
COMMON_SOURCE = $(COMMONDIR)/av_common.c
SOURCES = $(MAIN_SOURCE) $(COMMON_SOURCE)

# 目标文件
MAIN_OBJ = $(OBJDIR)/main_xen413_native.o
COMMON_OBJ = $(OBJDIR)/av_common.o
OBJECTS = $(MAIN_OBJ) $(COMMON_OBJ)

# 可执行文件
TARGET = $(BINDIR)/xenserver-antivirus-native

# 默认目标
all: $(TARGET)

# 创建目录
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(BINDIR):
	mkdir -p $(BINDIR)

# 编译主程序
$(MAIN_OBJ): $(MAIN_SOURCE) | $(OBJDIR)
	@echo "编译 $(MAIN_SOURCE) (原生模式)..."
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译通用模块
$(COMMON_OBJ): $(COMMON_SOURCE) | $(OBJDIR)
	@echo "编译 $(COMMON_SOURCE)..."
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 链接可执行文件
$(TARGET): $(OBJECTS) | $(BINDIR)
	@echo "链接 $(TARGET) (原生模式)..."
	$(CC) $(OBJECTS) -o $@ $(LIBS)
	@echo "构建完成: $@"
	@echo "原生Xen 4.13.x版本 - 使用命令行工具"
	@file $@
	@echo ""

# 清理
clean:
	@echo "清理原生版本构建文件..."
	rm -rf $(OBJDIR) $(TARGET)

# 测试
test: $(TARGET)
	@echo "运行环境测试..."
	./$(TARGET) --test

# 显示构建信息
show-info:
	@echo "=== 原生版本构建信息 ==="
	@echo "源文件: $(MAIN_SOURCE)"
	@echo "目标: $(TARGET)"
	@echo "编译器: $(CC)"
	@echo "标志: $(CFLAGS)"
	@echo "库: $(LIBS)"
	@echo "特性: 使用命令行工具，避免直接API调用"
	@echo "兼容性: Xen 4.13.x 原生支持"
	@echo "=========================="

.PHONY: all clean test show-info
