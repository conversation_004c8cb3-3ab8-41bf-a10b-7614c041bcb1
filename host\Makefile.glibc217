# XenServer宿主机防病毒服务Makefile - glibc 2.17兼容版本
# 专门针对Citrix Hypervisor 8.2.1 (glibc 2.17)优化
# 从WSL编译，在Citrix Hypervisor上运行

CC = gcc
# 针对glibc 2.17的特殊编译选项
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE -D_POSIX_C_SOURCE=200809L
INCLUDES = -I../common -I.

# 针对老版本glibc的兼容性设置
COMPAT_CFLAGS = -DTARGET_GLIBC_217 -D__USE_MISC -D__USE_GNU -DCROSS_COMPILE

# 静态链接选项（避免glibc版本冲突）
STATIC_CFLAGS = -static -static-libgcc

# WSL环境的Xen库路径
XEN_LIB_PATH = /usr/lib/x86_64-linux-gnu
XEN_INCLUDE_PATH = /usr/include

# 静态链接库（完整依赖，按正确顺序）
STATIC_LIBS = -lxenctrl -lxenstore -lxengnttab -lxencall -lxentoollog \
              -lxenforeignmemory -lxendevicemodel -lxentoolcore -lpthread -lrt -ldl

# 目录
OBJDIR = obj_glibc217
BINDIR = bin
DEPLOYDIR = deploy_glibc217
COMMONDIR = ../common

# 源文件
MAIN_SOURCE = main_cross_compile.c
MAIN_CITRIX_SOURCE = main_citrix_compatible.c
MAIN_COMMAND_SOURCE = main_command_based.c
COMMON_SOURCE = $(COMMONDIR)/av_common.c
SOURCES = $(MAIN_SOURCE) $(COMMON_SOURCE)
CITRIX_SOURCES = $(MAIN_CITRIX_SOURCE) $(COMMON_SOURCE)
COMMAND_SOURCES = $(MAIN_COMMAND_SOURCE) $(COMMON_SOURCE)

# 目标文件
MAIN_OBJ = $(OBJDIR)/main_cross_compile.o
MAIN_CITRIX_OBJ = $(OBJDIR)/main_citrix_compatible.o
MAIN_COMMAND_OBJ = $(OBJDIR)/main_command_based.o
COMMON_OBJ = $(OBJDIR)/av_common.o
OBJECTS = $(MAIN_OBJ) $(COMMON_OBJ)
CITRIX_OBJECTS = $(MAIN_CITRIX_OBJ) $(COMMON_OBJ)
COMMAND_OBJECTS = $(MAIN_COMMAND_OBJ) $(COMMON_OBJ)

# 可执行文件
TARGET_STATIC = $(BINDIR)/xenserver-antivirus-glibc217-static
TARGET_CITRIX = $(BINDIR)/xenserver-antivirus-citrix-static
TARGET_COMMAND = $(BINDIR)/xenserver-antivirus-command-static

# 默认目标
all: check-env $(TARGET_STATIC) $(TARGET_CITRIX) $(TARGET_COMMAND) package

# Citrix专用目标
citrix: check-env $(TARGET_CITRIX) package-citrix

# 基于命令的目标（推荐）
command: check-env $(TARGET_COMMAND) package-command

# 检查编译环境
check-env:
	@echo "=== 检查glibc 2.17兼容编译环境 ==="
	@echo "编译器: $(CC)"
	@$(CC) --version | head -1
	@echo ""
	@echo "当前glibc版本:"
	@ldd --version | head -1 || echo "无法检测glibc版本"
	@echo ""
	@echo "目标环境: Citrix Hypervisor 8.2.1 (glibc 2.17)"
	@echo ""
	@echo "Xen库检查:"
	@ls -la $(XEN_LIB_PATH)/libxen*.so 2>/dev/null | head -3 || echo "Xen库未找到"
	@echo "环境检查完成"
	@echo ""

# 创建目录
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(BINDIR):
	mkdir -p $(BINDIR)

$(DEPLOYDIR):
	mkdir -p $(DEPLOYDIR)

# 编译主程序（使用兼容性选项）
$(MAIN_OBJ): $(MAIN_SOURCE) | $(OBJDIR)
	@echo "编译 $(MAIN_SOURCE) (glibc 2.17兼容)..."
	$(CC) $(CFLAGS) $(COMPAT_CFLAGS) $(INCLUDES) -c $< -o $@

# 编译通用模块
$(COMMON_OBJ): $(COMMON_SOURCE) | $(OBJDIR)
	@echo "编译 $(COMMON_SOURCE) (glibc 2.17兼容)..."
	$(CC) $(CFLAGS) $(COMPAT_CFLAGS) $(INCLUDES) -c $< -o $@

# 编译Citrix兼容主程序
$(MAIN_CITRIX_OBJ): $(MAIN_CITRIX_SOURCE) | $(OBJDIR)
	@echo "编译 $(MAIN_CITRIX_SOURCE) (Citrix Hypervisor兼容)..."
	$(CC) $(CFLAGS) $(COMPAT_CFLAGS) -DCITRIX_HYPERVISOR $(INCLUDES) -c $< -o $@

# 编译基于命令的主程序
$(MAIN_COMMAND_OBJ): $(MAIN_COMMAND_SOURCE) | $(OBJDIR)
	@echo "编译 $(MAIN_COMMAND_SOURCE) (基于系统命令)..."
	$(CC) $(CFLAGS) $(COMPAT_CFLAGS) -DCOMMAND_BASED $(INCLUDES) -c $< -o $@

# 链接静态版本（针对glibc 2.17优化）
$(TARGET_STATIC): $(OBJECTS) | $(BINDIR)
	@echo "链接 $(TARGET_STATIC) (glibc 2.17兼容静态版本)..."
	$(CC) $(STATIC_CFLAGS) $(OBJECTS) -o $@ $(STATIC_LIBS)
	@echo "构建完成: $@"
	@echo "目标: Citrix Hypervisor 8.2.1 (glibc 2.17)"
	@file $@ 2>/dev/null || echo "文件信息不可用"
	@echo ""

# 链接Citrix兼容版本
$(TARGET_CITRIX): $(CITRIX_OBJECTS) | $(BINDIR)
	@echo "链接 $(TARGET_CITRIX) (Citrix Hypervisor专用版本)..."
	$(CC) $(STATIC_CFLAGS) $(CITRIX_OBJECTS) -o $@ $(STATIC_LIBS)
	@echo "构建完成: $@"
	@echo "目标: Citrix Hypervisor 8.2.1 (专用优化)"
	@file $@ 2>/dev/null || echo "文件信息不可用"
	@echo ""

# 链接基于命令的版本（无需Xen库）
$(TARGET_COMMAND): $(COMMAND_OBJECTS) | $(BINDIR)
	@echo "链接 $(TARGET_COMMAND) (基于系统命令版本)..."
	$(CC) $(STATIC_CFLAGS) $(COMMAND_OBJECTS) -o $@ -lpthread -lrt -ldl
	@echo "构建完成: $@"
	@echo "目标: 通用版本 (使用xe/xl命令，无需libxenctrl)"
	@file $@ 2>/dev/null || echo "文件信息不可用"
	@echo ""

# 打包部署文件
package: $(TARGET_STATIC) | $(DEPLOYDIR)
	@echo "=== 创建glibc 2.17兼容部署包 ==="
	@cp $(TARGET_STATIC) $(DEPLOYDIR)/
	@cp ../xen_permission_fix.sh $(DEPLOYDIR)/ 2>/dev/null || echo "权限修复脚本未找到，跳过"
	@cp ../quick_xen_fix.sh $(DEPLOYDIR)/ 2>/dev/null || echo "快速修复脚本未找到，跳过"
	@chmod +x $(DEPLOYDIR)/*.sh 2>/dev/null || true
	@echo "#!/bin/bash" > $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "# XenServer防病毒部署脚本 - glibc 2.17兼容版本" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "# 目标: Citrix Hypervisor 8.2.1" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "# 生成时间: $$(date)" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "echo '=== XenServer防病毒部署 (glibc 2.17兼容) ==='" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "echo '检查目标环境...'" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "# 检查是否以root运行" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "if [ \$$(id -u) -ne 0 ]; then" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "    echo 'ERROR: 必须以root权限运行'" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "    exit 1" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "fi" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "# 检查glibc版本" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "GLIBC_VERSION=\$$(ldd --version | head -1 | grep -o '[0-9]\\+\\.[0-9]\\+' | head -1)" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "echo \"检测到glibc版本: \$$GLIBC_VERSION\"" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "if [ \"\$$GLIBC_VERSION\" != \"2.17\" ]; then" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "    echo \"WARNING: 此程序专为glibc 2.17编译，当前版本: \$$GLIBC_VERSION\"" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "    echo \"如果运行出现问题，请使用对应版本的程序\"" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "fi" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "# 检查Xen环境" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "if ! xl info >/dev/null 2>&1; then" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "    echo 'ERROR: 未检测到Xen环境'" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "    echo '请确保在Citrix Hypervisor宿主机上运行'" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "    exit 1" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "fi" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "echo '安装XenServer防病毒宿主机服务...'" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "install -m 755 xenserver-antivirus-glibc217-static /usr/local/bin/" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "echo '测试安装...'" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "/usr/local/bin/xenserver-antivirus-glibc217-static --test" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "echo '安装完成！'" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "echo '使用方法: /usr/local/bin/xenserver-antivirus-glibc217-static [选项]'" >> $(DEPLOYDIR)/deploy_glibc217.sh
	@chmod +x $(DEPLOYDIR)/deploy_glibc217.sh
	@echo "# XenServer防病毒宿主机服务 - glibc 2.17兼容版本" > $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "## 目标环境" >> $(DEPLOYDIR)/README.md
	@echo "- Citrix Hypervisor 8.2.1" >> $(DEPLOYDIR)/README.md
	@echo "- glibc 2.17" >> $(DEPLOYDIR)/README.md
	@echo "- Xen 4.13.x - 4.17.x" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "## 文件说明" >> $(DEPLOYDIR)/README.md
	@echo "- \`xenserver-antivirus-glibc217-static\`: 静态链接版本（推荐）" >> $(DEPLOYDIR)/README.md
	@echo "- \`deploy_glibc217.sh\`: 自动部署脚本" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "## 部署方法" >> $(DEPLOYDIR)/README.md
	@echo "1. 将此目录复制到Citrix Hypervisor宿主机" >> $(DEPLOYDIR)/README.md
	@echo "2. 运行: \`sudo ./deploy_glibc217.sh\`" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "## 手动安装" >> $(DEPLOYDIR)/README.md
	@echo "\`\`\`bash" >> $(DEPLOYDIR)/README.md
	@echo "# 测试环境" >> $(DEPLOYDIR)/README.md
	@echo "./xenserver-antivirus-glibc217-static --test" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "# 安装" >> $(DEPLOYDIR)/README.md
	@echo "sudo install -m 755 xenserver-antivirus-glibc217-static /usr/local/bin/" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "# 运行" >> $(DEPLOYDIR)/README.md
	@echo "sudo /usr/local/bin/xenserver-antivirus-glibc217-static -v" >> $(DEPLOYDIR)/README.md
	@echo "\`\`\`" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "## 兼容性说明" >> $(DEPLOYDIR)/README.md
	@echo "- 编译环境: WSL (glibc 2.31+)" >> $(DEPLOYDIR)/README.md
	@echo "- 目标环境: Citrix Hypervisor 8.2.1 (glibc 2.17)" >> $(DEPLOYDIR)/README.md
	@echo "- 构建时间: $$(date)" >> $(DEPLOYDIR)/README.md
	@echo "- 特殊优化: 静态链接，避免glibc版本冲突" >> $(DEPLOYDIR)/README.md
	@ls -la $(DEPLOYDIR)/
	@echo "✅ glibc 2.17兼容部署包已准备就绪: $(DEPLOYDIR)/"
	@echo ""

# 清理
clean:
	@echo "清理glibc 2.17兼容构建文件..."
	rm -rf $(OBJDIR) $(BINDIR)/xenserver-antivirus-glibc217* $(DEPLOYDIR)

# 快速测试编译
test-compile: $(MAIN_OBJ) $(COMMON_OBJ)
	@echo "✅ glibc 2.17兼容编译测试成功"
	@echo "目标文件:"
	@ls -la $(OBJDIR)/

# 显示构建信息
show-info:
	@echo "=== glibc 2.17兼容构建信息 ==="
	@echo "源文件: $(MAIN_SOURCE)"
	@echo "目标文件: $(TARGET_STATIC)"
	@echo "编译器: $(CC)"
	@echo "编译选项: $(CFLAGS) $(COMPAT_CFLAGS)"
	@echo "链接选项: $(STATIC_CFLAGS)"
	@echo "库文件: $(STATIC_LIBS)"
	@echo "目标环境: Citrix Hypervisor 8.2.1 (glibc 2.17)"
	@echo "部署目录: $(DEPLOYDIR)"
	@echo "============================================"

# 创建压缩包
archive: package
	@echo "创建glibc 2.17兼容部署压缩包..."
	@tar -czf xenserver-antivirus-glibc217-$$(date +%Y%m%d-%H%M).tar.gz -C $(DEPLOYDIR) .
	@echo "✅ 压缩包已创建: xenserver-antivirus-glibc217-$$(date +%Y%m%d-%H%M).tar.gz"

# 创建Citrix专用部署包
package-citrix: $(TARGET_CITRIX)
	@echo "=== 创建Citrix Hypervisor专用部署包 ==="
	mkdir -p deploy_citrix
	@cp $(TARGET_CITRIX) deploy_citrix/
	@cp ../citrix_hypervisor_fix.sh deploy_citrix/ 2>/dev/null || echo "citrix_hypervisor_fix.sh未找到，跳过"
	@cp ../citrix_xenstore_fix.sh deploy_citrix/ 2>/dev/null || echo "citrix_xenstore_fix.sh未找到，跳过"
	@cp ../fix_xen_device_permissions.sh deploy_citrix/ 2>/dev/null || echo "fix_xen_device_permissions.sh未找到，跳过"
	@chmod +x deploy_citrix/*.sh 2>/dev/null || true
	@echo "#!/bin/bash" > deploy_citrix/deploy_citrix.sh
	@echo "# XenServer防病毒部署脚本 - Citrix Hypervisor专用版本" >> deploy_citrix/deploy_citrix.sh
	@echo "echo '=== XenServer防病毒部署 (Citrix Hypervisor专用) ==='" >> deploy_citrix/deploy_citrix.sh
	@echo "echo '安装XenServer防病毒宿主机服务 (Citrix专用版本)...'" >> deploy_citrix/deploy_citrix.sh
	@echo "install -m 755 xenserver-antivirus-citrix-static /usr/local/bin/" >> deploy_citrix/deploy_citrix.sh
	@echo "echo '安装完成！'" >> deploy_citrix/deploy_citrix.sh
	@echo "echo '使用方法: sudo /usr/local/bin/xenserver-antivirus-citrix-static -v'" >> deploy_citrix/deploy_citrix.sh
	@chmod +x deploy_citrix/deploy_citrix.sh
	@echo "Citrix专用部署包创建完成: deploy_citrix/"
	@ls -la deploy_citrix/

# 创建基于命令的部署包（推荐）
package-command: $(TARGET_COMMAND)
	@echo "=== 创建基于命令的部署包（推荐） ==="
	mkdir -p deploy_command
	@cp $(TARGET_COMMAND) deploy_command/
	@echo "#!/bin/bash" > deploy_command/deploy_command.sh
	@echo "# XenServer防病毒部署脚本 - 基于命令版本（推荐）" >> deploy_command/deploy_command.sh
	@echo "echo '=== XenServer防病毒部署 (基于命令版本) ==='" >> deploy_command/deploy_command.sh
	@echo "echo '此版本使用xe/xl命令获取VM信息，避免libxenctrl权限问题'" >> deploy_command/deploy_command.sh
	@echo "echo '安装XenServer防病毒宿主机服务...'" >> deploy_command/deploy_command.sh
	@echo "install -m 755 xenserver-antivirus-command-static /usr/local/bin/" >> deploy_command/deploy_command.sh
	@echo "echo '安装完成！'" >> deploy_command/deploy_command.sh
	@echo "echo '使用方法: sudo /usr/local/bin/xenserver-antivirus-command-static -v'" >> deploy_command/deploy_command.sh
	@echo "echo '测试模式: sudo /usr/local/bin/xenserver-antivirus-command-static --test'" >> deploy_command/deploy_command.sh
	@chmod +x deploy_command/deploy_command.sh
	@echo "基于命令的部署包创建完成: deploy_command/"
	@ls -la deploy_command/

.PHONY: all citrix command check-env package package-citrix package-command clean test-compile show-info archive
