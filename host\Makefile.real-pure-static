# XenServer防病毒宿主机服务 - 纯真实静态编译版本
# 使用真实Xen API，包含信号处理和权限检查修复

CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE -static

# 检查是否有Xen库
XEN_LIBS_AVAILABLE := $(shell pkg-config --exists xencontrol xenstore 2>/dev/null && echo yes || echo no)

ifeq ($(XEN_LIBS_AVAILABLE),yes)
    # 获取Xen库的编译和链接标志（静态版本）
    XEN_CFLAGS := $(shell pkg-config --cflags xencontrol xenstore 2>/dev/null)
    XEN_LDFLAGS := $(shell pkg-config --libs --static xencontrol xenstore 2>/dev/null)
    
    # 如果pkg-config不支持--static，手动指定静态库
    ifeq ($(XEN_LDFLAGS),)
        XEN_LDFLAGS = -lxenctrl -lxenstore -lxengnttab -lxentoollog -lxencall -lxenevtchn -lxenforeignmemory -lxendevicemodel -lxentoolcore
    endif
    
    CFLAGS += $(XEN_CFLAGS)
    LDFLAGS = $(XEN_LDFLAGS) -lpthread -static
else
    $(error Xen libraries not found. This version requires real Xen libraries. Please install libxen-dev package)
endif

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj_real_pure_static
BINDIR = bin

# 源文件和目标文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
HOST_SOURCES = main_real_pure.c
SOURCES = $(COMMON_SOURCES) $(HOST_SOURCES)
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/xenserver-antivirus-host-real-pure-static

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I.

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Linking pure real static executable..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Pure real static build completed: $(TARGET)"
	@echo "Built with real Xen libraries (version: $(shell pkg-config --modversion xencontrol))"
	@echo "Binary size: $$(ls -lh $(TARGET) | awk '{print $$5}')"
	@echo "Dependencies: $$(ldd $(TARGET) 2>/dev/null || echo 'Static binary - no dependencies')"

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $< (pure real version)..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning up pure real version..."
	rm -rf $(OBJDIR) $(BINDIR)/xenserver-antivirus-host-real-pure-static

# 检查依赖
check-deps:
	@echo "Checking dependencies for pure real build..."
	@echo "✓ Xen libraries found:"
	@pkg-config --modversion xencontrol xenstore
	@echo "✓ Xen headers found at: /usr/include/xen"
	@echo "✓ Static libraries check:"
	@find /usr/lib* -name "libxenctrl.a" 2>/dev/null | head -1 || echo "⚠ libxenctrl.a not found"
	@find /usr/lib* -name "libxenstore.a" 2>/dev/null | head -1 || echo "⚠ libxenstore.a not found"
	@echo "✓ Compilation flags:"
	@echo "  CFLAGS: $(CFLAGS)"
	@echo "  LDFLAGS: $(LDFLAGS)"

# 安装
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/xenserver-antivirus-host-pure
	sudo chmod +x /usr/local/bin/xenserver-antivirus-host-pure
	@echo "Installed to /usr/local/bin/xenserver-antivirus-host-pure"

# 测试编译
test: $(TARGET)
	@echo "Testing pure real static binary..."
	@echo "File info:"
	file $(TARGET)
	@echo "Size:"
	ls -lh $(TARGET)
	@echo "Dependencies:"
	ldd $(TARGET) 2>/dev/null || echo "Static binary - no dependencies"
	@echo "Testing help output:"
	./$(TARGET) --help || true

# 创建发布包
package: $(TARGET)
	@echo "Creating pure real version release package..."
	mkdir -p release-pure-real
	cp $(TARGET) release-pure-real/
	cp ../docs/INSTALL.md release-pure-real/ 2>/dev/null || echo "INSTALL.md not found"
	
	# 创建README
	cat > release-pure-real/README-PURE-REAL.md << 'EOF'
# XenServer Antivirus Host Service - Pure Real Version

这是XenServer防病毒宿主机服务的纯真实版本，使用真实的Xen API。

## 特性

- 使用真实的Xen API (libxenctrl, libxenstore, libxengnttab)
- 静态编译，无运行时依赖
- 包含信号处理修复 (Ctrl+C 可正常终止)
- 包含权限检查和错误报告改进
- 可中断的sleep调用

## 系统要求

- XenServer Dom0 环境
- root权限
- Xen hypervisor正在运行

## 使用方法

```bash
# 基本运行
sudo ./xenserver-antivirus-host-real-pure-static

# 查看帮助
./xenserver-antivirus-host-real-pure-static --help

# 守护进程模式
sudo ./xenserver-antivirus-host-real-pure-static -d

# 调试模式
sudo ./xenserver-antivirus-host-real-pure-static -v -l 3
```

## 信号处理

- Ctrl+C (SIGINT): 优雅关闭
- SIGTERM: 优雅关闭  
- 连续3次信号: 强制退出

## 故障排除

如果遇到权限错误：
1. 确保以root权限运行
2. 确保在XenServer Dom0中运行
3. 检查Xen服务：systemctl status xenstored
4. 检查设备权限：ls -la /dev/xen/

如果程序无法终止：
1. 尝试Ctrl+C
2. 连续按3次Ctrl+C强制退出
3. 或使用：kill -TERM <pid>
EOF
	
	tar -czf xenserver-antivirus-host-pure-real-static.tar.gz release-pure-real/
	rm -rf release-pure-real/
	@echo "Pure real version release package created: xenserver-antivirus-host-pure-real-static.tar.gz"

# 显示编译信息
info:
	@echo "=== Pure Real Version Build Configuration ==="
	@echo "CC: $(CC)"
	@echo "CFLAGS: $(CFLAGS)"
	@echo "LDFLAGS: $(LDFLAGS)"
	@echo "INCLUDES: $(INCLUDES)"
	@echo "Sources: $(HOST_SOURCES) $(COMMON_SOURCES)"
	@echo "Target: $(TARGET)"
	@echo "============================================="

.PHONY: all clean directories check-deps install test package info