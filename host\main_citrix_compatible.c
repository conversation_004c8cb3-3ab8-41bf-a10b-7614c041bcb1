/*
 * XenServer宿主机防病毒服务 - Citrix Hypervisor兼容版本
 * 专门针对Citrix Hypervisor 8.2.1环境优化
 */

#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <pthread.h>
#include <sys/mman.h>
#include <sys/select.h>
#include <sys/time.h>
#include <getopt.h>
#include <time.h>
#include <stdarg.h>

/* Xen头文件 */
#include <xenctrl.h>
#include <xenstore.h>
#include <xengnttab.h>

/* 项目头文件 */
#include "../common/av_common.h"

/* 版本信息 */
#undef AV_VERSION
#define AV_VERSION "1.0.0-citrix"
#define AV_BUILD_INFO "Citrix Hypervisor Compatible"

/* Citrix特定的环境设置 */
static void setup_citrix_environment(void) {
    /* 设置Citrix Hypervisor特定的环境变量 */
    setenv("XENSTORED_ROOTDIR", "/var/lib/xenstored", 0);
    setenv("XENSTORE_DOMAIN_INTERFACE", "1", 0);
    setenv("XEN_DOMAIN_INTERFACE", "1", 0);
    setenv("XENSTORED_PATH", "/var/run/xenstored", 0);
}

/* Citrix兼容的域信息获取 */
static int citrix_get_domain_list(xc_interface *xc_handle, xc_domaininfo_t **domain_info, int max_domains) {
    int num_domains;
    int retry_count = 0;
    const int max_retries = 3;
    
    *domain_info = malloc(max_domains * sizeof(xc_domaininfo_t));
    if (!*domain_info) {
        av_log(AV_LOG_ERROR, "Memory allocation failed for domain info");
        return -1;
    }
    
    /* 在Citrix环境中，可能需要多次尝试 */
    while (retry_count < max_retries) {
        errno = 0;  /* 清除之前的错误 */
        
        /* 尝试获取域列表 */
        num_domains = xc_domain_getinfolist(xc_handle, 0, max_domains, *domain_info);
        
        if (num_domains >= 0) {
            av_log(AV_LOG_DEBUG, "Successfully retrieved %d domains (attempt %d)", num_domains, retry_count + 1);
            return num_domains;
        }
        
        int saved_errno = errno;
        av_log(AV_LOG_WARN, "Domain list attempt %d failed: %s (errno: %d)",
               retry_count + 1, strerror(saved_errno), saved_errno);
        
        /* 在Citrix环境中，某些错误可能是临时的 */
        if (saved_errno == EAGAIN || saved_errno == EBUSY) {
            retry_count++;
            usleep(100000);  /* 等待100ms */
            continue;
        }
        
        /* 对于权限错误，提供Citrix特定的建议 */
        if (saved_errno == EPERM || saved_errno == EACCES) {
            av_log(AV_LOG_ERROR, "Permission denied in Citrix Hypervisor environment");
            av_log(AV_LOG_ERROR, "Ensure running as root on Citrix Hypervisor host (not in VM)");
            av_log(AV_LOG_ERROR, "Check that oxenstored service is running: pgrep oxenstored");
            av_log(AV_LOG_ERROR, "Verify /dev/xen/ device permissions: ls -la /dev/xen/");
            av_log(AV_LOG_ERROR, "Try: chmod 666 /dev/xen/privcmd /dev/xen/xenbus*");
        }
        
        break;  /* 对于其他错误，不重试 */
    }
    
    free(*domain_info);
    *domain_info = NULL;
    return -1;
}

/* Citrix兼容的Xen接口初始化 */
static xc_interface* citrix_xc_interface_open(void) {
    xc_interface *xc_handle;
    
    /* 设置Citrix环境 */
    setup_citrix_environment();
    
    av_log(AV_LOG_DEBUG, "Opening XC interface for Citrix Hypervisor");
    
    /* 尝试打开接口 */
    xc_handle = xc_interface_open(NULL, NULL, 0);
    if (!xc_handle) {
        int saved_errno = errno;
        av_log(AV_LOG_ERROR, "Failed to open xc interface: %s (errno: %d)", strerror(saved_errno), saved_errno);
        
        /* Citrix特定的错误诊断 */
        av_log(AV_LOG_ERROR, "Citrix Hypervisor troubleshooting:");
        av_log(AV_LOG_ERROR, "1. Ensure this is running on Citrix Hypervisor host (not VM)");
        av_log(AV_LOG_ERROR, "2. Check oxenstored: systemctl status oxenstored");
        av_log(AV_LOG_ERROR, "3. Check device permissions: ls -la /dev/xen/");
        av_log(AV_LOG_ERROR, "4. Verify Citrix version: cat /etc/xensource-inventory");
        
        return NULL;
    }
    
    av_log(AV_LOG_INFO, "XC interface opened successfully for Citrix Hypervisor");
    return xc_handle;
}

/* 宿主机服务结构体 */
typedef struct {
    xc_interface* xc_handle;        /* libxenctrl句柄 */
    struct xs_handle* xs_handle;    /* XenStore句柄 */
    xengnttab_handle* gnttab_handle; /* Grant table句柄 */
    pthread_t vm_monitor_thread;    /* VM监控线程 */
    volatile int shutdown_requested; /* 关闭请求标志 */
    int max_vms;                    /* 最大VM数量 */
} av_host_service_t;

/* 全局变量 */
static av_host_service_t g_service;
static volatile int g_shutdown_requested = 0;
static int g_log_level = AV_LOG_INFO;

/* 信号处理器 */
static void signal_handler(int sig) {
    av_log(AV_LOG_INFO, "Received signal %d, shutting down gracefully...", sig);
    g_shutdown_requested = 1;
    g_service.shutdown_requested = 1;
}

/* VM监控线程 */
static void* vm_monitor_thread(void* arg) {
    av_host_service_t* service = (av_host_service_t*)arg;
    xc_domaininfo_t* domain_info = NULL;
    
    av_log(AV_LOG_INFO, "VM monitor thread started");
    
    while (!service->shutdown_requested) {
        /* 获取域列表 */
        int num_domains = citrix_get_domain_list(service->xc_handle, &domain_info, service->max_vms);
        
        if (num_domains < 0) {
            av_log(AV_LOG_ERROR, "Failed to list running VMs: Xen error");
        } else {
            av_log(AV_LOG_DEBUG, "Found %d running domains", num_domains);
            
            /* 处理每个域 */
            for (int i = 0; i < num_domains; i++) {
                if (domain_info[i].domain == 0) {
                    continue;  /* 跳过Domain-0 */
                }

                av_log(AV_LOG_DEBUG, "Monitoring VM: domid=%d, flags=0x%x",
                       domain_info[i].domain, domain_info[i].flags);
            }
        }
        
        if (domain_info) {
            free(domain_info);
            domain_info = NULL;
        }
        
        /* 等待5秒或直到收到关闭信号 */
        for (int i = 0; i < 50 && !service->shutdown_requested; i++) {
            usleep(100000);  /* 100ms */
        }
    }
    
    av_log(AV_LOG_INFO, "VM monitor thread shutting down");
    return NULL;
}

/* 初始化宿主机服务 */
static int init_host_service(av_host_service_t* service, int max_vms) {
    memset(service, 0, sizeof(av_host_service_t));
    service->max_vms = max_vms;
    
    av_log(AV_LOG_INFO, "Detecting Xen environment...");
    
    /* 检测Xen版本 */
    FILE* fp = fopen("/sys/hypervisor/version/major", "r");
    if (fp) {
        int major_version;
        if (fscanf(fp, "%d", &major_version) == 1) {
            av_log(AV_LOG_INFO, "Detected Xen version: %d.x", major_version);
        }
        fclose(fp);
    }
    
    av_log(AV_LOG_INFO, "Compiled with Xen libraries version: 4.17.x (WSL)");
    av_log(AV_LOG_INFO, "Target compatibility: Xen 4.13.x - 4.17.x");
    
    av_log(AV_LOG_INFO, "Initializing Xen interfaces...");
    
    /* 打开libxenctrl - 使用Citrix兼容方法 */
    service->xc_handle = citrix_xc_interface_open();
    if (!service->xc_handle) {
        return AV_ERROR_XC_INTERFACE;
    }
    
    /* 打开XenStore */
    service->xs_handle = xs_open(0);
    if (!service->xs_handle) {
        av_log(AV_LOG_WARN, "Failed to open xenstore interface: %s", strerror(errno));
        av_log(AV_LOG_WARN, "Continuing without xenstore access");
    }
    
    /* 打开Grant Table */
    service->gnttab_handle = xengnttab_open(NULL, 0);
    if (!service->gnttab_handle) {
        av_log(AV_LOG_WARN, "Failed to open grant table interface: %s", strerror(errno));
        av_log(AV_LOG_WARN, "Continuing without grant table access");
    }
    
    av_log(AV_LOG_INFO, "Host service initialized successfully");
    return AV_SUCCESS;
}

/* 清理宿主机服务 */
static void cleanup_host_service(av_host_service_t* service) {
    av_log(AV_LOG_INFO, "Cleaning up host service...");
    
    if (service->gnttab_handle) {
        xengnttab_close(service->gnttab_handle);
        service->gnttab_handle = NULL;
    }
    
    if (service->xs_handle) {
        xs_close(service->xs_handle);
        service->xs_handle = NULL;
    }
    
    if (service->xc_handle) {
        xc_interface_close(service->xc_handle);
        service->xc_handle = NULL;
    }
    
    av_log(AV_LOG_INFO, "Host service cleanup complete");
}

/* 显示帮助信息 */
static void show_help(const char* program_name) {
    printf("XenServer Antivirus Host Service - Citrix Hypervisor Compatible\n");
    printf("Version: %s\n", AV_VERSION);
    printf("Build: %s\n\n", AV_BUILD_INFO);
    printf("Usage: %s [options]\n\n", program_name);
    printf("Options:\n");
    printf("  -h, --help     Show this help message\n");
    printf("  -v, --verbose  Enable verbose logging\n");
    printf("  -d, --daemon   Run as daemon\n");
    printf("  -t, --test     Run in test mode\n");
    printf("  --version      Show version information\n");
    printf("  --max-vms N    Set maximum VMs to monitor (default: 100)\n\n");
    printf("Citrix Hypervisor Requirements:\n");
    printf("  - Must run on Citrix Hypervisor host (not in VM)\n");
    printf("  - Requires root privileges\n");
    printf("  - oxenstored service must be running\n");
    printf("  - Proper /dev/xen/ device permissions\n\n");
}

/* 主函数 */
int main(int argc, char* argv[]) {
    int opt;
    int verbose = 0;
    int daemon_mode = 0;
    int test_mode = 0;
    int max_vms = 100;
    
    static struct option long_options[] = {
        {"help", no_argument, 0, 'h'},
        {"verbose", no_argument, 0, 'v'},
        {"daemon", no_argument, 0, 'd'},
        {"test", no_argument, 0, 't'},
        {"version", no_argument, 0, 1000},
        {"max-vms", required_argument, 0, 1001},
        {0, 0, 0, 0}
    };
    
    /* 解析命令行参数 */
    while ((opt = getopt_long(argc, argv, "hvdt", long_options, NULL)) != -1) {
        switch (opt) {
            case 'h':
                show_help(argv[0]);
                return 0;
            case 'v':
                verbose = 1;
                g_log_level = AV_LOG_DEBUG;
                break;
            case 'd':
                daemon_mode = 1;
                break;
            case 't':
                test_mode = 1;
                break;
            case 1000:  /* --version */
                printf("XenServer Antivirus Host Service\n");
                printf("Version: %s\n", AV_VERSION);
                printf("Build: %s\n", AV_BUILD_INFO);
                printf("Compiled: %s %s\n", __DATE__, __TIME__);
                return 0;
            case 1001:  /* --max-vms */
                max_vms = atoi(optarg);
                if (max_vms <= 0 || max_vms > 1000) {
                    fprintf(stderr, "Invalid max-vms value: %s\n", optarg);
                    return 1;
                }
                break;
            default:
                show_help(argv[0]);
                return 1;
        }
    }
    
    /* 设置日志级别 */
    av_set_log_level(g_log_level);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Host Service starting...");
    av_log(AV_LOG_INFO, "Version: %s, Build: %s", AV_VERSION, AV_BUILD_INFO);
    av_log(AV_LOG_INFO, "Compiled: %s %s", __DATE__, __TIME__);
    av_log(AV_LOG_INFO, "Max VMs: %d", max_vms);
    
    /* 检查是否以root权限运行 */
    if (geteuid() != 0) {
        av_log(AV_LOG_ERROR, "Must run as root");
        return 1;
    }
    
    /* 设置信号处理器 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    /* 初始化服务 */
    if (init_host_service(&g_service, max_vms) != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to initialize host service");
        return 1;
    }
    
    av_log(AV_LOG_INFO, "Host service started successfully");
    
    /* 测试模式 */
    if (test_mode) {
        av_log(AV_LOG_INFO, "Running in test mode...");
        
        /* 测试域列表获取 */
        xc_domaininfo_t* domain_info = NULL;
        int num_domains = citrix_get_domain_list(g_service.xc_handle, &domain_info, max_vms);
        
        if (num_domains >= 0) {
            av_log(AV_LOG_INFO, "Test successful: Found %d domains", num_domains);
            if (domain_info) free(domain_info);
        } else {
            av_log(AV_LOG_ERROR, "Test failed: Could not retrieve domain list");
        }
        
        cleanup_host_service(&g_service);
        return (num_domains >= 0) ? 0 : 1;
    }
    
    /* 启动VM监控线程 */
    if (pthread_create(&g_service.vm_monitor_thread, NULL, vm_monitor_thread, &g_service) != 0) {
        av_log(AV_LOG_ERROR, "Failed to create VM monitor thread");
        cleanup_host_service(&g_service);
        return 1;
    }
    
    av_log(AV_LOG_INFO, "VM monitor thread started");
    av_log(AV_LOG_INFO, "Service started successfully, monitoring VMs...");
    
    /* 主循环 */
    while (!g_shutdown_requested) {
        sleep(1);
    }
    
    av_log(AV_LOG_INFO, "Shutting down...");
    
    /* 等待监控线程结束 */
    pthread_join(g_service.vm_monitor_thread, NULL);
    
    /* 清理资源 */
    cleanup_host_service(&g_service);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Host Service stopped");
    return 0;
}
