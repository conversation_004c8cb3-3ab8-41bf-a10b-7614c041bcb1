# 示例程序Makefile

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g -D_GNU_SOURCE
LDFLAGS = -lxenctrl -lxenstore -lpthread

# 目录定义
COMMONDIR = ../common
HOSTDIR = ../host
OBJDIR = obj
BINDIR = bin

# 源文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
HOST_SOURCES = $(HOSTDIR)/av_vm_discovery.c $(HOSTDIR)/av_xenstore.c $(HOSTDIR)/av_error_handling.c $(HOSTDIR)/av_communication.c $(HOSTDIR)/av_service.c $(HOSTDIR)/av_shared_memory.c
EXAMPLE_SOURCES = monitor_demo.c

# 目标文件
COMMON_OBJECTS = $(COMMON_SOURCES:%.c=$(OBJDIR)/%.o)
HOST_OBJECTS = $(HOST_SOURCES:%.c=$(OBJDIR)/%.o)
EXAMPLE_OBJECTS = $(EXAMPLE_SOURCES:%.c=$(OBJDIR)/%.o)

# 可执行文件
TARGETS = $(BINDIR)/monitor_demo

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I$(HOSTDIR)

# 默认目标
all: directories $(TARGETS)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(OBJDIR)/$(HOSTDIR)
	@mkdir -p $(BINDIR)

# 编译示例程序
$(BINDIR)/monitor_demo: $(OBJDIR)/monitor_demo.o $(COMMON_OBJECTS) $(HOST_OBJECTS)
	@echo "Linking $@..."
	$(CC) $^ -o $@ $(LDFLAGS)

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $<..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning example build files..."
	rm -rf $(OBJDIR) $(BINDIR)

# 安装示例程序
install: $(TARGETS)
	@echo "Installing examples..."
	@mkdir -p /usr/local/bin
	cp $(BINDIR)/monitor_demo /usr/local/bin/

# 帮助信息
help:
	@echo "Available targets:"
	@echo "  all     - Build all examples (default)"
	@echo "  clean   - Remove build files"
	@echo "  install - Install examples to /usr/local/bin"
	@echo "  help    - Show this help message"

.PHONY: all clean install help directories