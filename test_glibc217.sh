#!/bin/bash

# XenServer防病毒系统 - glibc 2.17兼容版本测试脚本
# 在WSL中测试编译的程序基本功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示测试信息
show_header() {
    echo "=================================================================="
    echo "XenServer防病毒系统 - glibc 2.17兼容版本测试"
    echo "=================================================================="
    echo "测试环境: WSL"
    echo "目标程序: xenserver-antivirus-glibc217-static"
    echo "测试时间: $(date)"
    echo "=================================================================="
    echo ""
}

# 检查程序文件
check_program() {
    log_info "检查程序文件..."
    
    PROGRAM_PATH="host/bin/xenserver-antivirus-glibc217-static"
    
    if [ ! -f "$PROGRAM_PATH" ]; then
        log_error "程序文件不存在: $PROGRAM_PATH"
        log_info "请先运行构建脚本: ./build_glibc217.sh"
        exit 1
    fi
    
    # 检查文件权限
    if [ ! -x "$PROGRAM_PATH" ]; then
        log_warning "程序文件不可执行，正在修复权限..."
        chmod +x "$PROGRAM_PATH"
    fi
    
    # 显示文件信息
    log_info "程序文件信息:"
    ls -lh "$PROGRAM_PATH"
    file "$PROGRAM_PATH"
    
    # 检查静态链接
    if ldd "$PROGRAM_PATH" 2>&1 | grep -q "not a dynamic executable"; then
        log_success "✅ 静态链接验证通过"
    else
        log_warning "⚠️  程序可能不是完全静态链接"
        ldd "$PROGRAM_PATH" 2>/dev/null || true
    fi
    
    echo ""
}

# 测试程序基本功能
test_basic_functions() {
    log_info "测试程序基本功能..."
    
    PROGRAM_PATH="host/bin/xenserver-antivirus-glibc217-static"
    
    # 测试1: 帮助信息
    log_info "测试1: 帮助信息"
    if timeout 10s "$PROGRAM_PATH" --help >/dev/null 2>&1; then
        log_success "✅ 帮助信息测试通过"
    else
        log_warning "⚠️  帮助信息测试失败（可能需要在Xen环境中运行）"
    fi
    
    # 测试2: 版本信息
    log_info "测试2: 版本信息"
    if timeout 10s "$PROGRAM_PATH" --version >/dev/null 2>&1; then
        log_success "✅ 版本信息测试通过"
    else
        log_warning "⚠️  版本信息测试失败（可能需要在Xen环境中运行）"
    fi
    
    # 测试3: 测试模式
    log_info "测试3: 测试模式"
    if timeout 10s "$PROGRAM_PATH" --test >/dev/null 2>&1; then
        log_success "✅ 测试模式通过"
    else
        log_warning "⚠️  测试模式失败（预期行为，需要Xen环境）"
    fi
    
    # 测试4: 无效参数
    log_info "测试4: 无效参数处理"
    if timeout 10s "$PROGRAM_PATH" --invalid-option >/dev/null 2>&1; then
        log_warning "⚠️  无效参数未被正确处理"
    else
        log_success "✅ 无效参数处理正确"
    fi
    
    echo ""
}

# 测试部署包
test_deployment_package() {
    log_info "测试部署包..."
    
    DEPLOY_DIR="host/deploy_glibc217"
    
    if [ ! -d "$DEPLOY_DIR" ]; then
        log_error "部署目录不存在: $DEPLOY_DIR"
        return 1
    fi
    
    # 检查必要文件
    local required_files=(
        "xenserver-antivirus-glibc217-static"
        "deploy_glibc217.sh"
        "README.md"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$DEPLOY_DIR/$file" ]; then
            log_success "✅ $file 存在"
        else
            log_error "❌ $file 缺失"
        fi
    done
    
    # 检查部署脚本权限
    if [ -x "$DEPLOY_DIR/deploy_glibc217.sh" ]; then
        log_success "✅ 部署脚本可执行"
    else
        log_warning "⚠️  部署脚本权限问题"
    fi
    
    # 显示部署包大小
    log_info "部署包内容:"
    ls -lah "$DEPLOY_DIR/"
    
    echo ""
}

# 测试压缩包
test_archive() {
    log_info "测试压缩包..."
    
    ARCHIVE_PATTERN="host/xenserver-antivirus-glibc217-*.tar.gz"
    ARCHIVE_FILE=$(ls $ARCHIVE_PATTERN 2>/dev/null | head -1)
    
    if [ -n "$ARCHIVE_FILE" ]; then
        log_success "✅ 压缩包存在: $ARCHIVE_FILE"
        log_info "压缩包大小: $(du -h "$ARCHIVE_FILE" | cut -f1)"
        
        # 测试压缩包内容
        log_info "压缩包内容:"
        tar -tzf "$ARCHIVE_FILE" | head -10
    else
        log_warning "⚠️  压缩包不存在"
    fi
    
    echo ""
}

# 生成部署指南
generate_deployment_guide() {
    log_info "生成部署指南..."
    
    cat > "DEPLOYMENT_GUIDE_glibc217.md" << 'EOF'
# XenServer防病毒系统 - glibc 2.17兼容版本部署指南

## 概述
此版本专门针对Citrix Hypervisor 8.2.1 (glibc 2.17)优化，解决了从WSL编译的程序在老版本Xen环境中的兼容性问题。

## 问题背景
- **问题**: 从WSL编译的程序无法在Citrix Hypervisor 8.2.1上运行
- **原因**: glibc版本不兼容（WSL使用glibc 2.39，目标环境使用glibc 2.17）
- **解决方案**: 使用静态链接和兼容性编译选项

## 部署步骤

### 1. 准备部署包
```bash
# 在WSL中构建
./build_glibc217.sh

# 或使用Windows批处理
.\build_glibc217.bat
```

### 2. 传输到目标环境
```bash
# 方法1: 使用压缩包
scp host/xenserver-antivirus-glibc217-*.tar.gz root@your-xenserver:/tmp/

# 方法2: 直接传输部署目录
scp -r host/deploy_glibc217/ root@your-xenserver:/tmp/
```

### 3. 在Citrix Hypervisor上部署
```bash
# 解压（如果使用压缩包）
cd /tmp
tar -xzf xenserver-antivirus-glibc217-*.tar.gz

# 运行部署脚本
sudo ./deploy_glibc217.sh

# 或手动安装
sudo install -m 755 xenserver-antivirus-glibc217-static /usr/local/bin/
```

### 4. 验证安装
```bash
# 检查程序
/usr/local/bin/xenserver-antivirus-glibc217-static --help

# 测试运行
sudo /usr/local/bin/xenserver-antivirus-glibc217-static --test

# 正常运行
sudo /usr/local/bin/xenserver-antivirus-glibc217-static -v
```

## 技术特性

### 编译优化
- **静态链接**: 避免动态库依赖问题
- **glibc兼容**: 针对glibc 2.17优化
- **交叉编译**: WSL环境编译，Xen环境运行

### 兼容性保证
- **目标系统**: Citrix Hypervisor 8.2.1
- **Xen版本**: 4.13.x - 4.17.x
- **架构**: x86_64

### 文件说明
- `xenserver-antivirus-glibc217-static`: 主程序（静态链接）
- `deploy_glibc217.sh`: 自动部署脚本
- `README.md`: 部署说明文档

## 故障排除

### 常见问题
1. **权限错误**: 确保以root权限运行
2. **Xen环境检测失败**: 确保在Xen宿主机上运行
3. **glibc版本警告**: 正常现象，程序仍可运行

### 调试命令
```bash
# 检查程序依赖
ldd /usr/local/bin/xenserver-antivirus-glibc217-static

# 检查Xen环境
xl info

# 检查glibc版本
ldd --version
```

## 构建信息
- **构建时间**: $(date)
- **编译环境**: WSL (Ubuntu)
- **编译器**: gcc 13.3.0
- **目标glibc**: 2.17
- **程序大小**: ~1.3MB
EOF

    log_success "✅ 部署指南已生成: DEPLOYMENT_GUIDE_glibc217.md"
    echo ""
}

# 显示测试摘要
show_test_summary() {
    echo "=================================================================="
    echo "测试摘要"
    echo "=================================================================="
    
    PROGRAM_PATH="host/bin/xenserver-antivirus-glibc217-static"
    DEPLOY_DIR="host/deploy_glibc217"
    ARCHIVE_PATTERN="host/xenserver-antivirus-glibc217-*.tar.gz"
    ARCHIVE_FILE=$(ls $ARCHIVE_PATTERN 2>/dev/null | head -1)
    
    if [ -f "$PROGRAM_PATH" ]; then
        echo "✅ 主程序: $PROGRAM_PATH ($(du -h "$PROGRAM_PATH" | cut -f1))"
    else
        echo "❌ 主程序: 缺失"
    fi
    
    if [ -d "$DEPLOY_DIR" ]; then
        echo "✅ 部署包: $DEPLOY_DIR/"
    else
        echo "❌ 部署包: 缺失"
    fi
    
    if [ -n "$ARCHIVE_FILE" ]; then
        echo "✅ 压缩包: $ARCHIVE_FILE ($(du -h "$ARCHIVE_FILE" | cut -f1))"
    else
        echo "❌ 压缩包: 缺失"
    fi
    
    echo ""
    log_info "下一步操作:"
    echo "1. 将部署包传输到Citrix Hypervisor 8.2.1宿主机"
    echo "2. 在宿主机上运行: sudo ./deploy_glibc217.sh"
    echo "3. 参考部署指南: DEPLOYMENT_GUIDE_glibc217.md"
    
    echo ""
    log_success "glibc 2.17兼容版本测试完成！"
}

# 主函数
main() {
    # 检查是否在正确的目录中
    if [ ! -f "host/main_cross_compile.c" ]; then
        log_error "请在xenserver-antivirus项目根目录中运行此脚本"
        exit 1
    fi
    
    # 执行测试步骤
    show_header
    check_program
    test_basic_functions
    test_deployment_package
    test_archive
    generate_deployment_guide
    show_test_summary
}

# 运行主函数
main "$@"
