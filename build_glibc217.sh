#!/bin/bash

# XenServer防病毒系统 - glibc 2.17兼容构建脚本
# 专门针对Citrix Hypervisor 8.2.1 (glibc 2.17)
# 从WSL编译，在Citrix Hypervisor上运行

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示脚本信息
show_header() {
    echo "=================================================================="
    echo "XenServer防病毒系统 - glibc 2.17兼容构建"
    echo "=================================================================="
    echo "目标环境: Citrix Hypervisor 8.2.1 (glibc 2.17)"
    echo "编译环境: WSL"
    echo "构建时间: $(date)"
    echo "=================================================================="
    echo ""
}

# 检查构建环境
check_build_environment() {
    log_info "检查构建环境..."
    
    # 检查是否在正确的目录中
    if [ ! -f "host/main_cross_compile.c" ] || [ ! -f "common/av_common.c" ]; then
        log_error "请在xenserver-antivirus项目根目录中运行此脚本"
        exit 1
    fi
    
    # 检查编译器
    if ! command -v gcc >/dev/null 2>&1; then
        log_error "gcc编译器未找到，请安装build-essential"
        exit 1
    fi
    
    # 检查Xen开发库
    if ! pkg-config --exists xencontrol xenstore 2>/dev/null; then
        log_warning "Xen开发库未找到，将尝试使用系统库"
        if [ ! -f "/usr/lib/x86_64-linux-gnu/libxenctrl.so" ]; then
            log_error "Xen库未找到，请安装libxen-dev包"
            echo "Ubuntu/Debian: sudo apt install libxen-dev"
            exit 1
        fi
    fi
    
    log_success "构建环境检查完成"
    echo ""
}

# 清理之前的构建
clean_previous_builds() {
    log_info "清理之前的构建文件..."
    
    cd host
    make -f Makefile.glibc217 clean >/dev/null 2>&1 || true
    cd ..
    
    log_success "清理完成"
    echo ""
}

# 编译宿主机服务
build_host_service() {
    log_info "编译宿主机服务（glibc 2.17兼容版本）..."
    
    cd host
    
    # 显示编译配置
    make -f Makefile.glibc217 show-info
    
    # 开始编译
    if make -f Makefile.glibc217; then
        log_success "宿主机服务编译成功"
        
        # 显示编译结果
        if [ -f "bin/xenserver-antivirus-glibc217-static" ]; then
            log_info "编译结果："
            ls -lh bin/xenserver-antivirus-glibc217-static
            file bin/xenserver-antivirus-glibc217-static 2>/dev/null || echo "文件信息不可用"
            
            # 检查是否为静态链接
            if ldd bin/xenserver-antivirus-glibc217-static 2>&1 | grep -q "not a dynamic executable"; then
                log_success "✅ 静态链接验证通过"
            else
                log_warning "⚠️  程序可能不是完全静态链接"
                ldd bin/xenserver-antivirus-glibc217-static 2>/dev/null || true
            fi
        fi
    else
        log_error "宿主机服务编译失败"
        cd ..
        exit 1
    fi
    
    cd ..
}

# 运行测试
run_tests() {
    log_info "运行基本测试..."
    
    cd host
    
    # 测试程序是否可以执行
    if [ -f "bin/xenserver-antivirus-glibc217-static" ]; then
        # 测试帮助信息
        if timeout 5s ./bin/xenserver-antivirus-glibc217-static --help >/dev/null 2>&1; then
            log_success "✅ 程序帮助信息测试通过"
        else
            log_warning "⚠️  程序帮助信息测试失败（可能需要在Xen环境中运行）"
        fi
        
        # 测试版本信息
        if timeout 5s ./bin/xenserver-antivirus-glibc217-static --version >/dev/null 2>&1; then
            log_success "✅ 程序版本信息测试通过"
        else
            log_warning "⚠️  程序版本信息测试失败（可能需要在Xen环境中运行）"
        fi
    fi
    
    cd ..
    
    log_info "基本测试完成"
    echo ""
}

# 创建发布包
create_release_package() {
    log_info "创建发布包..."
    
    cd host
    
    # 创建压缩包
    if make -f Makefile.glibc217 archive; then
        log_success "发布包创建成功"
        
        # 显示发布包信息
        ARCHIVE_NAME=$(ls xenserver-antivirus-glibc217-*.tar.gz 2>/dev/null | head -1)
        if [ -n "$ARCHIVE_NAME" ]; then
            log_info "发布包: $ARCHIVE_NAME"
            log_info "大小: $(du -h "$ARCHIVE_NAME" | cut -f1)"
        fi
    else
        log_warning "发布包创建失败"
    fi
    
    cd ..
    
    echo ""
}

# 显示构建摘要
show_build_summary() {
    echo "=================================================================="
    echo "构建摘要"
    echo "=================================================================="
    
    if [ -f "host/bin/xenserver-antivirus-glibc217-static" ]; then
        echo "✅ 宿主机服务: host/bin/xenserver-antivirus-glibc217-static"
        echo "   大小: $(du -h host/bin/xenserver-antivirus-glibc217-static | cut -f1)"
    fi
    
    if [ -d "host/deploy_glibc217" ]; then
        echo "✅ 部署包: host/deploy_glibc217/"
        echo "   文件:"
        ls -la host/deploy_glibc217/ | grep -v "^total" | awk '{print "     " $9 " (" $5 " bytes)"}'
    fi
    
    ARCHIVE_NAME=$(ls host/xenserver-antivirus-glibc217-*.tar.gz 2>/dev/null | head -1)
    if [ -n "$ARCHIVE_NAME" ]; then
        echo "✅ 发布包: $ARCHIVE_NAME"
        echo "   大小: $(du -h "$ARCHIVE_NAME" | cut -f1)"
    fi
    
    echo
    log_info "部署说明："
    echo "1. 将部署包复制到Citrix Hypervisor 8.2.1宿主机"
    echo "2. 解压并运行: sudo ./deploy_glibc217.sh"
    echo "3. 或手动安装: sudo install -m 755 xenserver-antivirus-glibc217-static /usr/local/bin/"
    
    echo
    log_success "glibc 2.17兼容构建完成！"
}

# 主函数
main() {
    # 检查是否在正确的目录中
    if [ ! -f "host/main_cross_compile.c" ] || [ ! -f "common/av_common.c" ]; then
        log_error "请在xenserver-antivirus项目根目录中运行此脚本"
        exit 1
    fi
    
    # 执行构建步骤
    show_header
    check_build_environment
    clean_previous_builds
    build_host_service
    run_tests
    create_release_package
    show_build_summary
}

# 运行主函数
main "$@"
