# 简化版静态编译Makefile - 避开复杂依赖

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -static -DSIMPLE_BUILD

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj-simple-static
BINDIR = bin-simple-static

# 简化版源文件（不包含复杂的Xen依赖）
SIMPLE_SOURCES = main_simple.c xen_mock_impl.c
COMMON_SOURCES = $(COMMONDIR)/av_common.c
SOURCES = $(SIMPLE_SOURCES) $(COMMON_SOURCES)
OBJECTS = $(SIMPLE_SOURCES:%.c=$(OBJDIR)/%.o) $(OBJDIR)/av_common.o
TARGET = $(BINDIR)/av_linux_client_simple_static

# 简化的链接选项（只链接基本库）
LDFLAGS = -lpthread

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR)

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Statically linking simple version $@..."
	$(CC) -static $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Simple static build complete: $@"
	@echo "Binary size: $$(du -h $@ | cut -f1)"
	@echo "Dependencies check:"
	@ldd $@ 2>/dev/null || echo "  -> Statically linked (no dynamic dependencies)"

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $< (simple static)..."
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译common文件
$(OBJDIR)/av_common.o: $(COMMONDIR)/av_common.c
	@echo "Compiling $< (simple static)..."
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning simple static build files..."
	rm -rf $(OBJDIR) $(BINDIR)

# 安装程序
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/av_linux_client_simple_static
	sudo chmod +x /usr/local/bin/av_linux_client_simple_static
	@echo "Simple static installation complete"

# 运行程序
run: $(TARGET)
	@echo "Running $(TARGET)..."
	$(TARGET)

# 验证静态链接
verify: $(TARGET)
	@echo "Verifying simple static linkage..."
	@file $(TARGET)
	@echo "Dependencies:"
	@ldd $(TARGET) 2>/dev/null || echo "  -> Statically linked (no dynamic dependencies)"
	@echo "Binary size: $$(du -h $(TARGET) | cut -f1)"
	@echo "Symbols:"
	@nm $(TARGET) | head -10

# 帮助信息
help:
	@echo "Available targets for simple static build:"
	@echo "  all        - Build the simple static program (default)"
	@echo "  clean      - Remove simple static build files"
	@echo "  install    - Install the simple static program"
	@echo "  run        - Build and run the simple static program"
	@echo "  verify     - Verify static linkage of built binary"
	@echo "  help       - Show this help message"

.PHONY: all clean install run verify help directories