#ifdef SIMPLE_BUILD
#include "av_host_simple.h"
#else
#include "av_host.h"
#endif
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>

/* 通过UUID查找虚拟机 */
int av_find_vm_by_uuid(av_host_service_t* service, const char* uuid, domid_t* domid) {
    if (!service || !uuid || !domid) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_find_vm_by_uuid");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 验证UUID格式 */
    int ret = av_validate_uuid(uuid);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Invalid UUID format: %s", uuid);
        return ret;
    }

    /* 检查libxc句柄 */
    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "libxc handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Searching for VM with UUID: %s", uuid);

    /* 获取所有域的信息 */
    xc_domaininfo_t* domain_info = NULL;
    int max_domains = 1024;  /* 最大域数量 */
    int num_domains = 0;

    domain_info = malloc(max_domains * sizeof(xc_domaininfo_t));
    if (!domain_info) {
        av_log(AV_LOG_ERROR, "Failed to allocate memory for domain info");
        return AV_ERROR_MEMORY_ALLOC;
    }

    /* 获取域列表 */
    num_domains = xc_domain_getinfolist(service->xc_handle, 0, max_domains, domain_info);
    if (num_domains < 0) {
        int saved_errno = errno;
        av_log(AV_LOG_ERROR, "Failed to get domain list: %s (errno: %d)", strerror(saved_errno), saved_errno);
        
        /* 提供更详细的错误信息 */
        if (saved_errno == EPERM || saved_errno == EACCES) {
            av_log(AV_LOG_ERROR, "Permission denied - ensure running as root with proper Xen privileges");
            av_log(AV_LOG_ERROR, "Check if user is in 'xen' group and has access to /dev/xen/xenstore");
        } else if (saved_errno == ENOENT || saved_errno == ENODEV) {
            av_log(AV_LOG_ERROR, "Xen hypervisor not found - ensure Xen is running and properly configured");
        } else if (saved_errno == ECONNREFUSED) {
            av_log(AV_LOG_ERROR, "Connection to Xen hypervisor refused - check xenstored daemon");
        }
        
        free(domain_info);
        return AV_ERROR_XEN_INTERFACE;
    }

    av_log(AV_LOG_DEBUG, "Found %d domains", num_domains);

    /* 遍历所有域，查找匹配的UUID */
    for (int i = 0; i < num_domains; i++) {
        domid_t current_domid = domain_info[i].domain;
        
        /* 跳过dom0 */
        if (current_domid == 0) {
            continue;
        }

        /* 通过XenStore获取域的UUID */
        char xenstore_path[256];
        char domain_uuid[AV_UUID_STRING_LENGTH];
        
        snprintf(xenstore_path, sizeof(xenstore_path), "/local/domain/%u/vm", current_domid);
        
        /* 读取VM路径 */
        char vm_path[256];
        ret = av_xenstore_read(service, xenstore_path, vm_path, sizeof(vm_path));
        if (ret != AV_SUCCESS) {
            av_log(AV_LOG_DEBUG, "Failed to read VM path for domain %u", current_domid);
            continue;
        }

        /* 构建UUID路径 */
        char uuid_path[512];
        snprintf(uuid_path, sizeof(uuid_path), "%s/uuid", vm_path);
        
        /* 读取UUID */
        ret = av_xenstore_read(service, uuid_path, domain_uuid, sizeof(domain_uuid));
        if (ret != AV_SUCCESS) {
            av_log(AV_LOG_DEBUG, "Failed to read UUID for domain %u", current_domid);
            continue;
        }

        av_log(AV_LOG_DEBUG, "Domain %u UUID: %s", current_domid, domain_uuid);

        /* 比较UUID */
        if (strcasecmp(uuid, domain_uuid) == 0) {
            *domid = current_domid;
            free(domain_info);
            av_log(AV_LOG_INFO, "Found VM %s with domain ID %u", uuid, current_domid);
            return AV_SUCCESS;
        }
    }

    free(domain_info);
    av_log(AV_LOG_WARN, "VM with UUID %s not found", uuid);
    return AV_ERROR_VM_NOT_FOUND;
}

/* 验证域状态 */
int av_verify_domain_state(av_host_service_t* service, domid_t domid) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "libxc handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Verifying state for domain %u", domid);

    /* 获取域信息 */
    xc_domaininfo_t domain_info;
    int ret = xc_domain_getinfolist(service->xc_handle, domid, 1, &domain_info);
    if (ret != 1) {
        int saved_errno = errno;
        av_log(AV_LOG_ERROR, "Failed to get info for domain %u: %s (errno: %d)", domid, strerror(saved_errno), saved_errno);
        
        if (saved_errno == EPERM || saved_errno == EACCES) {
            av_log(AV_LOG_ERROR, "Insufficient privileges to access domain %u", domid);
        } else if (saved_errno == ESRCH) {
            av_log(AV_LOG_ERROR, "Domain %u does not exist or is not accessible", domid);
        }
        
        return AV_ERROR_XEN_INTERFACE;
    }

    /* 检查域ID是否匹配 */
#ifdef SIMPLE_BUILD
    if (domain_info.domain != domid) {
        av_log(AV_LOG_ERROR, "Domain ID mismatch: expected %u, got %u", domid, domain_info.domain);
        return AV_ERROR_VM_NOT_FOUND;
    }
#else
    if (domain_info.domain != domid) {
        av_log(AV_LOG_ERROR, "Domain ID mismatch: expected %u, got %u", domid, domain_info.domain);
        return AV_ERROR_VM_NOT_FOUND;
    }
#endif

    /* 检查域状态 */
    if (domain_info.flags & XEN_DOMINF_dying) {
        av_log(AV_LOG_WARN, "Domain %u is dying", domid);
        return AV_ERROR_VM_NOT_FOUND;
    }

    if (domain_info.flags & XEN_DOMINF_shutdown) {
        av_log(AV_LOG_WARN, "Domain %u is shutdown", domid);
        return AV_ERROR_VM_NOT_FOUND;
    }

    /* 检查域是否正在运行 */
    if (!(domain_info.flags & XEN_DOMINF_running)) {
        av_log(AV_LOG_WARN, "Domain %u is not running (flags: 0x%x)", domid, domain_info.flags);
        return AV_ERROR_VM_NOT_FOUND;
    }

    av_log(AV_LOG_DEBUG, "Domain %u is running and healthy", domid);
    av_log(AV_LOG_DEBUG, "Domain info - Memory: %lu KB, VCPUs: %u, Flags: 0x%x", 
           domain_info.tot_pages * 4, domain_info.max_vcpu_id + 1, domain_info.flags);

    return AV_SUCCESS;
}

/* 获取域的详细信息 */
int av_get_domain_info(av_host_service_t* service, domid_t domid, xc_domaininfo_t* info) {
    if (!service || !info) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_get_domain_info");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "libxc handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    int ret = xc_domain_getinfolist(service->xc_handle, domid, 1, info);
    if (ret != 1) {
        av_log(AV_LOG_ERROR, "Failed to get domain info for %u: %s", domid, strerror(errno));
        return AV_ERROR_XEN_INTERFACE;
    }

    if (info->domain != domid) {
        av_log(AV_LOG_ERROR, "Domain ID mismatch in info");
        return AV_ERROR_VM_NOT_FOUND;
    }

    return AV_SUCCESS;
}

/* 检查域是否支持PV驱动 */
int av_check_pv_drivers(av_host_service_t* service, domid_t domid) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_DEBUG, "Checking PV drivers for domain %u", domid);

    /* 检查XenStore中的PV驱动相关键 */
    char xenstore_path[256];
    char value[64];
    int ret;

    /* 检查data目录是否存在 */
    snprintf(xenstore_path, sizeof(xenstore_path), "/local/domain/%u/data", domid);
    ret = av_xenstore_read(service, xenstore_path, value, sizeof(value));
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_DEBUG, "No data directory found for domain %u", domid);
    }

    /* 检查device目录是否存在 */
    snprintf(xenstore_path, sizeof(xenstore_path), "/local/domain/%u/device", domid);
    ret = av_xenstore_read(service, xenstore_path, value, sizeof(value));
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_DEBUG, "No device directory found for domain %u", domid);
    }

    /* 检查control目录是否存在 */
    snprintf(xenstore_path, sizeof(xenstore_path), "/local/domain/%u/control", domid);
    ret = av_xenstore_read(service, xenstore_path, value, sizeof(value));
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_DEBUG, "No control directory found for domain %u", domid);
    }

    av_log(AV_LOG_INFO, "PV driver check completed for domain %u", domid);
    return AV_SUCCESS;
}

/* 列出所有运行的虚拟机 */
int av_list_running_vms(av_host_service_t* service, domid_t** domids, int* count) {
    if (!service || !domids || !count) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_list_running_vms");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "libxc handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    /* 获取所有域的信息 */
    xc_domaininfo_t* domain_info = NULL;
    int max_domains = 1024;
    int num_domains = 0;

    domain_info = malloc(max_domains * sizeof(xc_domaininfo_t));
    if (!domain_info) {
        av_log(AV_LOG_ERROR, "Failed to allocate memory for domain info");
        return AV_ERROR_MEMORY_ALLOC;
    }

    num_domains = xc_domain_getinfolist(service->xc_handle, 0, max_domains, domain_info);
    if (num_domains < 0) {
        av_log(AV_LOG_ERROR, "Failed to get domain list: %s", strerror(errno));
        free(domain_info);
        return AV_ERROR_XEN_INTERFACE;
    }

    /* 分配结果数组 */
    domid_t* result = malloc(num_domains * sizeof(domid_t));
    if (!result) {
        av_log(AV_LOG_ERROR, "Failed to allocate memory for result array");
        free(domain_info);
        return AV_ERROR_MEMORY_ALLOC;
    }

    int running_count = 0;

    /* 筛选运行中的虚拟机 */
    for (int i = 0; i < num_domains; i++) {
        domid_t current_domid = domain_info[i].domain;
        
        /* 跳过dom0 */
        if (current_domid == 0) {
            continue;
        }

        /* 检查是否正在运行 */
        if ((domain_info[i].flags & XEN_DOMINF_running) &&
            !(domain_info[i].flags & XEN_DOMINF_dying) &&
            !(domain_info[i].flags & XEN_DOMINF_shutdown)) {
            
            result[running_count] = current_domid;
            running_count++;
            
            av_log(AV_LOG_DEBUG, "Found running VM: domain %u", current_domid);
        }
    }

    free(domain_info);

    *domids = result;
    *count = running_count;

    av_log(AV_LOG_INFO, "Found %d running VMs", running_count);
    return AV_SUCCESS;
}