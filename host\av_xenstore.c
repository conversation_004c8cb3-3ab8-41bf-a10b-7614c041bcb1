#ifdef SIMPLE_BUILD
#include "av_host_simple.h"
#else
#include "av_host.h"
#endif
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <stdbool.h>

/* XenStore读操作 */
int av_xenstore_read(av_host_service_t* service, const char* path, char* buffer, size_t buffer_size) {
    if (!service || !path || !buffer || buffer_size == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_read");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Reading XenStore path: %s", path);

    unsigned int len = 0;
    char* value = xs_read(service->xs_handle, XBT_NULL, path, &len);
    
    if (!value) {
        av_log(AV_LOG_DEBUG, "Failed to read XenStore path %s: %s", path, strerror(errno));
        return AV_ERROR_XENSTORE;
    }

    /* 检查缓冲区大小 */
    if (len >= buffer_size) {
        av_log(AV_LOG_ERROR, "Buffer too small for XenStore value (need %u, have %zu)", len + 1, buffer_size);
        free(value);
        return AV_ERROR_MEMORY_ALLOC;
    }

    /* 复制值到缓冲区 */
    memcpy(buffer, value, len);
    buffer[len] = '\0';

    free(value);

    av_log(AV_LOG_DEBUG, "XenStore read successful: %s = %s", path, buffer);
    return AV_SUCCESS;
}

/* XenStore写操作 */
int av_xenstore_write(av_host_service_t* service, const char* path, const char* value) {
    if (!service || !path || !value) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_write");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Writing XenStore path: %s = %s", path, value);

    bool result = xs_write(service->xs_handle, XBT_NULL, path, value, strlen(value));
    if (!result) {
        av_log(AV_LOG_ERROR, "Failed to write XenStore path %s: %s", path, strerror(errno));
        return AV_ERROR_XENSTORE;
    }

    av_log(AV_LOG_DEBUG, "XenStore write successful: %s", path);
    return AV_SUCCESS;
}

/* 写入共享内存ID到XenStore */
int av_xenstore_write_shm_id(av_host_service_t* service, const char* vm_uuid, grant_ref_t shm_id) {
    if (!service || !vm_uuid) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_write_shm_id");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 构建XenStore路径 */
    char xenstore_path[512];
    int ret = av_build_xenstore_path(vm_uuid, AV_XENSTORE_SHM_ID_KEY, xenstore_path, sizeof(xenstore_path));
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to build XenStore path for VM %s", vm_uuid);
        return ret;
    }

    /* 将grant reference转换为字符串 */
    char shm_id_str[32];
    snprintf(shm_id_str, sizeof(shm_id_str), "%u", shm_id);

    av_log(AV_LOG_INFO, "Writing shared memory ID %u to XenStore for VM %s", shm_id, vm_uuid);

    /* 写入XenStore */
    ret = av_xenstore_write(service, xenstore_path, shm_id_str);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to write shared memory ID to XenStore");
        return ret;
    }

    av_log(AV_LOG_INFO, "Successfully wrote shared memory ID to XenStore: %s", xenstore_path);
    return AV_SUCCESS;
}

/* 检查XenStore路径是否存在 */
int av_xenstore_exists(av_host_service_t* service, const char* path) {
    if (!service || !path) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_exists");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    char** entries = xs_directory(service->xs_handle, XBT_NULL, path, NULL);
    if (entries) {
        free(entries);
        return AV_SUCCESS;
    }

    /* 尝试读取路径 */
    unsigned int len = 0;
    char* value = xs_read(service->xs_handle, XBT_NULL, path, &len);
    if (value) {
        free(value);
        return AV_SUCCESS;
    }

    return AV_ERROR_XENSTORE;
}

/* 创建XenStore目录 */
int av_xenstore_mkdir(av_host_service_t* service, const char* path) {
    if (!service || !path) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_mkdir");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Creating XenStore directory: %s", path);

    bool result = xs_mkdir(service->xs_handle, XBT_NULL, path);
    if (!result) {
        av_log(AV_LOG_ERROR, "Failed to create XenStore directory %s: %s", path, strerror(errno));
        return AV_ERROR_XENSTORE;
    }

    av_log(AV_LOG_DEBUG, "XenStore directory created successfully: %s", path);
    return AV_SUCCESS;
}

/* 删除XenStore路径 */
int av_xenstore_remove(av_host_service_t* service, const char* path) {
    if (!service || !path) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_remove");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Removing XenStore path: %s", path);

    bool result = xs_rm(service->xs_handle, XBT_NULL, path);
    if (!result) {
        av_log(AV_LOG_ERROR, "Failed to remove XenStore path %s: %s", path, strerror(errno));
        return AV_ERROR_XENSTORE;
    }

    av_log(AV_LOG_DEBUG, "XenStore path removed successfully: %s", path);
    return AV_SUCCESS;
}

/* 设置XenStore路径权限 */
int av_xenstore_set_permissions(av_host_service_t* service, const char* path, domid_t domid) {
    if (!service || !path) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_set_permissions");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Setting XenStore permissions for path %s, domain %u", path, domid);

    /* 设置权限：dom0可读写，目标域可读 */
    struct xs_permissions perms[2];
    
    /* dom0权限 */
    perms[0].id = 0;
    perms[0].perms = XS_PERM_READ | XS_PERM_WRITE;
    
    /* 目标域权限 */
    perms[1].id = domid;
    perms[1].perms = XS_PERM_READ;

    bool result = xs_set_permissions(service->xs_handle, XBT_NULL, path, perms, 2);
    if (!result) {
        av_log(AV_LOG_ERROR, "Failed to set XenStore permissions for %s: %s", path, strerror(errno));
        return AV_ERROR_XENSTORE;
    }

    av_log(AV_LOG_DEBUG, "XenStore permissions set successfully for %s", path);
    return AV_SUCCESS;
}

/* 监听XenStore路径变化 */
int av_xenstore_watch(av_host_service_t* service, const char* path, const char* token) {
    if (!service || !path || !token) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_watch");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Setting up XenStore watch for path: %s", path);

    bool result = xs_watch(service->xs_handle, path, token);
    if (!result) {
        av_log(AV_LOG_ERROR, "Failed to set up XenStore watch for %s: %s", path, strerror(errno));
        return AV_ERROR_XENSTORE;
    }

    av_log(AV_LOG_DEBUG, "XenStore watch set up successfully for %s", path);
    return AV_SUCCESS;
}

/* 取消XenStore路径监听 */
int av_xenstore_unwatch(av_host_service_t* service, const char* path, const char* token) {
    if (!service || !path || !token) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_unwatch");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Removing XenStore watch for path: %s", path);

    bool result = xs_unwatch(service->xs_handle, path, token);
    if (!result) {
        av_log(AV_LOG_ERROR, "Failed to remove XenStore watch for %s: %s", path, strerror(errno));
        return AV_ERROR_XENSTORE;
    }

    av_log(AV_LOG_DEBUG, "XenStore watch removed successfully for %s", path);
    return AV_SUCCESS;
}

/* 初始化VM的XenStore结构 */
int av_xenstore_init_vm_structure(av_host_service_t* service, const char* vm_uuid, domid_t domid) {
    if (!service || !vm_uuid) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_init_vm_structure");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_INFO, "Initializing XenStore structure for VM %s (domain %u)", vm_uuid, domid);

    /* 构建基础路径 */
    char base_path[512];
    snprintf(base_path, sizeof(base_path), "%s/%s%s", 
             AV_XENSTORE_BASE_PATH, vm_uuid, AV_XENSTORE_DATA_PATH);

    /* 创建data目录 */
    int ret = av_xenstore_mkdir(service, base_path);
    if (ret != AV_SUCCESS && ret != AV_ERROR_ALREADY_EXISTS) {
        av_log(AV_LOG_ERROR, "Failed to create XenStore data directory for VM %s", vm_uuid);
        return ret;
    }

    /* 设置目录权限 */
    ret = av_xenstore_set_permissions(service, base_path, domid);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_WARN, "Failed to set permissions for XenStore directory %s", base_path);
        /* 继续执行，权限设置失败不是致命错误 */
    }

    av_log(AV_LOG_INFO, "XenStore structure initialized successfully for VM %s", vm_uuid);
    return AV_SUCCESS;
}
/* Xe
nStore连接管理和错误重试机制 */
int av_xenstore_reconnect(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_INFO, "Attempting to reconnect to XenStore...");

    /* 关闭现有连接 */
    if (service->xs_handle) {
        xs_close(service->xs_handle);
        service->xs_handle = NULL;
    }

    /* 重新打开连接 */
    service->xs_handle = xs_open(0);
    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "Failed to reconnect to XenStore: %s", strerror(errno));
        return AV_ERROR_XENSTORE;
    }

    av_log(AV_LOG_INFO, "Successfully reconnected to XenStore");
    return AV_SUCCESS;
}

/* 带重试的XenStore读操作 */
int av_xenstore_read_retry(av_host_service_t* service, const char* path, char* buffer, size_t buffer_size, int max_retries) {
    if (!service || !path || !buffer || buffer_size == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_read_retry");
        return AV_ERROR_INVALID_PARAM;
    }

    int attempt = 0;
    int ret = AV_ERROR_XENSTORE;

    for (attempt = 0; attempt <= max_retries; attempt++) {
        if (attempt > 0) {
            av_log(AV_LOG_DEBUG, "XenStore read retry attempt %d/%d for path: %s", attempt, max_retries, path);
            
            /* 尝试重新连接 */
            if (av_xenstore_reconnect(service) != AV_SUCCESS) {
                continue;
            }
            
            /* 短暂延迟 */
            usleep(100000); /* 100ms */
        }

        ret = av_xenstore_read(service, path, buffer, buffer_size);
        if (ret == AV_SUCCESS) {
            if (attempt > 0) {
                av_log(AV_LOG_INFO, "XenStore read succeeded after %d retries", attempt);
            }
            return AV_SUCCESS;
        }
    }

    av_log(AV_LOG_ERROR, "XenStore read failed after %d attempts: %s", attempt, av_error_string(ret));
    return ret;
}

/* 带重试的XenStore写操作 */
int av_xenstore_write_retry(av_host_service_t* service, const char* path, const char* value, int max_retries) {
    if (!service || !path || !value) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_write_retry");
        return AV_ERROR_INVALID_PARAM;
    }

    int attempt = 0;
    int ret = AV_ERROR_XENSTORE;

    for (attempt = 0; attempt <= max_retries; attempt++) {
        if (attempt > 0) {
            av_log(AV_LOG_DEBUG, "XenStore write retry attempt %d/%d for path: %s", attempt, max_retries, path);
            
            /* 尝试重新连接 */
            if (av_xenstore_reconnect(service) != AV_SUCCESS) {
                continue;
            }
            
            /* 短暂延迟 */
            usleep(100000); /* 100ms */
        }

        ret = av_xenstore_write(service, path, value);
        if (ret == AV_SUCCESS) {
            if (attempt > 0) {
                av_log(AV_LOG_INFO, "XenStore write succeeded after %d retries", attempt);
            }
            return AV_SUCCESS;
        }
    }

    av_log(AV_LOG_ERROR, "XenStore write failed after %d attempts: %s", attempt, av_error_string(ret));
    return ret;
}

/* 批量XenStore操作 */

int av_xenstore_batch_operations(av_host_service_t* service, av_xenstore_batch_op_t* operations, int count) {
    if (!service || !operations || count <= 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_batch_operations");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Executing %d XenStore batch operations", count);

    /* 开始事务 */
    xs_transaction_t trans = xs_transaction_start(service->xs_handle);
    if (trans == XBT_NULL) {
        av_log(AV_LOG_ERROR, "Failed to start XenStore transaction: %s", strerror(errno));
        return AV_ERROR_XENSTORE;
    }

    int success_count = 0;
    int ret = AV_SUCCESS;

    /* 执行批量操作 */
    for (int i = 0; i < count; i++) {
        av_xenstore_batch_op_t* op = &operations[i];
        bool op_success = false;

        switch (op->operation) {
            case 0: /* 读操作 */
                {
                    unsigned int len = 0;
                    char* value = xs_read(service->xs_handle, trans, op->path, &len);
                    if (value) {
                        /* 将读取的值存储回操作结构 */
                        if (op->value) {
                            free(op->value);
                        }
                        op->value = value;
                        op_success = true;
                    }
                }
                break;

            case 1: /* 写操作 */
                op_success = xs_write(service->xs_handle, trans, op->path, op->value, strlen(op->value));
                break;

            case 2: /* 删除操作 */
                op_success = xs_rm(service->xs_handle, trans, op->path);
                break;

            default:
                av_log(AV_LOG_ERROR, "Invalid operation type: %d", op->operation);
                ret = AV_ERROR_INVALID_PARAM;
                break;
        }

        if (op_success) {
            success_count++;
            av_log(AV_LOG_DEBUG, "Batch operation %d succeeded: %s", i, op->path);
        } else {
            av_log(AV_LOG_WARN, "Batch operation %d failed: %s", i, op->path);
        }
    }

    /* 提交或回滚事务 */
    if (ret == AV_SUCCESS && success_count == count) {
        if (xs_transaction_end(service->xs_handle, trans, false)) {
            av_log(AV_LOG_INFO, "XenStore batch operations committed successfully (%d operations)", count);
        } else {
            av_log(AV_LOG_ERROR, "Failed to commit XenStore transaction: %s", strerror(errno));
            ret = AV_ERROR_XENSTORE;
        }
    } else {
        /* 回滚事务 */
        xs_transaction_end(service->xs_handle, trans, true);
        av_log(AV_LOG_WARN, "XenStore batch operations rolled back (succeeded: %d/%d)", success_count, count);
        if (ret == AV_SUCCESS) {
            ret = AV_ERROR_XENSTORE;
        }
    }

    return ret;
}

/* XenStore路径枚举 */
int av_xenstore_list_directory(av_host_service_t* service, const char* path, char*** entries, unsigned int* count) {
    if (!service || !path || !entries || !count) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_list_directory");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Listing XenStore directory: %s", path);

    *entries = xs_directory(service->xs_handle, XBT_NULL, path, count);
    if (!*entries) {
        av_log(AV_LOG_DEBUG, "Failed to list XenStore directory %s: %s", path, strerror(errno));
        *count = 0;
        return AV_ERROR_XENSTORE;
    }

    av_log(AV_LOG_DEBUG, "Found %u entries in XenStore directory %s", *count, path);
    return AV_SUCCESS;
}

/* 释放目录列表内存 */
void av_xenstore_free_directory_list(char** entries, unsigned int count) {
    if (entries) {
        for (unsigned int i = 0; i < count; i++) {
            free(entries[i]);
        }
        free(entries);
    }
}

/* XenStore路径递归遍历 */
int av_xenstore_walk_tree(av_host_service_t* service, const char* root_path, 
                         void (*callback)(const char* path, const char* value, void* user_data), 
                         void* user_data, int max_depth) {
    if (!service || !root_path || !callback || max_depth < 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_walk_tree");
        return AV_ERROR_INVALID_PARAM;
    }

    if (max_depth == 0) {
        return AV_SUCCESS; /* 达到最大深度 */
    }

    av_log(AV_LOG_DEBUG, "Walking XenStore tree: %s (depth: %d)", root_path, max_depth);

    /* 读取当前路径的值 */
    char value[1024];
    if (av_xenstore_read(service, root_path, value, sizeof(value)) == AV_SUCCESS) {
        callback(root_path, value, user_data);
    }

    /* 列出子目录 */
    char** entries = NULL;
    unsigned int count = 0;
    
    if (av_xenstore_list_directory(service, root_path, &entries, &count) == AV_SUCCESS) {
        for (unsigned int i = 0; i < count; i++) {
            char child_path[1024];
            snprintf(child_path, sizeof(child_path), "%s/%s", root_path, entries[i]);
            
            /* 递归遍历子路径 */
            av_xenstore_walk_tree(service, child_path, callback, user_data, max_depth - 1);
        }
        
        av_xenstore_free_directory_list(entries, count);
    }

    return AV_SUCCESS;
}

/* XenStore监听事件处理 */
typedef struct {
    av_host_service_t* service;
    char* watch_path;
    char* watch_token;
    void (*callback)(const char* path, void* user_data);
    void* user_data;
    int active;
} av_xenstore_watch_context_t;

void* av_xenstore_watch_thread(void* arg) {
    av_xenstore_watch_context_t* ctx = (av_xenstore_watch_context_t*)arg;
    
    if (!ctx || !ctx->service || !ctx->service->xs_handle) {
        av_log(AV_LOG_ERROR, "Invalid watch context");
        return NULL;
    }

    av_log(AV_LOG_INFO, "XenStore watch thread started for path: %s", ctx->watch_path);

    char** watch_events;
    unsigned int num_events;

    while (ctx->active) {
        /* 等待XenStore事件 */
        watch_events = xs_read_watch(ctx->service->xs_handle, &num_events);
        if (!watch_events) {
            if (errno == EINTR) {
                continue; /* 被信号中断，继续等待 */
            }
            av_log(AV_LOG_ERROR, "XenStore watch read failed: %s", strerror(errno));
            break;
        }

        /* 处理事件 */
        for (unsigned int i = 0; i < num_events; i += 2) {
            const char* path = watch_events[i];
            const char* token = watch_events[i + 1];

            if (strcmp(token, ctx->watch_token) == 0) {
                av_log(AV_LOG_DEBUG, "XenStore watch event: %s", path);
                if (ctx->callback) {
                    ctx->callback(path, ctx->user_data);
                }
            }
        }

        /* 释放事件内存 */
        free(watch_events);
    }

    av_log(AV_LOG_INFO, "XenStore watch thread stopped for path: %s", ctx->watch_path);
    return NULL;
}

/* 启动XenStore监听 */
int av_xenstore_start_watch(av_host_service_t* service, const char* path, const char* token,
                           void (*callback)(const char* path, void* user_data), void* user_data,
                           pthread_t* watch_thread) {
    if (!service || !path || !token || !callback || !watch_thread) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_start_watch");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 设置监听 */
    int ret = av_xenstore_watch(service, path, token);
    if (ret != AV_SUCCESS) {
        return ret;
    }

    /* 创建监听上下文 */
    av_xenstore_watch_context_t* ctx = malloc(sizeof(av_xenstore_watch_context_t));
    if (!ctx) {
        av_xenstore_unwatch(service, path, token);
        return AV_ERROR_MEMORY_ALLOC;
    }

    ctx->service = service;
    ctx->watch_path = strdup(path);
    ctx->watch_token = strdup(token);
    ctx->callback = callback;
    ctx->user_data = user_data;
    ctx->active = 1;

    /* 启动监听线程 */
    ret = pthread_create(watch_thread, NULL, av_xenstore_watch_thread, ctx);
    if (ret != 0) {
        av_log(AV_LOG_ERROR, "Failed to create watch thread: %s", strerror(ret));
        free(ctx->watch_path);
        free(ctx->watch_token);
        free(ctx);
        av_xenstore_unwatch(service, path, token);
        return AV_ERROR_MEMORY_ALLOC;
    }

    av_log(AV_LOG_INFO, "XenStore watch started for path: %s", path);
    return AV_SUCCESS;
}

/* 停止XenStore监听 */
int av_xenstore_stop_watch(av_host_service_t* service, const char* path, const char* token, pthread_t watch_thread) {
    if (!service || !path || !token) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_stop_watch");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_INFO, "Stopping XenStore watch for path: %s", path);

    /* 取消监听 */
    av_xenstore_unwatch(service, path, token);

    /* 等待线程结束 */
    pthread_join(watch_thread, NULL);

    av_log(AV_LOG_INFO, "XenStore watch stopped for path: %s", path);
    return AV_SUCCESS;
}

/* XenStore健康检查 */
int av_xenstore_health_check(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Performing XenStore health check");

    /* 尝试读取一个已知存在的路径 */
    char buffer[64];
    int ret = av_xenstore_read(service, "/", buffer, sizeof(buffer));
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_WARN, "XenStore health check failed: %s", av_error_string(ret));
        return ret;
    }

    av_log(AV_LOG_DEBUG, "XenStore health check passed");
    return AV_SUCCESS;
}

/* XenStore性能统计 */
typedef struct {
    uint64_t read_operations;
    uint64_t write_operations;
    uint64_t failed_operations;
    uint64_t total_bytes_read;
    uint64_t total_bytes_written;
    time_t start_time;
} av_xenstore_stats_t;

static av_xenstore_stats_t g_xenstore_stats = {0};

void av_xenstore_stats_init(void) {
    memset(&g_xenstore_stats, 0, sizeof(g_xenstore_stats));
    g_xenstore_stats.start_time = time(NULL);
    av_log(AV_LOG_INFO, "XenStore statistics initialized");
}

void av_xenstore_stats_record_read(size_t bytes, int success) {
    g_xenstore_stats.read_operations++;
    if (success) {
        g_xenstore_stats.total_bytes_read += bytes;
    } else {
        g_xenstore_stats.failed_operations++;
    }
}

void av_xenstore_stats_record_write(size_t bytes, int success) {
    g_xenstore_stats.write_operations++;
    if (success) {
        g_xenstore_stats.total_bytes_written += bytes;
    } else {
        g_xenstore_stats.failed_operations++;
    }
}

void av_xenstore_stats_report(void) {
    time_t current_time = time(NULL);
    double uptime = difftime(current_time, g_xenstore_stats.start_time);
    
    av_log(AV_LOG_INFO, "=== XenStore Statistics ===");
    av_log(AV_LOG_INFO, "Uptime: %.0f seconds", uptime);
    av_log(AV_LOG_INFO, "Read operations: %lu", g_xenstore_stats.read_operations);
    av_log(AV_LOG_INFO, "Write operations: %lu", g_xenstore_stats.write_operations);
    av_log(AV_LOG_INFO, "Failed operations: %lu", g_xenstore_stats.failed_operations);
    av_log(AV_LOG_INFO, "Total bytes read: %lu", g_xenstore_stats.total_bytes_read);
    av_log(AV_LOG_INFO, "Total bytes written: %lu", g_xenstore_stats.total_bytes_written);
    
    uint64_t total_ops = g_xenstore_stats.read_operations + g_xenstore_stats.write_operations;
    if (total_ops > 0) {
        double success_rate = (double)(total_ops - g_xenstore_stats.failed_operations) * 100.0 / total_ops;
        av_log(AV_LOG_INFO, "Success rate: %.2f%%", success_rate);
    }
    
    if (uptime > 0) {
        double ops_per_second = total_ops / uptime;
        av_log(AV_LOG_INFO, "Operations per second: %.2f", ops_per_second);
    }
}