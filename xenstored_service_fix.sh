#!/bin/bash

# XenServer防病毒系统 - xenstored服务修复脚本
# 专门解决xenstored服务启动失败问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "=================================================================="
echo "XenServer防病毒系统 - xenstored服务诊断和修复"
echo "=================================================================="

# 检查root权限
if [ $(id -u) -ne 0 ]; then
    log_error "必须以root权限运行"
    echo "使用方法: sudo $0"
    exit 1
fi

log_info "开始xenstored服务诊断..."

# 1. 检查当前xenstored状态
log_info "检查xenstored服务状态..."
echo "systemctl状态:"
systemctl status xenstored --no-pager -l || true
echo ""

echo "进程状态:"
ps aux | grep xenstored | grep -v grep || echo "未找到xenstored进程"
echo ""

# 2. 检查Xen hypervisor状态
log_info "检查Xen hypervisor状态..."
if [ -f "/proc/xen/capabilities" ]; then
    log_success "Xen hypervisor已加载"
    echo "Xen capabilities: $(cat /proc/xen/capabilities)"
else
    log_error "Xen hypervisor未加载或不可用"
    echo "这可能是问题的根源"
fi
echo ""

# 3. 检查Xen模块
log_info "检查Xen内核模块..."
echo "已加载的Xen模块:"
lsmod | grep xen || echo "未找到Xen模块"
echo ""

# 4. 检查/proc/xen目录
log_info "检查/proc/xen目录..."
if [ -d "/proc/xen" ]; then
    log_success "/proc/xen目录存在"
    echo "目录内容:"
    ls -la /proc/xen/ || true
else
    log_error "/proc/xen目录不存在"
fi
echo ""

# 5. 检查/dev/xen目录
log_info "检查/dev/xen目录..."
if [ -d "/dev/xen" ]; then
    log_success "/dev/xen目录存在"
    echo "目录内容:"
    ls -la /dev/xen/ || true
else
    log_error "/dev/xen目录不存在"
    log_info "尝试创建/dev/xen目录..."
    mkdir -p /dev/xen
    if [ -d "/dev/xen" ]; then
        log_success "/dev/xen目录已创建"
    else
        log_error "无法创建/dev/xen目录"
    fi
fi
echo ""

# 6. 检查xenstored配置
log_info "检查xenstored配置..."
XENSTORED_CONFIG="/etc/default/xenstored"
if [ -f "$XENSTORED_CONFIG" ]; then
    echo "xenstored配置文件存在: $XENSTORED_CONFIG"
    echo "配置内容:"
    cat "$XENSTORED_CONFIG" || true
else
    log_warning "xenstored配置文件不存在: $XENSTORED_CONFIG"
fi
echo ""

# 7. 检查systemd服务文件
log_info "检查systemd服务文件..."
SERVICE_FILE="/lib/systemd/system/xenstored.service"
if [ -f "$SERVICE_FILE" ]; then
    log_success "xenstored服务文件存在"
    echo "服务文件内容:"
    cat "$SERVICE_FILE" || true
else
    log_error "xenstored服务文件不存在: $SERVICE_FILE"
    # 查找其他可能的位置
    for path in "/usr/lib/systemd/system/xenstored.service" "/etc/systemd/system/xenstored.service"; do
        if [ -f "$path" ]; then
            log_info "找到服务文件: $path"
            cat "$path" || true
            break
        fi
    done
fi
echo ""

# 8. 尝试手动启动xenstored
log_info "尝试手动启动xenstored..."
if command -v xenstored >/dev/null 2>&1; then
    log_info "xenstored命令存在，尝试手动启动..."
    
    # 停止可能存在的xenstored进程
    pkill -f xenstored 2>/dev/null || true
    sleep 2
    
    # 尝试不同的启动方式
    echo "尝试方式1: 直接启动xenstored"
    timeout 10s xenstored --no-fork --verbose 2>&1 | head -10 || true
    echo ""
    
    echo "尝试方式2: 使用systemctl启动"
    systemctl start xenstored 2>&1 || true
    sleep 3
    
    echo "尝试方式3: 使用service命令启动"
    service xenstored start 2>&1 || true
    sleep 3
    
    # 检查启动结果
    if pgrep -x "xenstored" > /dev/null; then
        log_success "xenstored已成功启动"
        echo "xenstored PID: $(pgrep -x xenstored)"
    else
        log_error "xenstored启动失败"
    fi
else
    log_error "xenstored命令不存在"
    echo "查找xenstored二进制文件..."
    find /usr -name "xenstored" 2>/dev/null || echo "未找到xenstored"
fi
echo ""

# 9. 检查日志
log_info "检查相关日志..."
echo "systemd日志:"
journalctl -u xenstored --no-pager -l | tail -20 || true
echo ""

echo "系统日志中的xen相关信息:"
dmesg | grep -i xen | tail -10 || echo "未找到xen相关日志"
echo ""

echo "syslog中的xenstored信息:"
grep -i xenstored /var/log/messages 2>/dev/null | tail -10 || \
grep -i xenstored /var/log/syslog 2>/dev/null | tail -10 || \
echo "未找到xenstored日志"
echo ""

# 10. 提供修复建议
log_info "修复建议:"
echo ""
echo "基于诊断结果，请尝试以下解决方案："
echo ""

if [ ! -f "/proc/xen/capabilities" ]; then
    echo "1. Xen hypervisor问题:"
    echo "   - 检查系统是否在Xen hypervisor上运行"
    echo "   - 确认这是Citrix Hypervisor/XenServer宿主机"
    echo "   - 如果是虚拟机，请在宿主机上运行此程序"
    echo ""
fi

if ! command -v xenstored >/dev/null 2>&1; then
    echo "2. xenstored程序缺失:"
    echo "   - 安装Xen工具包: yum install xen-tools"
    echo "   - 或重新安装Citrix Hypervisor"
    echo ""
fi

echo "3. 服务配置问题:"
echo "   - 重新加载systemd配置: systemctl daemon-reload"
echo "   - 重置服务状态: systemctl reset-failed xenstored"
echo "   - 手动启动: /usr/sbin/xenstored --pid-file=/var/run/xenstored.pid"
echo ""

echo "4. 权限问题:"
echo "   - 检查/var/lib/xenstored目录权限"
echo "   - 创建必要目录: mkdir -p /var/lib/xenstored"
echo "   - 设置权限: chown root:root /var/lib/xenstored"
echo ""

echo "5. 系统重启:"
echo "   - 如果上述方法都无效，尝试重启系统: reboot"
echo ""

# 11. 生成诊断报告
REPORT_FILE="xenstored_diagnostic_$(date +%Y%m%d_%H%M%S).txt"
log_info "生成诊断报告: $REPORT_FILE"

cat > "$REPORT_FILE" << EOF
XenServer防病毒系统 - xenstored服务诊断报告
生成时间: $(date)
主机名: $(hostname)
系统信息: $(uname -a)

=== 系统版本 ===
$(cat /etc/redhat-release 2>/dev/null || cat /etc/os-release 2>/dev/null || echo "无法获取系统版本")

=== Xen hypervisor状态 ===
/proc/xen/capabilities: $(cat /proc/xen/capabilities 2>/dev/null || echo "不存在")
/proc/xen目录: $([ -d /proc/xen ] && echo "存在" || echo "不存在")
/dev/xen目录: $([ -d /dev/xen ] && echo "存在" || echo "不存在")

=== xenstored状态 ===
systemctl状态:
$(systemctl status xenstored --no-pager -l 2>&1)

进程状态:
$(ps aux | grep xenstored | grep -v grep || echo "未找到进程")

=== Xen模块 ===
$(lsmod | grep xen || echo "未找到Xen模块")

=== 日志信息 ===
systemd日志:
$(journalctl -u xenstored --no-pager -l | tail -20 2>/dev/null || echo "无法获取日志")

系统日志:
$(dmesg | grep -i xen | tail -10 || echo "无日志")
EOF

echo "=================================================================="
log_success "xenstored服务诊断完成！"
echo "=================================================================="
echo ""
echo "请查看诊断报告: $REPORT_FILE"
echo "根据上述建议进行修复，或联系技术支持"
