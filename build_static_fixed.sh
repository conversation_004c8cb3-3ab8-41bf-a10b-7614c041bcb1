#!/bin/bash

# XenServer防病毒系统 - 静态编译脚本（修复版）
# 包含信号处理和权限检查修复

set -e  # 遇到错误立即退出

echo "=== XenServer Antivirus Static Build (Fixed Version) ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查编译环境
check_build_environment() {
    log_info "检查编译环境..."
    
    # 检查gcc
    if ! command -v gcc &> /dev/null; then
        log_error "gcc未安装，请安装gcc编译器"
        exit 1
    fi
    
    # 检查make
    if ! command -v make &> /dev/null; then
        log_error "make未安装，请安装make工具"
        exit 1
    fi
    
    # 检查静态库支持
    if ! gcc -static -xc -c /dev/null -o /dev/null 2>/dev/null; then
        log_warning "静态编译可能不被支持，但继续尝试..."
    fi
    
    log_success "编译环境检查完成"
}

# 清理之前的构建
clean_previous_builds() {
    log_info "清理之前的构建..."
    
    cd host
    make -f Makefile.static-fixed clean 2>/dev/null || true
    cd ../linux-client
    make -f Makefile.static-fixed clean 2>/dev/null || true
    cd ..
    
    log_success "清理完成"
}

# 编译宿主机服务
build_host_service() {
    log_info "编译宿主机服务（静态版本，包含修复）..."
    
    cd host
    
    # 显示编译配置
    make -f Makefile.static-fixed info
    
    # 开始编译
    if make -f Makefile.static-fixed; then
        log_success "宿主机服务编译成功"
        
        # 显示编译结果
        if [ -f "xenserver-antivirus-host-static-fixed" ]; then
            log_info "编译结果："
            ls -lh xenserver-antivirus-host-static-fixed
            file xenserver-antivirus-host-static-fixed
            
            # 测试可执行性
            if ./xenserver-antivirus-host-static-fixed --help >/dev/null 2>&1; then
                log_success "宿主机服务可执行性测试通过"
            else
                log_warning "宿主机服务可执行性测试失败，但文件已生成"
            fi
        fi
    else
        log_error "宿主机服务编译失败"
        cd ..
        exit 1
    fi
    
    cd ..
}

# 编译Linux客户端
build_linux_client() {
    log_info "编译Linux客户端（静态版本，包含修复）..."
    
    cd linux-client
    
    # 显示编译配置
    make -f Makefile.static-fixed info
    
    # 开始编译
    if make -f Makefile.static-fixed; then
        log_success "Linux客户端编译成功"
        
        # 显示编译结果
        if [ -f "xenserver-antivirus-client-static-fixed" ]; then
            log_info "编译结果："
            ls -lh xenserver-antivirus-client-static-fixed
            file xenserver-antivirus-client-static-fixed
            
            # 测试可执行性
            if ./xenserver-antivirus-client-static-fixed --help >/dev/null 2>&1; then
                log_success "Linux客户端可执行性测试通过"
            else
                log_warning "Linux客户端可执行性测试失败，但文件已生成"
            fi
        fi
    else
        log_error "Linux客户端编译失败"
        cd ..
        exit 1
    fi
    
    cd ..
}

# 创建发布包
create_release_package() {
    log_info "创建发布包..."
    
    # 创建发布目录
    RELEASE_DIR="release-static-fixed"
    rm -rf "$RELEASE_DIR"
    mkdir -p "$RELEASE_DIR"
    
    # 复制可执行文件
    if [ -f "host/xenserver-antivirus-host-static-fixed" ]; then
        cp host/xenserver-antivirus-host-static-fixed "$RELEASE_DIR/"
        log_success "宿主机服务已添加到发布包"
    fi
    
    if [ -f "linux-client/xenserver-antivirus-client-static-fixed" ]; then
        cp linux-client/xenserver-antivirus-client-static-fixed "$RELEASE_DIR/"
        log_success "Linux客户端已添加到发布包"
    fi
    
    # 复制文档
    [ -f "docs/INSTALL.md" ] && cp docs/INSTALL.md "$RELEASE_DIR/"
    [ -f "docs/COMMUNICATION.md" ] && cp docs/COMMUNICATION.md "$RELEASE_DIR/"
    [ -f "BUILD_SUMMARY.md" ] && cp BUILD_SUMMARY.md "$RELEASE_DIR/"
    
    # 创建README
    cat > "$RELEASE_DIR/README-FIXED.md" << 'EOF'
# XenServer Antivirus Static Build (Fixed Version)

这是XenServer防病毒系统的静态编译版本，包含以下修复：

## 修复内容

### 1. 权限问题修复
- 改进了xc_domain_getinfolist的错误处理
- 添加了详细的权限检查和错误报告
- 提供了更清晰的错误信息和解决建议

### 2. 信号处理修复
- 修复了Ctrl+C无法终止程序的问题
- 使用可中断的nanosleep替代普通sleep
- 改进了信号处理器，支持强制退出
- 确保所有线程都能正确响应关闭信号

## 文件说明

- `xenserver-antivirus-host-static-fixed`: 宿主机服务（静态编译）
- `xenserver-antivirus-client-static-fixed`: Linux客户端（静态编译）

## 使用方法

### 宿主机服务
```bash
# 在XenServer宿主机上运行
sudo ./xenserver-antivirus-host-static-fixed

# 查看帮助
./xenserver-antivirus-host-static-fixed --help

# 测试模式
sudo ./xenserver-antivirus-host-static-fixed --test-mode
```

### Linux客户端
```bash
# 在Linux虚拟机中运行
sudo ./xenserver-antivirus-client-static-fixed

# 查看帮助
./xenserver-antivirus-client-static-fixed --help
```

## 信号处理

程序现在正确支持以下信号：
- Ctrl+C (SIGINT): 优雅关闭
- SIGTERM: 优雅关闭
- 连续3次信号: 强制退出

## 权限要求

- 必须以root权限运行
- 宿主机服务需要在XenServer Dom0中运行
- 确保用户在xen组中（如果适用）

## 故障排除

如果遇到权限问题：
1. 确保以root权限运行
2. 检查是否在正确的环境中（Dom0 vs 虚拟机）
3. 验证Xen服务状态：`systemctl status xenstored`
4. 检查设备权限：`ls -la /dev/xen/`

如果程序无法终止：
1. 尝试Ctrl+C
2. 如果无响应，连续按3次Ctrl+C强制退出
3. 或使用：`kill -TERM <pid>`
EOF

    # 创建压缩包
    tar -czf "xenserver-antivirus-static-fixed-$(date +%Y%m%d).tar.gz" "$RELEASE_DIR"
    
    log_success "发布包已创建: xenserver-antivirus-static-fixed-$(date +%Y%m%d).tar.gz"
    
    # 显示发布包内容
    log_info "发布包内容："
    ls -la "$RELEASE_DIR/"
}

# 运行测试
run_tests() {
    log_info "运行基本测试..."
    
    # 测试宿主机服务
    if [ -f "host/xenserver-antivirus-host-static-fixed" ]; then
        log_info "测试宿主机服务..."
        cd host
        if timeout 5s ./xenserver-antivirus-host-static-fixed --help >/dev/null 2>&1; then
            log_success "宿主机服务基本测试通过"
        else
            log_warning "宿主机服务测试超时或失败"
        fi
        cd ..
    fi
    
    # 测试Linux客户端
    if [ -f "linux-client/xenserver-antivirus-client-static-fixed" ]; then
        log_info "测试Linux客户端..."
        cd linux-client
        if timeout 5s ./xenserver-antivirus-client-static-fixed --help >/dev/null 2>&1; then
            log_success "Linux客户端基本测试通过"
        else
            log_warning "Linux客户端测试超时或失败"
        fi
        cd ..
    fi
}

# 显示构建摘要
show_build_summary() {
    echo
    log_info "=== 构建摘要 ==="
    
    if [ -f "host/xenserver-antivirus-host-static-fixed" ]; then
        echo "✅ 宿主机服务: host/xenserver-antivirus-host-static-fixed"
        echo "   大小: $(du -h host/xenserver-antivirus-host-static-fixed | cut -f1)"
    else
        echo "❌ 宿主机服务: 构建失败"
    fi
    
    if [ -f "linux-client/xenserver-antivirus-client-static-fixed" ]; then
        echo "✅ Linux客户端: linux-client/xenserver-antivirus-client-static-fixed"
        echo "   大小: $(du -h linux-client/xenserver-antivirus-client-static-fixed | cut -f1)"
    else
        echo "❌ Linux客户端: 构建失败"
    fi
    
    if [ -f "xenserver-antivirus-static-fixed-$(date +%Y%m%d).tar.gz" ]; then
        echo "✅ 发布包: xenserver-antivirus-static-fixed-$(date +%Y%m%d).tar.gz"
        echo "   大小: $(du -h xenserver-antivirus-static-fixed-$(date +%Y%m%d).tar.gz | cut -f1)"
    fi
    
    echo
    log_info "修复内容："
    echo "  - xc_domain_getinfolist权限问题修复"
    echo "  - Ctrl+C信号处理修复"
    echo "  - 可中断的sleep调用"
    echo "  - 改进的错误报告"
    echo "  - 强制退出机制"
    
    echo
    log_success "静态编译（修复版）完成！"
}

# 主函数
main() {
    # 检查是否在正确的目录中
    if [ ! -f "host/main.c" ] || [ ! -f "linux-client/main.c" ]; then
        log_error "请在xenserver-antivirus项目根目录中运行此脚本"
        exit 1
    fi
    
    # 执行构建步骤
    check_build_environment
    clean_previous_builds
    build_host_service
    build_linux_client
    create_release_package
    run_tests
    show_build_summary
}

# 运行主函数
main "$@"