# XenServer Agentless Antivirus

XenServer无代理防病毒解决方案，通过共享内存机制实现宿主机与虚拟机之间的高效通信。

## 项目结构

```
xenserver-antivirus/
├── host/                   # XenServer宿主机服务程序
├── linux-client/          # Linux虚拟机客户端程序
├── windows-client/         # Windows虚拟机客户端服务程序
├── common/                 # 共享头文件和定义
├── tests/                  # 测试代码
└── docs/                   # 文档
```

## 编译要求

### 宿主机服务程序
- XenServer环境
- libxc开发库
- libxenstore开发库
- GCC编译器

### Linux客户端程序
- Linux虚拟机环境
- libxenctrl开发库
- libxenstore开发库
- GCC编译器

### Windows客户端程序
- Windows虚拟机环境
- Windows Xen PV驱动
- Visual Studio或MinGW编译器

## 快速开始

1. 编译宿主机服务：
```bash
cd host
make
```

2. 编译Linux客户端：
```bash
cd linux-client
make
```

3. 编译Windows客户端：
```bash
cd windows-client
# 使用Visual Studio或CMake编译
```

## 许可证

本项目采用MIT许可证。