#include "test_common.h"
#include "../host/av_host.h"
#include <string.h>

/* 模拟的libxc和XenStore函数 */
static mock_function_t mock_xc_domain_getinfolist;
static mock_function_t mock_xs_read;
static mock_function_t mock_xs_write;

/* 测试用的虚拟机信息 */
static xc_domaininfo_t test_domains[] = {
    {.domid = 0, .flags = XEN_DOMINF_running},  /* dom0 */
    {.domid = 1, .flags = XEN_DOMINF_running},  /* 运行中的VM */
    {.domid = 2, .flags = XEN_DOMINF_shutdown}, /* 关闭的VM */
    {.domid = 3, .flags = XEN_DOMINF_running}   /* 另一个运行中的VM */
};

static const char* test_uuid1 = "12345678-1234-1234-1234-123456789abc";
static const char* test_uuid2 = "*************-4321-4321-cba987654321";

/* 模拟xc_domain_getinfolist函数 */
int mock_xc_domain_getinfolist_impl(void* params) {
    (void)params; /* 未使用的参数 */
    
    if (mock_xc_domain_getinfolist.should_fail) {
        return -1;
    }
    
    mock_xc_domain_getinfolist.call_count++;
    return 4; /* 返回域的数量 */
}

/* 模拟xs_read函数 */
char* mock_xs_read_impl(struct xs_handle* h, xs_transaction_t t, const char* path, unsigned int* len) {
    (void)h; (void)t; /* 未使用的参数 */
    
    if (mock_xs_read.should_fail) {
        return NULL;
    }
    
    mock_xs_read.call_count++;
    
    /* 根据路径返回不同的值 */
    if (strstr(path, "/local/domain/1/vm")) {
        char* result = malloc(32);
        strcpy(result, "/vm/test-vm-1");
        *len = strlen(result);
        return result;
    } else if (strstr(path, "/vm/test-vm-1/uuid")) {
        char* result = malloc(37);
        strcpy(result, test_uuid1);
        *len = strlen(result);
        return result;
    } else if (strstr(path, "/local/domain/3/vm")) {
        char* result = malloc(32);
        strcpy(result, "/vm/test-vm-3");
        *len = strlen(result);
        return result;
    } else if (strstr(path, "/vm/test-vm-3/uuid")) {
        char* result = malloc(37);
        strcpy(result, test_uuid2);
        *len = strlen(result);
        return result;
    }
    
    return NULL;
}

/* 测试UUID验证功能 */
int test_uuid_validation(void) {
    /* 测试有效的UUID */
    TEST_ASSERT_EQUAL(AV_SUCCESS, av_validate_uuid(test_uuid1), "Valid UUID should pass validation");
    
    /* 测试无效的UUID */
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_validate_uuid("invalid-uuid"), "Invalid UUID should fail validation");
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_validate_uuid(""), "Empty UUID should fail validation");
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_validate_uuid(NULL), "NULL UUID should fail validation");
    
    /* 测试长度错误的UUID */
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_validate_uuid("12345678-1234-1234-1234-123456789ab"), "Short UUID should fail validation");
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_validate_uuid("12345678-1234-1234-1234-123456789abcd"), "Long UUID should fail validation");
    
    return 0;
}

/* 测试XenStore路径构建 */
int test_xenstore_path_building(void) {
    char path[512];
    
    /* 测试正常路径构建 */
    int ret = av_build_xenstore_path(test_uuid1, AV_XENSTORE_SHM_ID_KEY, path, sizeof(path));
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Path building should succeed");
    
    char expected_path[512];
    snprintf(expected_path, sizeof(expected_path), "/guest/%s/data/av_shm_id", test_uuid1);
    TEST_ASSERT_STRING_EQUAL(expected_path, path, "Built path should match expected");
    
    /* 测试缓冲区太小的情况 */
    char small_buffer[10];
    ret = av_build_xenstore_path(test_uuid1, AV_XENSTORE_SHM_ID_KEY, small_buffer, sizeof(small_buffer));
    TEST_ASSERT_EQUAL(AV_ERROR_MEMORY_ALLOC, ret, "Small buffer should cause error");
    
    /* 测试无效参数 */
    ret = av_build_xenstore_path(NULL, AV_XENSTORE_SHM_ID_KEY, path, sizeof(path));
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "NULL UUID should cause error");
    
    return 0;
}

/* 测试错误处理 */
int test_error_handling(void) {
    /* 测试错误字符串获取 */
    const char* error_str = av_error_string(AV_SUCCESS);
    TEST_ASSERT(error_str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Success", error_str, "Success error string should match");
    
    error_str = av_error_string(AV_ERROR_INVALID_PARAM);
    TEST_ASSERT(error_str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Invalid parameter", error_str, "Invalid parameter error string should match");
    
    /* 测试重试判断 */
    TEST_ASSERT_EQUAL(1, av_should_retry_error(AV_ERROR_XENSTORE), "XenStore error should be retryable");
    TEST_ASSERT_EQUAL(1, av_should_retry_error(AV_ERROR_TIMEOUT), "Timeout error should be retryable");
    TEST_ASSERT_EQUAL(0, av_should_retry_error(AV_ERROR_INVALID_PARAM), "Invalid parameter error should not be retryable");
    TEST_ASSERT_EQUAL(0, av_should_retry_error(AV_ERROR_PERMISSION), "Permission error should not be retryable");
    
    return 0;
}

/* 测试操作统计 */
int test_operation_stats(void) {
    /* 初始化统计 */
    av_stats_init();
    
    /* 记录一些操作 */
    av_stats_record_operation(1, 0); /* 成功，无重试 */
    av_stats_record_operation(1, 2); /* 成功，2次重试 */
    av_stats_record_operation(0, 3); /* 失败，3次重试 */
    
    /* 生成报告（这里只是确保不会崩溃） */
    av_stats_report();
    
    return 0;
}

/* 简单的重试操作测试函数 */
static int retry_test_counter = 0;
static int retry_test_operation(void* params) {
    (void)params;
    retry_test_counter++;
    
    if (retry_test_counter < 3) {
        return AV_ERROR_TIMEOUT; /* 前两次失败 */
    }
    
    return AV_SUCCESS; /* 第三次成功 */
}

/* 测试重试机制 */
int test_retry_mechanism(void) {
    /* 重置计数器 */
    retry_test_counter = 0;
    
    /* 测试重试操作 */
    int ret = av_retry_operation(retry_test_operation, NULL, 5, 1);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Retry operation should eventually succeed");
    TEST_ASSERT_EQUAL(3, retry_test_counter, "Operation should be called 3 times");
    
    /* 测试重试次数不足的情况 */
    retry_test_counter = 0;
    ret = av_retry_operation(retry_test_operation, NULL, 1, 1);
    TEST_ASSERT_EQUAL(AV_ERROR_TIMEOUT, ret, "Retry operation should fail with insufficient retries");
    TEST_ASSERT_EQUAL(2, retry_test_counter, "Operation should be called 2 times");
    
    return 0;
}

/* 主测试函数 */
int main(void) {
    test_init();
    
    /* 初始化模拟函数 */
    mock_reset(&mock_xc_domain_getinfolist);
    mock_reset(&mock_xs_read);
    mock_reset(&mock_xs_write);
    
    /* 运行测试 */
    RUN_TEST(test_uuid_validation);
    RUN_TEST(test_xenstore_path_building);
    RUN_TEST(test_error_handling);
    RUN_TEST(test_operation_stats);
    RUN_TEST(test_retry_mechanism);
    
    /* 输出测试结果 */
    test_summary();
    
    return (tests_failed > 0) ? 1 : 0;
}