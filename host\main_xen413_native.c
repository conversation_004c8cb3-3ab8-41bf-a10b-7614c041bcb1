/*
 * XenServer宿主机防病毒服务 - Xen 4.13原生兼容版本
 * 专门针对Xen 4.13.x环境，使用保守的API调用
 */

#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <pthread.h>
#include <sys/mman.h>
#include <sys/select.h>
#include <sys/time.h>
#include <getopt.h>
#include <time.h>
#include <stdarg.h>

/* Xen头文件 */
#include <xenctrl.h>
#include <xenstore.h>

/* 项目头文件 */
#include "../common/av_common.h"

/* 版本信息 */
#undef AV_VERSION
#define AV_VERSION "1.0.0-xen413-native"
#define AV_BUILD_INFO "Native Xen 4.13.x Compatible"

/* 宿主机服务结构体 - 简化版本 */
typedef struct {
    xc_interface* xc_handle;        /* libxenctrl句柄 */
    struct xs_handle* xs_handle;    /* XenStore句柄 */
    pthread_t vm_monitor_thread;    /* VM监控线程 */
    volatile int shutdown_requested; /* 关闭请求标志 */
    int max_vms;                    /* 最大VM数量 */
} av_host_service_t;

/* 全局变量 */
static av_host_service_t g_service;
static volatile int g_shutdown_requested = 0;
static int g_log_level = AV_LOG_INFO;

/* 信号处理函数 */
void signal_handler(int sig) {
    switch (sig) {
        case SIGINT:
        case SIGTERM:
            av_log(AV_LOG_INFO, "Received signal %d, shutting down gracefully...", sig);
            g_shutdown_requested = 1;
            g_service.shutdown_requested = 1;
            break;
    }
}

/* 检测Xen环境版本 */
void detect_xen_version(void) {
    av_log(AV_LOG_INFO, "Detecting Xen environment...");
    
    /* 尝试读取Xen版本信息 */
    FILE* fp = fopen("/sys/hypervisor/version/major", "r");
    if (fp) {
        int major = 0;
        if (fscanf(fp, "%d", &major) == 1) {
            fclose(fp);
            fp = fopen("/sys/hypervisor/version/minor", "r");
            if (fp) {
                int minor = 0;
                if (fscanf(fp, "%d", &minor) == 1) {
                    av_log(AV_LOG_INFO, "Detected Xen version: %d.%d", major, minor);
                }
                fclose(fp);
            }
        } else {
            fclose(fp);
        }
    }
    
    av_log(AV_LOG_INFO, "Program version: %s", AV_VERSION);
    av_log(AV_LOG_INFO, "Build info: %s", AV_BUILD_INFO);
}

/* 使用xenstore-ls命令获取VM信息 - 避免直接API调用 */
int av_list_running_vms_via_xenstore(av_host_service_t* service, domid_t** domids, int* count) {
    if (!service || !domids || !count) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_list_running_vms_via_xenstore");
        return AV_ERROR_INVALID_PARAM;
    }

    *domids = NULL;
    *count = 0;

    av_log(AV_LOG_DEBUG, "Using xenstore-ls to discover VMs...");
    
    /* 使用xenstore-ls命令获取域列表 */
    FILE* fp = popen("xenstore-ls /local/domain 2>/dev/null", "r");
    if (!fp) {
        av_log(AV_LOG_ERROR, "Failed to execute xenstore-ls command");
        return AV_ERROR_XENSTORE;
    }
    
    char line[256];
    domid_t* result_domids = malloc(service->max_vms * sizeof(domid_t));
    if (!result_domids) {
        pclose(fp);
        return AV_ERROR_MEMORY_ALLOC;
    }
    
    int running_count = 0;
    
    while (fgets(line, sizeof(line), fp) && running_count < service->max_vms) {
        /* 解析域ID */
        char* endptr;
        long domid = strtol(line, &endptr, 10);
        
        if (endptr != line && domid > 0 && domid < 65536) {
            /* 跳过Dom0 */
            if (domid == 0) continue;
            
            av_log(AV_LOG_DEBUG, "Found domain from xenstore: %ld", domid);
            result_domids[running_count] = (domid_t)domid;
            running_count++;
        }
    }
    
    pclose(fp);
    
    if (running_count == 0) {
        free(result_domids);
        av_log(AV_LOG_INFO, "No running VMs found via xenstore");
        return AV_SUCCESS;
    }
    
    *domids = result_domids;
    *count = running_count;
    
    av_log(AV_LOG_DEBUG, "Successfully discovered %d VMs via xenstore", running_count);
    return AV_SUCCESS;
}

/* 使用xl命令获取VM信息 - 备用方法 */
int av_list_running_vms_via_xl(av_host_service_t* service, domid_t** domids, int* count) {
    if (!service || !domids || !count) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_list_running_vms_via_xl");
        return AV_ERROR_INVALID_PARAM;
    }

    *domids = NULL;
    *count = 0;

    av_log(AV_LOG_DEBUG, "Using xl list to discover VMs...");
    
    /* 使用xl list命令获取域列表 */
    FILE* fp = popen("xl list 2>/dev/null | grep -v 'Name\\|Domain-0' | awk '{print $2}'", "r");
    if (!fp) {
        av_log(AV_LOG_ERROR, "Failed to execute xl list command");
        return AV_ERROR_XEN_INTERFACE;
    }
    
    char line[256];
    domid_t* result_domids = malloc(service->max_vms * sizeof(domid_t));
    if (!result_domids) {
        pclose(fp);
        return AV_ERROR_MEMORY_ALLOC;
    }
    
    int running_count = 0;
    
    while (fgets(line, sizeof(line), fp) && running_count < service->max_vms) {
        /* 解析域ID */
        char* endptr;
        long domid = strtol(line, &endptr, 10);
        
        if (endptr != line && domid > 0 && domid < 65536) {
            av_log(AV_LOG_DEBUG, "Found running domain from xl: %ld", domid);
            result_domids[running_count] = (domid_t)domid;
            running_count++;
        }
    }
    
    pclose(fp);
    
    if (running_count == 0) {
        free(result_domids);
        av_log(AV_LOG_INFO, "No running VMs found via xl list");
        return AV_SUCCESS;
    }
    
    *domids = result_domids;
    *count = running_count;
    
    av_log(AV_LOG_DEBUG, "Successfully discovered %d VMs via xl list", running_count);
    return AV_SUCCESS;
}

/* 主要的VM发现函数 - 尝试多种方法 */
int av_list_running_vms(av_host_service_t* service, domid_t** domids, int* count) {
    int ret;
    
    /* 方法1: 尝试使用xenstore-ls */
    ret = av_list_running_vms_via_xenstore(service, domids, count);
    if (ret == AV_SUCCESS && *count > 0) {
        av_log(AV_LOG_DEBUG, "VM discovery via xenstore successful");
        return ret;
    }
    
    /* 方法2: 尝试使用xl list */
    ret = av_list_running_vms_via_xl(service, domids, count);
    if (ret == AV_SUCCESS) {
        av_log(AV_LOG_DEBUG, "VM discovery via xl list successful");
        return ret;
    }
    
    av_log(AV_LOG_WARN, "All VM discovery methods failed");
    return ret;
}

/* XenStore读取函数 - 使用xenstore-read命令 */
int av_xenstore_read(av_host_service_t* service, const char* path, char* buffer, size_t buffer_size) {
    if (!service || !path || !buffer || buffer_size == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_read");
        return AV_ERROR_INVALID_PARAM;
    }

    char command[512];
    snprintf(command, sizeof(command), "xenstore-read %s 2>/dev/null", path);
    
    FILE* fp = popen(command, "r");
    if (!fp) {
        av_log(AV_LOG_DEBUG, "Failed to execute xenstore-read for path: %s", path);
        return AV_ERROR_XENSTORE_READ;
    }
    
    if (fgets(buffer, buffer_size, fp)) {
        /* 移除换行符 */
        char* newline = strchr(buffer, '\n');
        if (newline) *newline = '\0';
        
        pclose(fp);
        av_log(AV_LOG_DEBUG, "Successfully read XenStore path %s: %s", path, buffer);
        return AV_SUCCESS;
    }
    
    pclose(fp);
    av_log(AV_LOG_DEBUG, "XenStore path not found or empty: %s", path);
    return AV_ERROR_XENSTORE_READ;
}

/* VM监控线程 */
void* vm_monitor_thread(void* arg) {
    av_host_service_t* service = (av_host_service_t*)arg;
    
    av_log(AV_LOG_INFO, "VM monitor thread started (using command-line tools)");
    
    while (!service->shutdown_requested) {
        av_log(AV_LOG_DEBUG, "Monitoring VMs...");
        
        /* 获取运行中的VM列表 */
        av_log(AV_LOG_DEBUG, "Getting list of running VMs...");
        domid_t* domids = NULL;
        int count = 0;
        
        int ret = av_list_running_vms(service, &domids, &count);
        if (ret == AV_SUCCESS) {
            if (count > 0) {
                av_log(AV_LOG_INFO, "Found %d running VMs", count);
                
                /* 检查每个VM的防病毒客户端状态 */
                for (int i = 0; i < count; i++) {
                    char xenstore_path[256];
                    char vm_name[128];
                    
                    /* 尝试获取VM名称 */
                    snprintf(xenstore_path, sizeof(xenstore_path), 
                            "/local/domain/%d/name", domids[i]);
                    
                    if (av_xenstore_read(service, xenstore_path, vm_name, sizeof(vm_name)) == AV_SUCCESS) {
                        av_log(AV_LOG_INFO, "VM %d name: %s", domids[i], vm_name);
                    } else {
                        av_log(AV_LOG_DEBUG, "VM %d: Unable to get name", domids[i]);
                    }
                    
                    /* 检查防病毒客户端状态 */
                    char av_status_path[512];
                    char av_status[64];
                    snprintf(av_status_path, sizeof(av_status_path), 
                            "/local/domain/%d/antivirus/status", domids[i]);
                    
                    if (av_xenstore_read(service, av_status_path, av_status, sizeof(av_status)) == AV_SUCCESS) {
                        av_log(AV_LOG_INFO, "VM %d antivirus status: %s", domids[i], av_status);
                    } else {
                        av_log(AV_LOG_DEBUG, "VM %d: No antivirus status found", domids[i]);
                    }
                }
                
                free(domids);
            } else {
                av_log(AV_LOG_INFO, "No running VMs found");
            }
        } else {
            av_log(AV_LOG_ERROR, "Failed to list running VMs: %s", 
                   ret == AV_ERROR_XEN_INTERFACE ? "Xen interface error" : 
                   ret == AV_ERROR_MEMORY_ALLOC ? "Memory allocation error" : 
                   ret == AV_ERROR_XENSTORE ? "XenStore error" : "Unknown error");
        }
        
        /* 等待5秒后再次检查 */
        for (int i = 0; i < 50 && !service->shutdown_requested; i++) {
            usleep(100000); /* 100ms */
        }
    }
    
    av_log(AV_LOG_INFO, "VM monitor thread exiting");
    return NULL;
}

/* 初始化宿主机服务 - 简化版本，避免直接API调用 */
int av_host_service_init(av_host_service_t* service) {
    if (!service) {
        return AV_ERROR_INIT;
    }

    memset(service, 0, sizeof(av_host_service_t));
    service->max_vms = 100; /* 默认最大100个VM */

    av_log(AV_LOG_INFO, "Initializing service (command-line tool mode)...");

    /* 测试xenstore-ls命令是否可用 */
    FILE* fp = popen("xenstore-ls / 2>/dev/null | head -1", "r");
    if (fp) {
        char buffer[256];
        if (fgets(buffer, sizeof(buffer), fp)) {
            av_log(AV_LOG_INFO, "XenStore access via xenstore-ls: OK");
        } else {
            av_log(AV_LOG_WARN, "XenStore access limited");
        }
        pclose(fp);
    }

    /* 测试xl命令是否可用 */
    fp = popen("xl info 2>/dev/null | grep host", "r");
    if (fp) {
        char buffer[256];
        if (fgets(buffer, sizeof(buffer), fp)) {
            av_log(AV_LOG_INFO, "Xen access via xl command: OK");
        } else {
            av_log(AV_LOG_WARN, "xl command access limited");
        }
        pclose(fp);
    }

    av_log(AV_LOG_INFO, "Host service initialized successfully (command-line mode)");
    return AV_SUCCESS;
}

/* 启动宿主机服务 */
int av_host_service_start(av_host_service_t* service) {
    if (!service) {
        return AV_ERROR_INIT;
    }

    /* 启动VM监控线程 */
    int ret = pthread_create(&service->vm_monitor_thread, NULL, vm_monitor_thread, service);
    if (ret != 0) {
        av_log(AV_LOG_ERROR, "Failed to create VM monitor thread: %s", strerror(ret));
        return AV_ERROR_THREAD_CREATE;
    }

    av_log(AV_LOG_INFO, "Host service started successfully");
    return AV_SUCCESS;
}

/* 停止宿主机服务 */
int av_host_service_stop(av_host_service_t* service) {
    if (!service) {
        return AV_ERROR_INIT;
    }

    /* 请求关闭 */
    service->shutdown_requested = 1;

    /* 等待VM监控线程结束 */
    int ret = pthread_join(service->vm_monitor_thread, NULL);
    if (ret != 0) {
        av_log(AV_LOG_WARN, "Failed to join VM monitor thread: %s", strerror(ret));
    }

    av_log(AV_LOG_INFO, "Host service stopped successfully");
    return AV_SUCCESS;
}

/* 清理宿主机服务 */
void av_host_service_cleanup(av_host_service_t* service) {
    if (!service) {
        return;
    }

    av_log(AV_LOG_INFO, "Host service cleaned up");
}

/* 显示帮助信息 */
void show_help(const char* program_name) {
    printf("XenServer Antivirus Host Service v%s\n", AV_VERSION);
    printf("Build: %s (%s %s)\n", AV_BUILD_INFO, __DATE__, __TIME__);
    printf("Usage: %s [OPTIONS]\n\n", program_name);
    printf("Options:\n");
    printf("  -h, --help           Show this help message\n");
    printf("  -v, --verbose        Enable verbose logging\n");
    printf("  -d, --daemon         Run as daemon\n");
    printf("  -l, --log-level N    Set log level (0=ERROR, 1=WARN, 2=INFO, 3=DEBUG)\n");
    printf("  -m, --max-vms N      Set maximum number of VMs to monitor (default: 100)\n");
    printf("  -f, --log-file FILE  Log to file instead of stdout\n");
    printf("  -t, --test           Run environment test and exit\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s                   Run in foreground with default settings\n", program_name);
    printf("  %s -v -l 3           Run with verbose debug logging\n", program_name);
    printf("  %s -d -f /var/log/xenserver-antivirus.log  Run as daemon with file logging\n", program_name);
    printf("  %s -t                Test Xen environment compatibility\n", program_name);
    printf("\n");
    printf("Requirements:\n");
    printf("  - Must run as root\n");
    printf("  - Must run on XenServer Dom0\n");
    printf("  - Uses command-line tools (xenstore-ls, xl) instead of direct API\n");
    printf("  - Compatible with Xen 4.13.x\n");
    printf("\n");
    printf("Native Mode Info:\n");
    printf("  - Uses xenstore-ls and xl commands for VM discovery\n");
    printf("  - Avoids problematic direct libxenctrl API calls\n");
    printf("  - Designed specifically for Xen 4.13.x environments\n");
    printf("\n");
    printf("Signal Handling:\n");
    printf("  SIGINT/SIGTERM       Graceful shutdown\n");
    printf("  SIGPIPE              Ignored\n");
}

/* 环境测试函数 */
int test_environment(void) {
    printf("=== XenServer Antivirus Environment Test (Native Mode) ===\n\n");

    detect_xen_version();

    printf("Testing command-line tool access...\n");

    /* 测试xenstore-ls */
    FILE* fp = popen("xenstore-ls /local/domain 2>/dev/null | head -3", "r");
    if (fp) {
        char buffer[256];
        int lines = 0;
        printf("✅ xenstore-ls command available:\n");
        while (fgets(buffer, sizeof(buffer), fp) && lines < 3) {
            printf("   %s", buffer);
            lines++;
        }
        pclose(fp);
    } else {
        printf("❌ xenstore-ls command not available\n");
    }

    /* 测试xl list */
    fp = popen("xl list 2>/dev/null", "r");
    if (fp) {
        char buffer[256];
        int lines = 0;
        printf("✅ xl list command available:\n");
        while (fgets(buffer, sizeof(buffer), fp) && lines < 5) {
            printf("   %s", buffer);
            lines++;
        }
        pclose(fp);
    } else {
        printf("❌ xl list command not available\n");
    }

    printf("Testing VM discovery...\n");
    av_host_service_t test_service;
    int ret = av_host_service_init(&test_service);

    if (ret != AV_SUCCESS) {
        printf("❌ Service initialization FAILED\n");
        return 1;
    }

    printf("✅ Service initialized successfully\n");

    domid_t* domids = NULL;
    int count = 0;

    ret = av_list_running_vms(&test_service, &domids, &count);
    if (ret == AV_SUCCESS) {
        printf("✅ VM discovery successful\n");
        printf("   Found %d running VMs\n", count);
        if (domids) {
            for (int i = 0; i < count; i++) {
                printf("   - Domain ID: %d\n", domids[i]);
            }
            free(domids);
        }
    } else {
        printf("❌ VM discovery FAILED\n");
        printf("   Error code: %d\n", ret);
    }

    av_host_service_cleanup(&test_service);

    printf("\n=== Environment Test Complete ===\n");
    printf("✅ System is ready for XenServer Antivirus Host Service (Native Mode)\n");

    return 0;
}

/* 主函数 */
int main(int argc, char* argv[]) {
    int daemon_mode = 0;
    int max_vms = 100;
    int test_mode = 0;
    char* log_file = NULL;

    /* 解析命令行参数 */
    static struct option long_options[] = {
        {"help", no_argument, 0, 'h'},
        {"verbose", no_argument, 0, 'v'},
        {"daemon", no_argument, 0, 'd'},
        {"log-level", required_argument, 0, 'l'},
        {"max-vms", required_argument, 0, 'm'},
        {"log-file", required_argument, 0, 'f'},
        {"test", no_argument, 0, 't'},
        {0, 0, 0, 0}
    };

    int c;
    while ((c = getopt_long(argc, argv, "hvdl:m:f:t", long_options, NULL)) != -1) {
        switch (c) {
            case 'h':
                show_help(argv[0]);
                return 0;
            case 'v':
                g_log_level = AV_LOG_DEBUG;
                break;
            case 'd':
                daemon_mode = 1;
                break;
            case 'l':
                g_log_level = atoi(optarg);
                if (g_log_level < 0 || g_log_level > 3) {
                    fprintf(stderr, "Invalid log level: %s\n", optarg);
                    return 1;
                }
                break;
            case 'm':
                max_vms = atoi(optarg);
                if (max_vms <= 0 || max_vms > 1000) {
                    fprintf(stderr, "Invalid max VMs: %s\n", optarg);
                    return 1;
                }
                break;
            case 'f':
                log_file = optarg;
                break;
            case 't':
                test_mode = 1;
                break;
            default:
                show_help(argv[0]);
                return 1;
        }
    }

    /* 测试模式 */
    if (test_mode) {
        return test_environment();
    }

    /* 重定向日志到文件 */
    if (log_file) {
        if (freopen(log_file, "a", stdout) == NULL) {
            fprintf(stderr, "Failed to open log file: %s\n", log_file);
            return 1;
        }
        if (freopen(log_file, "a", stderr) == NULL) {
            fprintf(stderr, "Failed to redirect stderr to log file\n");
            return 1;
        }
    }

    av_log(AV_LOG_INFO, "XenServer Antivirus Host Service starting...");
    av_log(AV_LOG_INFO, "Version: %s, Build: %s", AV_VERSION, AV_BUILD_INFO);
    av_log(AV_LOG_INFO, "Compiled: %s %s", __DATE__, __TIME__);
    av_log(AV_LOG_INFO, "Max VMs: %d", max_vms);

    /* 检查运行权限 */
    if (geteuid() != 0) {
        av_log(AV_LOG_ERROR, "This program must be run as root");
        return 1;
    }

    /* 检测Xen环境 */
    detect_xen_version();

    /* 守护进程化 */
    if (daemon_mode) {
        if (daemon(0, 0) != 0) {
            av_log(AV_LOG_ERROR, "Failed to daemonize: %s", strerror(errno));
            return 1;
        }
        av_log(AV_LOG_INFO, "Running as daemon");
    }

    /* 设置信号处理 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGPIPE, SIG_IGN);  /* 忽略SIGPIPE信号 */

    /* 初始化服务 */
    g_service.max_vms = max_vms;
    int ret = av_host_service_init(&g_service);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to initialize host service");
        return 1;
    }

    /* 启动服务 */
    ret = av_host_service_start(&g_service);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to start host service");
        av_host_service_cleanup(&g_service);
        return 1;
    }

    av_log(AV_LOG_INFO, "Service started successfully, monitoring VMs...");

    /* 主循环 */
    while (!g_shutdown_requested) {
        sleep(1);
    }

    av_log(AV_LOG_INFO, "Shutting down...");

    /* 停止服务 */
    av_host_service_stop(&g_service);
    av_host_service_cleanup(&g_service);

    av_log(AV_LOG_INFO, "Service stopped successfully");
    return 0;
}
