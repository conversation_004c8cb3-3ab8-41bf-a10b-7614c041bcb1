# XenServer防病毒Linux客户端 - 静态编译版本（修复版）
# 包含信号处理修复

CC = gcc
CFLAGS = -Wall -Wextra -O2 -g -std=c99 -D_GNU_SOURCE -DSIMPLE_BUILD
STATIC_CFLAGS = -static -static-libgcc
INCLUDES = -I../common -I.
LIBS = -lpthread -lrt

# 源文件
COMMON_SOURCES = ../common/av_common.c
CLIENT_SOURCES = main.c av_linux_client.c xen_mock_impl.c

# 目标文件
COMMON_OBJECTS = $(COMMON_SOURCES:.c=.o)
CLIENT_OBJECTS = $(CLIENT_SOURCES:.c=.o)
ALL_OBJECTS = $(COMMON_OBJECTS) $(CLIENT_OBJECTS)

# 目标程序
TARGET = xenserver-antivirus-client-static-fixed

# 默认目标
all: $(TARGET)

# 编译目标程序
$(TARGET): $(ALL_OBJECTS)
	@echo "Linking static client executable with fixes..."
	$(CC) $(STATIC_CFLAGS) -o $@ $(ALL_OBJECTS) $(LIBS)
	@echo "Static client build with fixes completed: $(TARGET)"
	@echo "Size: $$(du -h $(TARGET) | cut -f1)"
	@echo "Dependencies: $$(ldd $(TARGET) 2>/dev/null || echo 'Static binary - no dependencies')"

# 编译规则
%.o: %.c
	@echo "Compiling $< with fixes..."
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理
clean:
	@echo "Cleaning up..."
	rm -f $(ALL_OBJECTS) $(TARGET)
	rm -f *.o ../common/*.o

# 安装
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(TARGET)
	@echo "Installed to /usr/local/bin/$(TARGET)"

# 测试编译
test: $(TARGET)
	@echo "Testing static client binary..."
	@echo "File info:"
	file $(TARGET)
	@echo "Size:"
	ls -lh $(TARGET)
	@echo "Dependencies:"
	ldd $(TARGET) 2>/dev/null || echo "Static binary - no dependencies"
	@echo "Testing help output:"
	./$(TARGET) --help || true

# 创建发布包
package: $(TARGET)
	@echo "Creating client release package..."
	mkdir -p release
	cp $(TARGET) release/
	cp README.md release/ 2>/dev/null || echo "README.md not found"
	cp DEPLOYMENT.md release/ 2>/dev/null || echo "DEPLOYMENT.md not found"
	tar -czf xenserver-antivirus-client-static-fixed.tar.gz release/
	rm -rf release/
	@echo "Client release package created: xenserver-antivirus-client-static-fixed.tar.gz"

# 调试版本
debug: CFLAGS += -DDEBUG -g3 -O0
debug: $(TARGET)

# 性能分析版本
profile: CFLAGS += -pg -O2
profile: $(TARGET)

# 检查内存泄漏
valgrind: $(TARGET)
	valgrind --leak-check=full --show-leak-kinds=all ./$(TARGET) --test-mode

# 显示编译信息
info:
	@echo "=== Client Build Configuration ==="
	@echo "CC: $(CC)"
	@echo "CFLAGS: $(CFLAGS)"
	@echo "STATIC_CFLAGS: $(STATIC_CFLAGS)"
	@echo "INCLUDES: $(INCLUDES)"
	@echo "LIBS: $(LIBS)"
	@echo "Sources: $(CLIENT_SOURCES) $(COMMON_SOURCES)"
	@echo "Target: $(TARGET)"
	@echo "================================="

.PHONY: all clean install test package debug profile valgrind info