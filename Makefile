# XenServer无代理防病毒项目主Makefile

# 默认目标
all: host linux-client examples

# 编译宿主机服务
host:
	@echo "Building host service..."
	$(MAKE) -C host

# 编译Linux客户端
linux-client:
	@echo "Building Linux client..."
	$(MAKE) -C linux-client

# 编译示例程序
examples:
	@echo "Building examples..."
	$(MAKE) -C examples

# 编译Windows客户端（需要Windows环境和CMake）
windows-client:
	@echo "Building Windows client..."
	@if [ ! -d "windows-client/build" ]; then mkdir -p windows-client/build; fi
	cd windows-client/build && cmake .. && cmake --build .

# 清理所有编译文件
clean:
	@echo "Cleaning all build files..."
	$(MAKE) -C host clean
	$(MAKE) -C linux-client clean
	$(MAKE) -C examples clean
	@if [ -d "windows-client/build" ]; then rm -rf windows-client/build; fi

# 安装所有组件
install: install-host install-linux-client

# 安装宿主机服务
install-host:
	@echo "Installing host service..."
	$(MAKE) -C host install

# 安装Linux客户端
install-linux-client:
	@echo "Installing Linux client..."
	$(MAKE) -C linux-client install

# 卸载所有组件
uninstall: uninstall-host uninstall-linux-client

# 卸载宿主机服务
uninstall-host:
	@echo "Uninstalling host service..."
	$(MAKE) -C host uninstall

# 卸载Linux客户端
uninstall-linux-client:
	@echo "Uninstalling Linux client..."
	$(MAKE) -C linux-client uninstall

# 检查依赖
check-deps:
	@echo "Checking dependencies for all components..."
	@echo "Checking host dependencies..."
	$(MAKE) -C host check-deps
	@echo "Checking Linux client dependencies..."
	$(MAKE) -C linux-client check-deps

# 运行测试
test:
	@echo "Running tests..."
	@if [ -d "tests" ]; then $(MAKE) -C tests; fi

# 代码格式化
format:
	@echo "Formatting all source code..."
	$(MAKE) -C host format
	$(MAKE) -C linux-client format
	@find windows-client -name "*.cpp" -o -name "*.h" | xargs clang-format -i 2>/dev/null || true

# 静态分析
analyze:
	@echo "Running static analysis on all components..."
	$(MAKE) -C host analyze
	$(MAKE) -C linux-client analyze

# 创建发布包
package: clean all
	@echo "Creating release package..."
	@mkdir -p release
	@cp -r host/bin release/host 2>/dev/null || true
	@cp -r linux-client/bin release/linux-client 2>/dev/null || true
	@cp -r windows-client/build/bin release/windows-client 2>/dev/null || true
	@cp README.md release/
	@cp -r docs release/ 2>/dev/null || true
	@tar -czf xenserver-antivirus-$(shell date +%Y%m%d).tar.gz release/
	@echo "Package created: xenserver-antivirus-$(shell date +%Y%m%d).tar.gz"

# 帮助信息
help:
	@echo "XenServer Agentless Antivirus Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  all              - Build host service, Linux client, and examples (default)"
	@echo "  host             - Build host service only"
	@echo "  linux-client     - Build Linux client only"
	@echo "  examples         - Build example programs only"
	@echo "  windows-client   - Build Windows client (requires CMake)"
	@echo "  clean            - Remove all build files"
	@echo "  install          - Install host service and Linux client"
	@echo "  install-host     - Install host service only"
	@echo "  install-linux-client - Install Linux client only"
	@echo "  uninstall        - Uninstall all components"
	@echo "  uninstall-host   - Uninstall host service only"
	@echo "  uninstall-linux-client - Uninstall Linux client only"
	@echo "  check-deps       - Check dependencies for all components"
	@echo "  test             - Run tests"
	@echo "  format           - Format all source code"
	@echo "  analyze          - Run static code analysis"
	@echo "  package          - Create release package"
	@echo "  help             - Show this help message"
	@echo ""
	@echo "Build Requirements:"
	@echo "  Host Service:    libxenctrl, libxenstore, gcc, make"
	@echo "  Linux Client:    libxenctrl, libxenstore, gcc, make"
	@echo "  Windows Client:  Visual Studio or MinGW, CMake, Xen PV drivers"

.PHONY: all host linux-client windows-client examples clean install install-host install-linux-client uninstall uninstall-host uninstall-linux-client check-deps test format analyze package help