#!/bin/bash

# XenServer防病毒系统 - Citrix Hypervisor专用权限修复脚本
# 针对使用oxenstored的Citrix Hypervisor环境

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "=================================================================="
echo "XenServer防病毒系统 - Citrix Hypervisor权限修复"
echo "=================================================================="

# 检查root权限
if [ $(id -u) -ne 0 ]; then
    log_error "必须以root权限运行"
    echo "使用方法: sudo $0"
    exit 1
fi

log_info "开始Citrix Hypervisor环境权限修复..."

# 1. 检查Citrix Hypervisor环境
log_info "检查Citrix Hypervisor环境..."
if [ -f "/etc/xensource-inventory" ]; then
    log_success "✅ 检测到Citrix Hypervisor环境"
    echo "产品信息:"
    grep "PRODUCT_" /etc/xensource-inventory | head -3
else
    log_warning "⚠️  未检测到标准Citrix Hypervisor环境"
fi
echo ""

# 2. 检查Xen服务状态
log_info "检查Xen相关服务状态..."

# 检查oxenstored (Citrix使用的xenstore实现)
if pgrep -x "oxenstored" > /dev/null; then
    log_success "✅ oxenstored服务正在运行"
    echo "oxenstored PID: $(pgrep -x oxenstored)"
else
    log_error "❌ oxenstored服务未运行"
    log_info "尝试启动oxenstored..."
    systemctl start oxenstored 2>/dev/null || service oxenstored start 2>/dev/null || {
        log_error "无法启动oxenstored服务"
    }
fi

# 检查xenopsd-xc
if pgrep -x "xenopsd-xc" > /dev/null; then
    log_success "✅ xenopsd-xc服务正在运行"
else
    log_warning "⚠️  xenopsd-xc服务未运行"
fi

# 检查xenconsoled
if pgrep -x "xenconsoled" > /dev/null; then
    log_success "✅ xenconsoled服务正在运行"
else
    log_warning "⚠️  xenconsoled服务未运行"
fi

echo ""

# 3. 检查Xen设备文件
log_info "检查Xen设备文件..."
if [ -d "/dev/xen" ]; then
    log_success "✅ /dev/xen目录存在"
    echo "设备文件列表:"
    ls -la /dev/xen/
    
    # 检查xenstore设备
    if [ -e "/dev/xen/xenstore" ]; then
        log_success "✅ /dev/xen/xenstore设备存在"
        XENSTORE_PERMS=$(ls -l /dev/xen/xenstore)
        echo "当前权限: $XENSTORE_PERMS"
        
        # 修复权限
        if [ ! -r "/dev/xen/xenstore" ] || [ ! -w "/dev/xen/xenstore" ]; then
            log_info "修复xenstore设备权限..."
            chmod 666 /dev/xen/xenstore 2>/dev/null && {
                log_success "✅ xenstore权限已修复"
            } || {
                log_warning "⚠️  xenstore权限修复失败"
            }
        else
            log_success "✅ xenstore权限正常"
        fi
    else
        log_error "❌ /dev/xen/xenstore设备不存在"
    fi
else
    log_error "❌ /dev/xen目录不存在"
fi
echo ""

# 4. 测试xe命令 (Citrix Hypervisor管理命令)
log_info "测试Citrix Hypervisor管理命令..."
if command -v xe >/dev/null 2>&1; then
    log_success "✅ xe命令可用"
    
    # 测试xe host-list
    if xe host-list >/dev/null 2>&1; then
        log_success "✅ xe host-list命令正常"
        echo "宿主机信息:"
        xe host-list params=name-label,software-version | head -5
    else
        log_warning "⚠️  xe host-list命令失败"
    fi
    
    # 测试xe vm-list
    if xe vm-list >/dev/null 2>&1; then
        log_success "✅ xe vm-list命令正常"
        echo "虚拟机数量: $(xe vm-list --minimal | tr ',' '\n' | wc -l)"
    else
        log_warning "⚠️  xe vm-list命令失败"
    fi
else
    log_error "❌ xe命令不可用"
fi
echo ""

# 5. 测试xl命令 (如果可用)
log_info "测试xl命令..."
if command -v xl >/dev/null 2>&1; then
    log_success "✅ xl命令可用"
    
    if xl info >/dev/null 2>&1; then
        log_success "✅ xl info命令正常"
        echo "Xen版本信息:"
        xl info | grep -E "(xen_version|xen_caps)" | head -3
    else
        log_warning "⚠️  xl info命令失败"
        echo "错误信息:"
        xl info 2>&1 | head -3
    fi
    
    if xl list >/dev/null 2>&1; then
        log_success "✅ xl list命令正常"
        echo "域列表:"
        xl list | head -5
    else
        log_warning "⚠️  xl list命令失败"
    fi
else
    log_warning "⚠️  xl命令不可用（在某些Citrix Hypervisor版本中是正常的）"
fi
echo ""

# 6. 检查libxenctrl访问
log_info "测试libxenctrl访问..."
cat > /tmp/test_xenctrl.c << 'EOF'
#include <stdio.h>
#include <xenctrl.h>

int main() {
    xc_interface *xc_handle = xc_interface_open(NULL, NULL, 0);
    if (xc_handle == NULL) {
        printf("ERROR: Cannot open xc interface\n");
        return 1;
    }
    
    xc_domaininfo_t info;
    int ret = xc_domain_getinfolist(xc_handle, 0, 1, &info);
    if (ret < 0) {
        printf("ERROR: xc_domain_getinfolist failed\n");
        xc_interface_close(xc_handle);
        return 1;
    }
    
    printf("SUCCESS: libxenctrl access working\n");
    xc_interface_close(xc_handle);
    return 0;
}
EOF

if gcc -o /tmp/test_xenctrl /tmp/test_xenctrl.c -lxenctrl 2>/dev/null; then
    log_info "编译测试程序成功，运行测试..."
    if /tmp/test_xenctrl 2>/dev/null; then
        log_success "✅ libxenctrl访问正常"
    else
        log_error "❌ libxenctrl访问失败"
        echo "错误详情:"
        /tmp/test_xenctrl 2>&1
    fi
    rm -f /tmp/test_xenctrl /tmp/test_xenctrl.c
else
    log_warning "⚠️  无法编译测试程序（可能缺少开发包）"
    rm -f /tmp/test_xenctrl.c
fi
echo ""

# 7. 修复常见权限问题
log_info "修复常见权限问题..."

# 确保用户在正确的组中
GROUPS_TO_ADD="xen xenstore"
for group in $GROUPS_TO_ADD; do
    if getent group "$group" >/dev/null 2>&1; then
        usermod -a -G "$group" root 2>/dev/null && {
            log_success "✅ 已将root添加到$group组"
        } || {
            log_warning "⚠️  添加到$group组失败"
        }
    else
        log_info "组$group不存在，跳过"
    fi
done

# 设置环境变量
export XENSTORE_DOMAIN_INTERFACE=1
export XEN_DOMAIN_INTERFACE=1

echo ""

# 8. 测试防病毒程序
PROGRAM_PATH="/usr/local/bin/xenserver-antivirus-glibc217-static"
if [ -f "$PROGRAM_PATH" ]; then
    log_info "测试防病毒程序..."
    
    # 测试帮助信息
    if timeout 10s "$PROGRAM_PATH" --help >/dev/null 2>&1; then
        log_success "✅ 程序帮助信息正常"
    else
        log_warning "⚠️  程序帮助信息测试失败"
    fi
    
    # 测试程序运行
    log_info "尝试运行程序（10秒超时）..."
    timeout 10s "$PROGRAM_PATH" --test 2>&1 | head -10 || {
        log_warning "⚠️  程序测试运行失败或超时"
        echo "尝试获取详细错误信息:"
        timeout 5s "$PROGRAM_PATH" -v 2>&1 | head -5
    }
else
    log_warning "⚠️  防病毒程序未安装在$PROGRAM_PATH"
fi

echo ""
echo "=================================================================="
echo "修复完成！"
echo "=================================================================="

echo ""
log_info "环境状态总结:"
echo "- oxenstored服务: $(pgrep -x oxenstored >/dev/null && echo '运行中' || echo '未运行')"
echo "- xenopsd-xc服务: $(pgrep -x xenopsd-xc >/dev/null && echo '运行中' || echo '未运行')"
echo "- /dev/xen/xenstore: $([ -e /dev/xen/xenstore ] && echo '存在' || echo '不存在')"
echo "- xe命令: $(command -v xe >/dev/null && echo '可用' || echo '不可用')"
echo "- xl命令: $(command -v xl >/dev/null && echo '可用' || echo '不可用')"

echo ""
log_info "现在尝试运行防病毒程序:"
echo "sudo $PROGRAM_PATH -v"

echo ""
log_info "如果仍有问题，请检查程序的详细错误输出"
