#!/bin/bash

# XenServer防病毒系统 - Citrix Hypervisor xenstore最终修复
# 解决"Is a directory"错误和libxenstore访问问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "=================================================================="
echo "Citrix Hypervisor xenstore最终修复"
echo "=================================================================="

# 检查root权限
if [ $(id -u) -ne 0 ]; then
    log_error "必须以root权限运行"
    exit 1
fi

log_info "开始最终修复Citrix Hypervisor xenstore访问..."

# 1. 清理之前的错误链接
log_info "清理之前的xenstore设备..."
if [ -L "/dev/xen/xenstore" ]; then
    rm -f /dev/xen/xenstore
    log_success "✅ 已删除错误的xenstore符号链接"
fi

# 2. 检查系统中xenstore的实际设备号
log_info "查找xenstore设备信息..."

# 检查/proc/devices中的xenstore设备
XENSTORE_MAJOR=$(grep xenstore /proc/devices 2>/dev/null | awk '{print $1}')
if [ -n "$XENSTORE_MAJOR" ]; then
    log_success "✅ 找到xenstore主设备号: $XENSTORE_MAJOR"
else
    log_warning "⚠️  /proc/devices中未找到xenstore设备"
    # 尝试常见的设备号
    XENSTORE_MAJOR=10
    log_info "使用常见的xenstore主设备号: $XENSTORE_MAJOR"
fi

# 3. 创建正确的xenstore字符设备
log_info "创建xenstore字符设备..."

# 尝试不同的次设备号
for MINOR in 60 61 62 63; do
    log_info "尝试创建xenstore设备 (主:$XENSTORE_MAJOR, 次:$MINOR)..."
    
    if mknod /dev/xen/xenstore c $XENSTORE_MAJOR $MINOR 2>/dev/null; then
        chmod 666 /dev/xen/xenstore
        log_success "✅ xenstore字符设备创建成功 ($XENSTORE_MAJOR:$MINOR)"
        
        # 测试设备是否可用
        if [ -c "/dev/xen/xenstore" ] && [ -r "/dev/xen/xenstore" ] && [ -w "/dev/xen/xenstore" ]; then
            log_success "✅ xenstore设备可读写"
            break
        else
            log_warning "⚠️  设备创建但不可访问，尝试下一个次设备号"
            rm -f /dev/xen/xenstore
        fi
    else
        log_warning "⚠️  无法创建设备 ($XENSTORE_MAJOR:$MINOR)"
    fi
done

# 4. 如果字符设备创建失败，尝试其他方法
if [ ! -c "/dev/xen/xenstore" ]; then
    log_warning "⚠️  无法创建标准xenstore字符设备"
    log_info "尝试替代方案..."
    
    # 方案1: 检查是否有其他xenstore相关设备
    if [ -e "/dev/xen/evtchn" ]; then
        log_info "找到evtchn设备，尝试基于其设备号创建xenstore"
        EVTCHN_INFO=$(ls -l /dev/xen/evtchn | awk '{print $5 $6}' | tr -d ',')
        EVTCHN_MAJOR=$(echo $EVTCHN_INFO | cut -c1-2)
        XENSTORE_MINOR=60
        
        if mknod /dev/xen/xenstore c $EVTCHN_MAJOR $XENSTORE_MINOR 2>/dev/null; then
            chmod 666 /dev/xen/xenstore
            log_success "✅ 基于evtchn创建xenstore设备成功"
        fi
    fi
    
    # 方案2: 创建到/proc/xen的链接（如果存在）
    if [ ! -c "/dev/xen/xenstore" ] && [ -d "/proc/xen" ]; then
        log_info "尝试使用/proc/xen接口..."
        # 在某些系统中，xenstore通过/proc/xen/xenstore访问
        if [ -e "/proc/xen/xenstore" ]; then
            ln -sf /proc/xen/xenstore /dev/xen/xenstore 2>/dev/null && {
                log_success "✅ 创建到/proc/xen/xenstore的链接"
            }
        fi
    fi
fi

# 5. 检查最终结果
log_info "检查xenstore设备最终状态..."
if [ -e "/dev/xen/xenstore" ]; then
    ls -la /dev/xen/xenstore
    
    if [ -c "/dev/xen/xenstore" ]; then
        log_success "✅ xenstore字符设备存在且正确"
    elif [ -L "/dev/xen/xenstore" ]; then
        log_warning "⚠️  xenstore是符号链接，可能不被libxenstore支持"
    else
        log_warning "⚠️  xenstore设备类型未知"
    fi
else
    log_error "❌ 无法创建xenstore设备"
fi

# 6. 设置环境变量（针对Citrix）
log_info "设置Citrix特定环境变量..."
export XENSTORED_ROOTDIR="/var/lib/xenstored"
export XENSTORE_DOMAIN_INTERFACE=1
export XEN_DOMAIN_INTERFACE=1
export XENSTORED_PATH="/var/run/xenstored"

# 对于某些版本的libxenstore，可能需要这些环境变量
export XENSTORE_DOMAIN_SOCKET="/var/run/xenstored/socket"
export XEN_DOMAIN_SOCKET="/var/run/xenstored/socket"

# 保存环境变量
cat > /etc/profile.d/xenserver-antivirus.sh << 'EOF'
# XenServer防病毒系统环境变量 - Citrix Hypervisor专用
export XENSTORED_ROOTDIR="/var/lib/xenstored"
export XENSTORE_DOMAIN_INTERFACE=1
export XEN_DOMAIN_INTERFACE=1
export XENSTORED_PATH="/var/run/xenstored"
export XENSTORE_DOMAIN_SOCKET="/var/run/xenstored/socket"
export XEN_DOMAIN_SOCKET="/var/run/xenstored/socket"
EOF

log_success "✅ 环境变量已更新"

# 7. 修复其他可能的权限问题
log_info "修复相关权限..."

# 确保xenstored socket可访问
if [ -S "/var/run/xenstored/socket" ]; then
    chmod 666 /var/run/xenstored/socket 2>/dev/null || true
    log_success "✅ xenstored socket权限已修复"
fi

# 确保/dev/xen目录权限正确
chmod 755 /dev/xen 2>/dev/null || true

# 修复所有/dev/xen下的设备权限
for device in /dev/xen/*; do
    if [ -c "$device" ] || [ -b "$device" ]; then
        chmod 666 "$device" 2>/dev/null || true
    fi
done

log_success "✅ 设备权限修复完成"

# 8. 测试修复结果
log_info "测试修复结果..."

# 加载环境变量
source /etc/profile.d/xenserver-antivirus.sh

# 测试程序
PROGRAM_PATH="/usr/local/bin/xenserver-antivirus-citrix-static"
if [ -f "$PROGRAM_PATH" ]; then
    log_info "测试Citrix专用程序..."
    echo "运行测试（10秒超时）..."
    timeout 10s "$PROGRAM_PATH" --test 2>&1 | head -20
else
    log_warning "⚠️  Citrix专用程序未找到"
    
    # 尝试标准程序
    STANDARD_PROGRAM="/usr/local/bin/xenserver-antivirus-glibc217-static"
    if [ -f "$STANDARD_PROGRAM" ]; then
        log_info "测试标准程序..."
        timeout 10s "$STANDARD_PROGRAM" --test 2>&1 | head -20
    fi
fi

echo ""
echo "=================================================================="
echo "Citrix Hypervisor xenstore最终修复完成！"
echo "=================================================================="

echo ""
log_info "修复摘要:"
echo "- xenstore设备: $([ -e /dev/xen/xenstore ] && echo '已创建' || echo '创建失败')"
echo "- 设备类型: $([ -c /dev/xen/xenstore ] && echo '字符设备' || ([ -L /dev/xen/xenstore ] && echo '符号链接' || echo '未知'))"
echo "- oxenstored状态: $(pgrep -x oxenstored >/dev/null && echo '运行中' || echo '未运行')"
echo "- socket权限: $([ -S /var/run/xenstored/socket ] && echo '存在' || echo '不存在')"

echo ""
log_info "现在请测试程序:"
echo "source /etc/profile.d/xenserver-antivirus.sh"
echo "sudo $PROGRAM_PATH --test"

echo ""
log_info "如果仍有问题，可能需要:"
echo "1. 重启系统以重新加载Xen模块"
echo "2. 检查Citrix Hypervisor的内核模块: lsmod | grep xen"
echo "3. 联系Citrix技术支持获取xenstore设备的正确创建方法"
