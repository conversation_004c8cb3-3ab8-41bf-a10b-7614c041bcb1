#!/bin/bash

# XenServer防病毒系统 - 真实版本静态编译脚本（修复版）
# 只编译真实版本，包含信号处理和权限检查修复

set -e  # 遇到错误立即退出

echo "=== XenServer Antivirus Real Static Build (Fixed Version) ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查编译环境
check_build_environment() {
    log_info "检查编译环境..."
    
    # 检查gcc
    if ! command -v gcc &> /dev/null; then
        log_error "gcc未安装，请安装gcc编译器"
        exit 1
    fi
    
    # 检查make
    if ! command -v make &> /dev/null; then
        log_error "make未安装，请安装make工具"
        exit 1
    fi
    
    log_success "编译环境检查完成"
}

# 清理之前的构建
clean_previous_builds() {
    log_info "清理之前的构建..."
    
    cd host
    make -f Makefile.real-static-fixed clean 2>/dev/null || true
    cd ../linux-client
    make -f Makefile.real-static-fixed clean 2>/dev/null || true
    cd ..
    
    log_success "清理完成"
}

# 编译宿主机服务（真实版本）
build_host_service() {
    log_info "编译宿主机服务（真实静态版本，包含修复）..."
    
    cd host
    
    # 显示编译配置
    make -f Makefile.real-static-fixed info
    
    # 开始编译
    if make -f Makefile.real-static-fixed; then
        log_success "宿主机服务编译成功"
        
        # 显示编译结果
        if [ -f "xenserver-antivirus-host-real-static-fixed" ]; then
            log_info "编译结果："
            ls -lh xenserver-antivirus-host-real-static-fixed
            file xenserver-antivirus-host-real-static-fixed
            
            # 测试可执行性
            if ./xenserver-antivirus-host-real-static-fixed --help >/dev/null 2>&1; then
                log_success "宿主机服务可执行性测试通过"
            else
                log_warning "宿主机服务可执行性测试失败，但文件已生成"
            fi
        fi
    else
        log_error "宿主机服务编译失败"
        cd ..
        exit 1
    fi
    
    cd ..
}

# 编译Linux客户端（真实版本）
build_linux_client() {
    log_info "编译Linux客户端（真实静态版本，包含修复）..."
    
    cd linux-client
    
    # 显示编译配置
    make -f Makefile.real-static-fixed info
    
    # 开始编译
    if make -f Makefile.real-static-fixed; then
        log_success "Linux客户端编译成功"
        
        # 显示编译结果
        if [ -f "xenserver-antivirus-client-real-static-fixed" ]; then
            log_info "编译结果："
            ls -lh xenserver-antivirus-client-real-static-fixed
            file xenserver-antivirus-client-real-static-fixed
            
            # 测试可执行性
            if ./xenserver-antivirus-client-real-static-fixed --help >/dev/null 2>&1; then
                log_success "Linux客户端可执行性测试通过"
            else
                log_warning "Linux客户端可执行性测试失败，但文件已生成"
            fi
        fi
    else
        log_error "Linux客户端编译失败"
        cd ..
        exit 1
    fi
    
    cd ..
}

# 创建发布包
create_release_package() {
    log_info "创建发布包..."
    
    # 创建发布目录
    RELEASE_DIR="release-real-static-fixed"
    rm -rf "$RELEASE_DIR"
    mkdir -p "$RELEASE_DIR"
    
    # 复制可执行文件
    if [ -f "host/bin/xenserver-antivirus-host-real-static-fixed" ]; then
        cp host/bin/xenserver-antivirus-host-real-static-fixed "$RELEASE_DIR/"
        log_success "宿主机服务已添加到发布包"
    fi
    
    if [ -f "linux-client/bin/av_linux_client_real_static_fixed" ]; then
        cp linux-client/bin/av_linux_client_real_static_fixed "$RELEASE_DIR/"
        log_success "Linux客户端已添加到发布包"
    fi
    
    # 复制文档
    [ -f "docs/INSTALL.md" ] && cp docs/INSTALL.md "$RELEASE_DIR/"
    [ -f "docs/COMMUNICATION.md" ] && cp docs/COMMUNICATION.md "$RELEASE_DIR/"
    
    # 创建README
    cat > "$RELEASE_DIR/README-REAL-FIXED.md" << 'EOF'
# XenServer Antivirus Real Static Build (Fixed Version)

这是XenServer防病毒系统的真实版本静态编译，包含以下修复：

## 修复内容

### 1. 权限问题修复
- 改进了xc_domain_getinfolist的错误处理
- 添加了详细的权限检查和错误报告
- 提供了更清晰的错误信息和解决建议

### 2. 信号处理修复
- 修复了Ctrl+C无法终止程序的问题
- 使用可中断的nanosleep替代普通sleep
- 改进了信号处理器，支持强制退出
- 确保所有线程都能正确响应关闭信号

## 文件说明

- `xenserver-antivirus-host-real-static-fixed`: 宿主机服务（真实静态编译）
- `xenserver-antivirus-client-real-static-fixed`: Linux客户端（真实静态编译）

## 使用方法

### 宿主机服务
```bash
# 在XenServer宿主机上运行
sudo ./xenserver-antivirus-host-real-static-fixed

# 查看帮助
./xenserver-antivirus-host-real-static-fixed --help

# 以守护进程模式运行
sudo ./xenserver-antivirus-host-real-static-fixed -d
```

### Linux客户端
```bash
# 在Linux虚拟机中运行
sudo ./xenserver-antivirus-client-real-static-fixed

# 查看帮助
./xenserver-antivirus-client-real-static-fixed --help
```

## 依赖要求

### 宿主机服务
- 必须在XenServer Dom0环境中运行
- 需要libxenctrl和libxenstore库
- 必须以root权限运行

### Linux客户端
- 需要在Xen虚拟机中运行
- 需要libxenctrl和libxenstore库
- 必须以root权限运行

## 信号处理

程序现在正确支持以下信号：
- Ctrl+C (SIGINT): 优雅关闭
- SIGTERM: 优雅关闭
- 连续3次信号: 强制退出

## 故障排除

如果遇到权限问题：
1. 确保以root权限运行
2. 检查是否在正确的环境中（Dom0 vs 虚拟机）
3. 验证Xen服务状态：`systemctl status xenstored`
4. 检查设备权限：`ls -la /dev/xen/`

如果程序无法终止：
1. 尝试Ctrl+C
2. 如果无响应，连续按3次Ctrl+C强制退出
3. 或使用：`kill -TERM <pid>`

## 注意事项

这是真实版本的编译，需要在实际的Xen环境中运行。
如果在非Xen环境中测试，请使用模拟版本。
EOF

    # 创建压缩包
    tar -czf "xenserver-antivirus-real-static-fixed-$(date +%Y%m%d).tar.gz" "$RELEASE_DIR"
    
    log_success "发布包已创建: xenserver-antivirus-real-static-fixed-$(date +%Y%m%d).tar.gz"
    
    # 显示发布包内容
    log_info "发布包内容："
    ls -la "$RELEASE_DIR/"
}

# 显示构建摘要
show_build_summary() {
    echo
    log_info "=== 构建摘要 ==="
    
    if [ -f "host/bin/xenserver-antivirus-host-real-static-fixed" ]; then
        echo "✅ 宿主机服务: host/bin/xenserver-antivirus-host-real-static-fixed"
        echo "   大小: $(du -h host/bin/xenserver-antivirus-host-real-static-fixed | cut -f1)"
    else
        echo "❌ 宿主机服务: 构建失败"
    fi
    
    if [ -f "linux-client/bin/av_linux_client_real_static_fixed" ]; then
        echo "✅ Linux客户端: linux-client/bin/av_linux_client_real_static_fixed"
        echo "   大小: $(du -h linux-client/bin/av_linux_client_real_static_fixed | cut -f1)"
    else
        echo "❌ Linux客户端: 构建失败"
    fi
    
    if [ -f "xenserver-antivirus-real-static-fixed-$(date +%Y%m%d).tar.gz" ]; then
        echo "✅ 发布包: xenserver-antivirus-real-static-fixed-$(date +%Y%m%d).tar.gz"
        echo "   大小: $(du -h xenserver-antivirus-real-static-fixed-$(date +%Y%m%d).tar.gz | cut -f1)"
    fi
    
    echo
    log_info "修复内容："
    echo "  - xc_domain_getinfolist权限问题修复"
    echo "  - Ctrl+C信号处理修复"
    echo "  - 可中断的sleep调用"
    echo "  - 改进的错误报告"
    echo "  - 强制退出机制"
    
    echo
    log_success "真实版本静态编译（修复版）完成！"
}

# 主函数
main() {
    # 检查是否在正确的目录中
    if [ ! -f "host/main_real.c" ] || [ ! -f "linux-client/main_real.c" ]; then
        log_error "请在xenserver-antivirus项目根目录中运行此脚本"
        exit 1
    fi
    
    # 执行构建步骤
    check_build_environment
    clean_previous_builds
    build_host_service
    build_linux_client
    create_release_package
    show_build_summary
}

# 运行主函数
main "$@"