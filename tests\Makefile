# 测试Makefile

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g -DDEBUG -D_GNU_SOURCE
LDFLAGS = -lpthread

# 目录定义
SRCDIR = .
COMMONDIR = ../common
HOSTDIR = ../host
OBJDIR = obj
BINDIR = bin

# 源文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
HOST_SOURCES = $(HOSTDIR)/av_vm_discovery.c $(HOSTDIR)/av_xenstore.c $(HOSTDIR)/av_error_handling.c $(HOSTDIR)/av_communication.c $(HOSTDIR)/av_service.c $(HOSTDIR)/av_resource_manager.c
TEST_SOURCES = test_common.c test_vm_discovery.c test_communication.c test_response_handling.c test_error_handling.c

# 目标文件
COMMON_OBJECTS = $(COMMON_SOURCES:%.c=$(OBJDIR)/%.o)
HOST_OBJECTS = $(HOST_SOURCES:%.c=$(OBJDIR)/%.o)
TEST_OBJECTS = $(TEST_SOURCES:%.c=$(OBJDIR)/%.o)

# 测试可执行文件
TEST_TARGETS = $(BINDIR)/test_vm_discovery $(BINDIR)/test_shared_memory $(BINDIR)/test_xenstore $(BINDIR)/test_communication $(BINDIR)/test_response_handling $(BINDIR)/test_error_handling

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I$(HOSTDIR) -I.

# 默认目标
all: directories $(TEST_TARGETS)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(OBJDIR)/$(HOSTDIR)
	@mkdir -p $(BINDIR)

# 编译测试程序
$(BINDIR)/test_vm_discovery: $(OBJDIR)/test_common.o $(OBJDIR)/test_vm_discovery.o $(COMMON_OBJECTS)
	@echo "Linking $@..."
	$(CC) $^ -o $@ $(LDFLAGS)

$(BINDIR)/test_shared_memory: $(OBJDIR)/test_common.o $(OBJDIR)/test_shared_memory.o $(COMMON_OBJECTS) $(OBJDIR)/$(HOSTDIR)/av_shared_memory.o
	@echo "Linking $@..."
	$(CC) $^ -o $@ $(LDFLAGS)

$(BINDIR)/test_xenstore: $(OBJDIR)/test_common.o $(OBJDIR)/test_xenstore.o $(COMMON_OBJECTS) $(OBJDIR)/$(HOSTDIR)/av_xenstore.o
	@echo "Linking $@..."
	$(CC) $^ -o $@ $(LDFLAGS)

$(BINDIR)/test_communication: $(OBJDIR)/test_common.o $(OBJDIR)/test_communication.o $(COMMON_OBJECTS) $(OBJDIR)/$(HOSTDIR)/av_communication.o $(OBJDIR)/$(HOSTDIR)/av_service.o $(OBJDIR)/$(HOSTDIR)/av_shared_memory.o $(OBJDIR)/$(HOSTDIR)/av_error_handling.o
	@echo "Linking $@..."
	$(CC) $^ -o $@ $(LDFLAGS)

$(BINDIR)/test_response_handling: $(OBJDIR)/test_common.o $(OBJDIR)/test_response_handling.o $(COMMON_OBJECTS) $(OBJDIR)/$(HOSTDIR)/av_communication.o $(OBJDIR)/$(HOSTDIR)/av_service.o $(OBJDIR)/$(HOSTDIR)/av_shared_memory.o $(OBJDIR)/$(HOSTDIR)/av_error_handling.o
	@echo "Linking $@..."
	$(CC) $^ -o $@ $(LDFLAGS)

$(BINDIR)/test_error_handling: $(OBJDIR)/test_common.o $(OBJDIR)/test_error_handling.o $(COMMON_OBJECTS) $(OBJDIR)/$(HOSTDIR)/av_error_handling.o $(OBJDIR)/$(HOSTDIR)/av_resource_manager.o
	@echo "Linking $@..."
	$(CC) $^ -o $@ $(LDFLAGS)

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $<..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 运行所有测试
test: $(TEST_TARGETS)
	@echo "Running tests..."
	@for test in $(TEST_TARGETS); do \
		echo "Running $$test..."; \
		$$test || exit 1; \
		echo ""; \
	done
	@echo "All tests passed!"

# 运行特定测试
test-vm-discovery: $(BINDIR)/test_vm_discovery
	@echo "Running VM discovery tests..."
	$(BINDIR)/test_vm_discovery

test-shared-memory: $(BINDIR)/test_shared_memory
	@echo "Running shared memory tests..."
	$(BINDIR)/test_shared_memory

test-xenstore: $(BINDIR)/test_xenstore
	@echo "Running XenStore tests..."
	$(BINDIR)/test_xenstore

test-communication: $(BINDIR)/test_communication
	@echo "Running communication tests..."
	$(BINDIR)/test_communication

test-response-handling: $(BINDIR)/test_response_handling
	@echo "Running response handling tests..."
	$(BINDIR)/test_response_handling

test-error-handling: $(BINDIR)/test_error_handling
	@echo "Running error handling tests..."
	$(BINDIR)/test_error_handling

# 清理编译文件
clean:
	@echo "Cleaning test build files..."
	rm -rf $(OBJDIR) $(BINDIR)

# 内存检查（需要valgrind）
memcheck: $(TEST_TARGETS)
	@echo "Running memory checks..."
	@for test in $(TEST_TARGETS); do \
		echo "Memory checking $$test..."; \
		valgrind --leak-check=full --error-exitcode=1 $$test || exit 1; \
		echo ""; \
	done

# 代码覆盖率（需要gcov）
coverage: CFLAGS += --coverage
coverage: LDFLAGS += --coverage
coverage: clean $(TEST_TARGETS)
	@echo "Running tests with coverage..."
	@for test in $(TEST_TARGETS); do \
		$$test; \
	done
	@echo "Generating coverage report..."
	gcov $(TEST_SOURCES) $(COMMON_SOURCES)

# 帮助信息
help:
	@echo "Available targets:"
	@echo "  all              - Build all tests (default)"
	@echo "  test             - Run all tests"
	@echo "  test-vm-discovery - Run VM discovery tests only"
	@echo "  test-shared-memory - Run shared memory tests only"
	@echo "  test-xenstore    - Run XenStore tests only"
	@echo "  test-communication - Run communication tests only"
	@echo "  test-response-handling - Run response handling tests only"
	@echo "  test-error-handling - Run error handling tests only"
	@echo "  clean            - Remove build files"
	@echo "  memcheck         - Run tests with valgrind memory checking"
	@echo "  coverage         - Run tests with code coverage analysis"
	@echo "  help             - Show this help message"

.PHONY: all clean test test-vm-discovery memcheck coverage help directories