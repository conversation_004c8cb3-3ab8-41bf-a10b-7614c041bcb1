#include "test_common.h"
#include <unistd.h>
#include <time.h>

/* 测试统计变量 */
int tests_total = 0;
int tests_passed = 0;
int tests_failed = 0;

/* 初始化测试环境 */
void test_init(void) {
    tests_total = 0;
    tests_passed = 0;
    tests_failed = 0;
    
    printf("=== XenServer Antivirus Test Suite ===\n");
    printf("Starting tests...\n\n");
}

/* 输出测试总结 */
void test_summary(void) {
    printf("\n=== Test Summary ===\n");
    printf("Total tests: %d\n", tests_total);
    printf("Passed: %d\n", tests_passed);
    printf("Failed: %d\n", tests_failed);
    
    if (tests_failed == 0) {
        printf("All tests PASSED!\n");
    } else {
        printf("Some tests FAILED!\n");
    }
}

/* 创建临时测试文件 */
int test_create_temp_file(const char* content, char* filename, size_t filename_size) {
    snprintf(filename, filename_size, "/tmp/av_test_%ld_%d", time(NULL), getpid());
    
    FILE* file = fopen(filename, "w");
    if (!file) {
        return -1;
    }
    
    if (content) {
        fprintf(file, "%s", content);
    }
    
    fclose(file);
    return 0;
}

/* 清理临时测试文件 */
void test_cleanup_temp_file(const char* filename) {
    if (filename) {
        unlink(filename);
    }
}

/* 重置模拟函数 */
void mock_reset(mock_function_t* mock) {
    if (mock) {
        mock->should_fail = 0;
        mock->call_count = 0;
        mock->return_value = NULL;
    }
}

/* 设置模拟函数返回值 */
void mock_set_return_value(mock_function_t* mock, void* value) {
    if (mock) {
        mock->return_value = value;
    }
}

/* 设置模拟函数是否失败 */
void mock_set_should_fail(mock_function_t* mock, int should_fail) {
    if (mock) {
        mock->should_fail = should_fail;
    }
}