# Linux虚拟机防病毒客户端静态编译Makefile

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE -D__USE_GNU -static

# 检查是否有Xen库
XEN_LIBS_AVAILABLE := $(shell pkg-config --exists xencontrol xenstore 2>/dev/null && echo yes || echo no)

ifeq ($(XEN_LIBS_AVAILABLE),yes)
    CFLAGS += -DHAVE_XEN_LIBS
    # 静态链接所有库，包括系统库
    LDFLAGS = -lpthread -lrt -ldl -lxencontrol -lxenstore
else
    # 静态链接基本库
    LDFLAGS = -lpthread -lrt -ldl
endif

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj-static
BINDIR = bin-static

# 源文件和目标文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
CLIENT_SOURCES = $(wildcard $(SRCDIR)/*.c)
SOURCES = $(COMMON_SOURCES) $(CLIENT_SOURCES)
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/av_linux_client_static

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I/usr/include/xen

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Statically linking $@..."
	$(CC) -static $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Static build complete: $@"
	@echo "Binary size: $$(du -h $@ | cut -f1)"
	@echo "Dependencies check:"
	@ldd $@ 2>/dev/null || echo "  -> Statically linked (no dynamic dependencies)"

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $< (static)..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning static build files..."
	rm -rf $(OBJDIR) $(BINDIR)

# 安装程序
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/av_linux_client_static
	sudo chmod +x /usr/local/bin/av_linux_client_static
	@echo "Static installation complete"

# 卸载程序
uninstall:
	@echo "Uninstalling av_linux_client_static..."
	sudo rm -f /usr/local/bin/av_linux_client_static
	@echo "Uninstall complete"

# 检查依赖
check-deps:
	@echo "Checking dependencies for static build..."
	@pkg-config --exists xencontrol || (echo "WARNING: libxencontrol not found, will build without Xen support" && exit 0)
	@pkg-config --exists xenstore || (echo "WARNING: libxenstore not found, will build without Xen support" && exit 0)
	@echo "Dependencies check complete"

# 运行程序（需要root权限）
run: $(TARGET)
	@echo "Running $(TARGET) (requires root privileges)..."
	sudo $(TARGET)

# 调试版本（静态）
debug: CFLAGS += -DDEBUG -g3
debug: $(TARGET)

# 发布版本（静态优化）
release: CFLAGS += -DNDEBUG -O3 -s
release: clean $(TARGET)

# 最小化版本（去除调试信息和符号）
minimal: CFLAGS += -DNDEBUG -O3 -s
minimal: LDFLAGS += -s
minimal: clean $(TARGET)
	@echo "Minimal static build complete"
	@echo "Final binary size: $$(du -h $(TARGET) | cut -f1)"
	@strip $(TARGET)
	@echo "Stripped binary size: $$(du -h $(TARGET) | cut -f1)"

# 验证静态链接
verify: $(TARGET)
	@echo "Verifying static linkage..."
	@file $(TARGET)
	@echo "Dependencies:"
	@ldd $(TARGET) 2>/dev/null || echo "  -> Statically linked (no dynamic dependencies)"
	@echo "Binary size: $$(du -h $(TARGET) | cut -f1)"

# 帮助信息
help:
	@echo "Available targets for static build:"
	@echo "  all        - Build the static program (default)"
	@echo "  clean      - Remove static build files"
	@echo "  install    - Install the static program to /usr/local/bin"
	@echo "  uninstall  - Remove the installed static program"
	@echo "  check-deps - Check for required dependencies"
	@echo "  run        - Build and run the static program"
	@echo "  debug      - Build static debug version"
	@echo "  release    - Build optimized static release version"
	@echo "  minimal    - Build minimal static version (stripped)"
	@echo "  verify     - Verify static linkage of built binary"
	@echo "  help       - Show this help message"

.PHONY: all clean install uninstall check-deps run debug release minimal verify help directories