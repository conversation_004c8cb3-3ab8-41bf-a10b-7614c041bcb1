/*
 * XenServer宿主机防病毒服务 - 真实Xen环境版本
 * 运行在XenServer宿主机上，管理虚拟机防病毒扫描
 */

/* 首先定义必要的宏 */
#ifndef _GNU_SOURCE
#define _GNU_SOURCE
#endif
#define __XEN_TOOLS__ 1

/* 标准C库头文件 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <pthread.h>
#include <sys/mman.h>
#include <sys/select.h>
#include <sys/time.h>
#include <getopt.h>
#include <time.h>

/* Xen头文件 - 在标准库之后包含 */
#ifndef USE_MOCK_XEN
#include <xenctrl.h>
#include <xenstore.h>
#include <xengnttab.h>
#endif

/* 项目头文件 */
#include "../common/av_common.h"

/* XenStore常量 */
#define XBT_NULL 0

/* 宿主机服务结构体 */
typedef struct {
    xc_interface* xc_handle;        /* libxenctrl句柄 */
    struct xs_handle* xs_handle;    /* XenStore句柄 */
    xengnttab_handle* gnttab_handle; /* Grant table句柄 */
    pthread_t vm_monitor_thread;    /* VM监控线程 */
    volatile int shutdown_requested; /* 关闭请求标志 */
    int max_vms;                    /* 最大VM数量 */
} av_host_service_t;

/* 使用真实的Xen库 - 不需要重复声明 */



/* 模拟辅助函数 */
int av_list_running_vms(av_host_service_t* service, domid_t** domids, int* count) {
    if (!service || !domids || !count) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_list_running_vms");
        return AV_ERROR_INIT;
    }

    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "libxc handle not initialized");
        return AV_ERROR_INIT;
    }

    av_log(AV_LOG_DEBUG, "Getting list of running VMs...");

    /* 获取所有域的信息 */
    xc_domaininfo_t* domain_info = NULL;
    int max_domains = 1024;
    int num_domains = 0;

    domain_info = malloc(max_domains * sizeof(xc_domaininfo_t));
    if (!domain_info) {
        av_log(AV_LOG_ERROR, "Failed to allocate memory for domain info");
        return AV_ERROR_INIT;
    }

    num_domains = xc_domain_getinfolist(service->xc_handle, (uint32_t)0, max_domains, domain_info);
    if (num_domains < 0) {
        int saved_errno = errno;
        av_log(AV_LOG_ERROR, "Failed to get domain list: %s (errno: %d)", strerror(saved_errno), saved_errno);
        
        /* 提供更详细的错误信息 */
        if (saved_errno == EPERM || saved_errno == EACCES) {
            av_log(AV_LOG_ERROR, "Permission denied - ensure running as root with proper Xen privileges");
        } else if (saved_errno == ENOENT || saved_errno == ENODEV) {
            av_log(AV_LOG_ERROR, "Xen hypervisor not found - ensure Xen is running and properly configured");
        }
        
        free(domain_info);
        return AV_ERROR_INIT;
    }

    av_log(AV_LOG_DEBUG, "Found %d domains", num_domains);

    /* 分配结果数组 */
    domid_t* result = malloc(num_domains * sizeof(domid_t));
    if (!result) {
        av_log(AV_LOG_ERROR, "Failed to allocate memory for result array");
        free(domain_info);
        return AV_ERROR_INIT;
    }

    int running_count = 0;

    /* 筛选运行中的虚拟机 */
    for (int i = 0; i < num_domains; i++) {
        domid_t current_domid = domain_info[i].domain;
        
        /* 跳过dom0 */
        if (current_domid == 0) {
            continue;
        }

        /* 检查是否正在运行 */
        if ((domain_info[i].flags & XEN_DOMINF_running) &&
            !(domain_info[i].flags & XEN_DOMINF_dying) &&
            !(domain_info[i].flags & XEN_DOMINF_shutdown)) {
            
            result[running_count] = current_domid;
            running_count++;
            
            av_log(AV_LOG_INFO, "Found running VM: domain %u (flags: 0x%x)", current_domid, domain_info[i].flags);
        } else {
            av_log(AV_LOG_DEBUG, "Skipping domain %u (flags: 0x%x)", current_domid, domain_info[i].flags);
        }
    }

    free(domain_info);

    *domids = result;
    *count = running_count;

    av_log(AV_LOG_INFO, "Found %d running VMs", running_count);
    return AV_SUCCESS;
}

int av_xenstore_read(av_host_service_t* service, const char* path, char* buffer, size_t buffer_size) {
    if (!service || !path || !buffer || buffer_size == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_read");
        return AV_ERROR_INIT;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_INIT;
    }

    av_log(AV_LOG_DEBUG, "Reading XenStore path: %s", path);

    unsigned int len;
    void* value_ptr = xs_read(service->xs_handle, XBT_NULL, path, &len);
    char* value = (char*)value_ptr;
    
    if (!value) {
        av_log(AV_LOG_DEBUG, "Failed to read XenStore path: %s", path);
        return AV_ERROR_XENSTORE_READ;
    }

    if (len >= buffer_size) {
        av_log(AV_LOG_ERROR, "Buffer too small for XenStore value (need %u, have %zu)", len + 1, buffer_size);
        free(value);
        return AV_ERROR_INIT;
    }

    strncpy(buffer, value, buffer_size - 1);
    buffer[buffer_size - 1] = '\0';
    
    av_log(AV_LOG_DEBUG, "Successfully read XenStore value: %s", buffer);
    
    free(value);
    return AV_SUCCESS;
}

/* 版本信息 */
#define AV_VERSION "1.0.0"

/* 错误码定义 */
#define AV_SUCCESS 0
#define AV_ERROR_INIT -1
#define AV_ERROR_MEMORY -2
#define AV_ERROR_XEN -3
#define AV_ERROR_TIMEOUT -4

/* 全局服务实例 */
static av_host_service_t g_service;

/* 信号处理函数 */
void signal_handler(int signum) {
    static volatile sig_atomic_t signal_count = 0;
    
    switch (signum) {
        case SIGINT:
        case SIGTERM:
            signal_count++;
            av_log(AV_LOG_INFO, "Received shutdown signal %d (%s), initiating graceful shutdown - count: %d", 
                   signum, (signum == SIGINT) ? "SIGINT" : "SIGTERM", (int)signal_count);
            g_service.shutdown_requested = 1;
            
            /* 如果多次收到信号，强制退出 */
            if (signal_count >= 3) {
                av_log(AV_LOG_WARN, "Received %d shutdown signals, forcing exit", (int)signal_count);
                _exit(1);
            }
            break;
        default:
            av_log(AV_LOG_WARN, "Received unexpected signal: %d", signum);
            break;
    }
}

/* VM监控线程 */
void* vm_monitor_thread(void* arg) {
    av_host_service_t* service = (av_host_service_t*)arg;
    
    av_log(AV_LOG_INFO, "VM monitor thread started");
    
    while (!service->shutdown_requested) {
        av_log(AV_LOG_DEBUG, "Monitoring VMs...");
        
        /* 扫描运行中的VM */
        domid_t* domids = NULL;
        int count = 0;
        
        int ret = av_list_running_vms(service, &domids, &count);
        if (ret == AV_SUCCESS) {
            if (count > 0) {
                av_log(AV_LOG_INFO, "Found %d running VMs", count);
                
                /* 检查每个VM的防病毒客户端状态 */
                for (int i = 0; i < count; i++) {
                    domid_t domid = domids[i];
                    av_log(AV_LOG_DEBUG, "Checking VM domain %u for antivirus client", domid);
                    
                    /* 检查VM是否有防病毒客户端 */
                    char xenstore_path[256];
                    char value[64];
                    
                    /* 检查客户端注册路径 */
                    snprintf(xenstore_path, sizeof(xenstore_path), 
                             "/local/domain/%u/data/av_client_ready", domid);
                    
                    ret = av_xenstore_read(service, xenstore_path, value, sizeof(value));
                    if (ret == AV_SUCCESS) {
                        av_log(AV_LOG_INFO, "VM domain %u has antivirus client ready: %s", domid, value);
                        
                        /* 检查共享内存ID */
                        snprintf(xenstore_path, sizeof(xenstore_path), 
                                 "/local/domain/%u/data/av_shm_id", domid);
                        
                        ret = av_xenstore_read(service, xenstore_path, value, sizeof(value));
                        if (ret == AV_SUCCESS) {
                            av_log(AV_LOG_INFO, "VM domain %u shared memory ID: %s", domid, value);
                        }
                    } else {
                        av_log(AV_LOG_DEBUG, "VM domain %u: no antivirus client detected", domid);
                    }
                }
                
                free(domids);
            } else {
                av_log(AV_LOG_DEBUG, "No running VMs found");
            }
        } else {
            av_log(AV_LOG_ERROR, "Failed to list running VMs: %s", av_error_string(ret));
        }
        
        /* 使用可中断的sleep */
        struct timespec sleep_time = {5, 0};
        struct timespec remaining;
        
        if (nanosleep(&sleep_time, &remaining) == -1) {
            if (errno == EINTR) {
                av_log(AV_LOG_DEBUG, "VM monitor sleep interrupted by signal");
                /* 检查是否需要退出 */
                if (service->shutdown_requested) {
                    break;
                }
            }
        }
    }
    
    av_log(AV_LOG_INFO, "VM monitor thread exiting");
    return NULL;
}

/* 初始化宿主机服务 */
int av_host_service_init(av_host_service_t* service) {
    if (!service) {
        return AV_ERROR_INIT;
    }
    
    memset(service, 0, sizeof(av_host_service_t));
    service->max_vms = 100; /* 默认最大100个VM */
    
    /* 打开libxenctrl */
    service->xc_handle = xc_interface_open(NULL, NULL, 0);
    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "Failed to open xc interface: %s", strerror(errno));
        return AV_ERROR_XEN;
    }
    
    /* 打开XenStore */
    service->xs_handle = xs_open(0);
    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "Failed to open XenStore: %s", strerror(errno));
        xc_interface_close(service->xc_handle);
        return AV_ERROR_XEN;
    }
    
    /* 打开Grant table */
    service->gnttab_handle = xengnttab_open(NULL, 0);
    if (!service->gnttab_handle) {
        av_log(AV_LOG_ERROR, "Failed to open grant table: %s", strerror(errno));
        xs_close(service->xs_handle);
        xc_interface_close(service->xc_handle);
        return AV_ERROR_XEN;
    }
    
    av_log(AV_LOG_INFO, "Host service initialized successfully");
    return AV_SUCCESS;
}

/* 启动宿主机服务 */
int av_host_service_start(av_host_service_t* service) {
    if (!service) {
        return AV_ERROR_INIT;
    }
    
    /* 创建防病毒根路径 */
    const char* antivirus_path = "/local/domain/0/antivirus";
    xs_transaction_t t = 0; /* 0 表示无事务 */
    if (!xs_mkdir(service->xs_handle, t, antivirus_path)) {
        av_log(AV_LOG_WARN, "Failed to create XenStore path: %s", antivirus_path);
        /* 继续执行，可能路径已存在 */
    }
    
    /* 启动VM监控线程 */
    if (pthread_create(&service->vm_monitor_thread, NULL, vm_monitor_thread, service) != 0) {
        av_log(AV_LOG_ERROR, "Failed to create VM monitor thread: %s", strerror(errno));
        return AV_ERROR_INIT;
    }
    
    av_log(AV_LOG_INFO, "Host service started successfully");
    return AV_SUCCESS;
}

/* 停止宿主机服务 */
int av_host_service_stop(av_host_service_t* service) {
    if (!service) {
        return AV_ERROR_INIT;
    }
    
    service->shutdown_requested = 1;
    
    /* 等待VM监控线程结束 */
    if (service->vm_monitor_thread) {
        pthread_join(service->vm_monitor_thread, NULL);
        service->vm_monitor_thread = 0;
    }
    
    av_log(AV_LOG_INFO, "Host service stopped successfully");
    return AV_SUCCESS;
}

/* 清理宿主机服务 */
void av_host_service_cleanup(av_host_service_t* service) {
    if (!service) {
        return;
    }
    
    /* 关闭Grant table */
    if (service->gnttab_handle) {
        xengnttab_close(service->gnttab_handle);
        service->gnttab_handle = NULL;
    }
    
    /* 关闭XenStore */
    if (service->xs_handle) {
        xs_close(service->xs_handle);
        service->xs_handle = NULL;
    }
    
    /* 关闭libxenctrl */
    if (service->xc_handle) {
        xc_interface_close(service->xc_handle);
        service->xc_handle = NULL;
    }
    
    av_log(AV_LOG_INFO, "Host service cleanup completed");
}

/* 打印使用帮助 */
void print_usage(const char* program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("XenServer Antivirus Host Service\n\n");
    printf("Options:\n");
    printf("  -h, --help           Show this help message\n");
    printf("  -v, --verbose        Enable verbose logging\n");
    printf("  -d, --daemon         Run as daemon process\n");
    printf("  -l, --log-level LEVEL Set log level (0=ERROR, 1=WARNING, 2=INFO, 3=DEBUG)\n");
    printf("  -f, --log-file FILE  Write logs to file instead of stdout\n");
    printf("  -m, --max-vms NUM    Maximum number of VMs to monitor (default: 100)\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s                   Run in foreground with default settings\n", program_name);
    printf("  %s -v -l 3           Run with verbose debug logging\n", program_name);
    printf("  %s -d -f /var/log/xenserver-antivirus.log  Run as daemon with file logging\n", program_name);
}

/* 守护进程化 */
int daemonize() {
    pid_t pid = fork();
    
    if (pid < 0) {
        av_log(AV_LOG_ERROR, "Failed to fork daemon process");
        return AV_ERROR_INIT;
    }
    
    if (pid > 0) {
        /* 父进程退出 */
        exit(0);
    }
    
    /* 子进程继续 */
    if (setsid() < 0) {
        av_log(AV_LOG_ERROR, "Failed to create new session");
        return AV_ERROR_INIT;
    }
    
    /* 改变工作目录到根目录 */
    if (chdir("/") < 0) {
        av_log(AV_LOG_ERROR, "Failed to change working directory");
        return AV_ERROR_INIT;
    }
    
    /* 关闭标准文件描述符 */
    close(STDIN_FILENO);
    close(STDOUT_FILENO);
    close(STDERR_FILENO);
    
    av_log(AV_LOG_INFO, "Successfully daemonized");
    return AV_SUCCESS;
}

/* 主函数 */
int main(int argc, char* argv[]) {
    int ret = AV_SUCCESS;
    int daemon_mode = 0;
    int log_level = AV_LOG_INFO;
    char* log_file = NULL;
    int max_vms = 100;
    
    /* 命令行参数解析 */
    static struct option long_options[] = {
        {"help",      no_argument,       0, 'h'},
        {"verbose",   no_argument,       0, 'v'},
        {"daemon",    no_argument,       0, 'd'},
        {"log-level", required_argument, 0, 'l'},
        {"log-file",  required_argument, 0, 'f'},
        {"max-vms",   required_argument, 0, 'm'},
        {0, 0, 0, 0}
    };
    
    int option_index = 0;
    int c;
    
    while ((c = getopt_long(argc, argv, "hvdl:f:m:", long_options, &option_index)) != -1) {
        switch (c) {
            case 'h':
                print_usage(argv[0]);
                return 0;
            case 'v':
                log_level = AV_LOG_DEBUG;
                break;
            case 'd':
                daemon_mode = 1;
                break;
            case 'l':
                log_level = atoi(optarg);
                if (log_level < AV_LOG_ERROR || log_level > AV_LOG_DEBUG) {
                    fprintf(stderr, "Invalid log level: %s\n", optarg);
                    return 1;
                }
                break;
            case 'f':
                log_file = optarg;
                break;
            case 'm':
                max_vms = atoi(optarg);
                if (max_vms <= 0 || max_vms > 1000) {
                    fprintf(stderr, "Invalid max VMs: %s (must be 1-1000)\n", optarg);
                    return 1;
                }
                break;
            case '?':
                print_usage(argv[0]);
                return 1;
            default:
                break;
        }
    }
    
    /* 初始化日志系统 */
    av_log_init(log_level, log_file);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Host Service starting...");
    av_log(AV_LOG_INFO, "Version: %s, Build: %s %s", AV_VERSION, __DATE__, __TIME__);
    av_log(AV_LOG_INFO, "Max VMs: %d", max_vms);
    
    /* 检查运行权限 */
    if (geteuid() != 0) {
        av_log(AV_LOG_ERROR, "This program must be run as root");
        return 1;
    }
    
    /* 守护进程化 */
    if (daemon_mode) {
        ret = daemonize();
        if (ret != AV_SUCCESS) {
            av_log(AV_LOG_ERROR, "Failed to daemonize process");
            return 1;
        }
    }
    
    /* 设置信号处理 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGPIPE, SIG_IGN);  /* 忽略SIGPIPE信号 */
    
    /* 初始化服务 */
    g_service.max_vms = max_vms;
    ret = av_host_service_init(&g_service);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to initialize host service");
        return 1;
    }
    
    /* 启动服务 */
    ret = av_host_service_start(&g_service);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to start host service");
        av_host_service_cleanup(&g_service);
        return 1;
    }
    
    av_log(AV_LOG_INFO, "Host service started successfully, entering main loop");
    
    /* 主循环 */
    while (!g_service.shutdown_requested) {
        av_log(AV_LOG_DEBUG, "Host service main loop iteration");
        
        /* 使用可中断的sleep */
        struct timespec sleep_time = {10, 0};
        struct timespec remaining;
        
        if (nanosleep(&sleep_time, &remaining) == -1) {
            if (errno == EINTR) {
                av_log(AV_LOG_DEBUG, "Main loop sleep interrupted by signal");
                /* 检查是否需要退出 */
                if (g_service.shutdown_requested) {
                    break;
                }
            }
        }
    }
    
    /* 清理资源 */
    av_log(AV_LOG_INFO, "Shutting down host service...");
    av_host_service_stop(&g_service);
    av_host_service_cleanup(&g_service);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Host Service shutdown complete");
    av_log_cleanup();
    
    return (ret == AV_SUCCESS) ? 0 : 1;
}