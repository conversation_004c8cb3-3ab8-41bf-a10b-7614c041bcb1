#include "test_common.h"
#include "../host/av_host.h"
#include <string.h>
#include <pthread.h>
#include <unistd.h>

/* 测试用的共享内存模拟 */
static av_shared_memory_layout_t test_shared_memory;

/* 测试监听扫描请求功能 */
int test_monitor_scan_requests(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 初始化测试共享内存 */
    memset(&test_shared_memory, 0, sizeof(test_shared_memory));
    
    /* 模拟扫描请求 */
    strncpy(test_shared_memory.command, AV_SCAN_REQUEST, AV_COMMAND_SIZE - 1);
    
    /* 测试检查扫描请求 */
    int ret = av_check_shared_memory_command(&test_shared_memory, AV_SCAN_REQUEST);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should detect scan request");
    
    /* 测试发送响应 */
    ret = av_send_scan_response(&service, &test_shared_memory, AV_SCAN_RESPONSE);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should send scan response successfully");
    
    /* 验证响应是否写入 */
    ret = av_check_shared_memory_command(&test_shared_memory, AV_SCAN_RESPONSE);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should find scan response in shared memory");
    
    return 0;
}

/* 测试共享内存命令操作 */
int test_shared_memory_commands(void) {
    /* 初始化测试共享内存 */
    memset(&test_shared_memory, 0, sizeof(test_shared_memory));
    
    /* 测试写入命令 */
    int ret = av_write_shared_memory_command(&test_shared_memory, AV_SCAN_REQUEST);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should write command successfully");
    
    /* 测试读取命令 */
    char command[AV_COMMAND_SIZE];
    ret = av_read_shared_memory_command(&test_shared_memory, command, sizeof(command));
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should read command successfully");
    TEST_ASSERT_STRING_EQUAL(AV_SCAN_REQUEST, command, "Command should match");
    
    /* 测试检查命令 */
    ret = av_check_shared_memory_command(&test_shared_memory, AV_SCAN_REQUEST);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should find expected command");
    
    ret = av_check_shared_memory_command(&test_shared_memory, AV_SCAN_RESPONSE);
    TEST_ASSERT_EQUAL(AV_ERROR_PROTOCOL, ret, "Should not find different command");
    
    /* 测试清除命令 */
    ret = av_clear_shared_memory_command(&test_shared_memory);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should clear command successfully");
    
    ret = av_check_shared_memory_command(&test_shared_memory, AV_SCAN_REQUEST);
    TEST_ASSERT_EQUAL(AV_ERROR_PROTOCOL, ret, "Should not find command after clearing");
    
    return 0;
}

/* 测试VM监听启用/禁用 */
int test_vm_monitoring_control(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 初始化服务 */
    pthread_mutex_init(&service.context_mutex, NULL);
    
    const char* test_uuid = "12345678-1234-1234-1234-123456789abc";
    
    /* 添加VM上下文 */
    int ret = av_add_vm_context(&service, test_uuid);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should add VM context successfully");
    
    /* 查找上下文 */
    av_context_t* ctx = av_find_vm_context(&service, test_uuid);
    TEST_ASSERT(ctx != NULL, "Should find VM context");
    
    /* 设置映射地址（模拟） */
    ctx->mapped_addr = &test_shared_memory;
    
    /* 启用监听 */
    ret = av_enable_vm_monitoring(&service, test_uuid);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should enable monitoring successfully");
    TEST_ASSERT_EQUAL(1, ctx->monitoring_active, "Monitoring should be active");
    
    /* 禁用监听 */
    ret = av_disable_vm_monitoring(&service, test_uuid);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should disable monitoring successfully");
    TEST_ASSERT_EQUAL(0, ctx->monitoring_active, "Monitoring should be inactive");
    
    /* 清理 */
    av_remove_vm_context(&service, test_uuid);
    pthread_mutex_destroy(&service.context_mutex);
    
    return 0;
}

/* 测试无效参数处理 */
int test_invalid_parameters(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 测试NULL参数 */
    int ret = av_monitor_scan_requests(NULL, &test_shared_memory);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL service");
    
    ret = av_monitor_scan_requests(&service, NULL);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL shared memory");
    
    ret = av_send_scan_response(NULL, &test_shared_memory, AV_SCAN_RESPONSE);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL service");
    
    ret = av_send_scan_response(&service, NULL, AV_SCAN_RESPONSE);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL shared memory");
    
    ret = av_send_scan_response(&service, &test_shared_memory, NULL);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL response");
    
    return 0;
}

/* 测试响应时间性能 */
int test_response_time_performance(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 初始化测试共享内存 */
    memset(&test_shared_memory, 0, sizeof(test_shared_memory));
    
    /* 测试多次响应，确保性能稳定 */
    for (int i = 0; i < 10; i++) {
        /* 写入请求 */
        av_write_shared_memory_command(&test_shared_memory, AV_SCAN_REQUEST);
        
        /* 发送响应并测量时间 */
        struct timespec start, end;
        clock_gettime(CLOCK_MONOTONIC, &start);
        
        int ret = av_send_scan_response(&service, &test_shared_memory, AV_SCAN_RESPONSE);
        
        clock_gettime(CLOCK_MONOTONIC, &end);
        
        TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Response should succeed");
        
        /* 计算响应时间 */
        long response_time_ms = (end.tv_sec - start.tv_sec) * 1000 +
                               (end.tv_nsec - start.tv_nsec) / 1000000;
        
        /* 响应时间应该小于100毫秒 */
        TEST_ASSERT(response_time_ms < 100, "Response time should be under 100ms");
        
        /* 清除命令准备下次测试 */
        av_clear_shared_memory_command(&test_shared_memory);
    }
    
    return 0;
}

/* 主测试函数 */
int main(void) {
    test_init();
    
    /* 运行测试 */
    RUN_TEST(test_shared_memory_commands);
    RUN_TEST(test_monitor_scan_requests);
    RUN_TEST(test_vm_monitoring_control);
    RUN_TEST(test_invalid_parameters);
    RUN_TEST(test_response_time_performance);
    
    /* 输出测试结果 */
    test_summary();
    
    return (tests_failed > 0) ? 1 : 0;
}