# Linux虚拟机防病毒客户端Makefile

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g -D_GNU_SOURCE

# 检查是否有Xen库
XEN_LIBS_AVAILABLE := $(shell pkg-config --exists xencontrol xenstore 2>/dev/null && echo yes || echo no)

ifeq ($(XEN_LIBS_AVAILABLE),yes)
    CFLAGS += -DHAVE_XEN_LIBS
    LDFLAGS = -lxencontrol -lxenstore -lpthread
else
    LDFLAGS = -lpthread
endif

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj
BINDIR = bin

# 源文件和目标文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
CLIENT_SOURCES = $(wildcard $(SRCDIR)/*.c)
SOURCES = $(COMMON_SOURCES) $(CLIENT_SOURCES)
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/av_linux_client

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I/usr/include/xen

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Linking $@..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Build complete: $@"

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $<..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning build files..."
	rm -rf $(OBJDIR) $(BINDIR)

# 安装程序
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/av_linux_client
	@echo "Installation complete"

# 卸载程序
uninstall:
	@echo "Uninstalling av_linux_client..."
	sudo rm -f /usr/local/bin/av_linux_client
	@echo "Uninstall complete"

# 检查依赖
check-deps:
	@echo "Checking dependencies..."
	@pkg-config --exists xencontrol || (echo "ERROR: libxencontrol not found" && exit 1)
	@pkg-config --exists xenstore || (echo "ERROR: libxenstore not found" && exit 1)
	@echo "All dependencies found"

# 运行程序（需要root权限）
run: $(TARGET)
	@echo "Running $(TARGET) (requires root privileges)..."
	sudo $(TARGET)

# 调试版本
debug: CFLAGS += -DDEBUG -g3
debug: $(TARGET)

# 发布版本
release: CFLAGS += -DNDEBUG -O3
release: clean $(TARGET)

# 代码格式化
format:
	@echo "Formatting code..."
	find . -name "*.c" -o -name "*.h" | xargs clang-format -i

# 静态分析
analyze:
	@echo "Running static analysis..."
	cppcheck --enable=all --std=c99 $(SRCDIR) $(COMMONDIR)

# 帮助信息
help:
	@echo "Available targets:"
	@echo "  all        - Build the program (default)"
	@echo "  clean      - Remove build files"
	@echo "  install    - Install the program to /usr/local/bin"
	@echo "  uninstall  - Remove the installed program"
	@echo "  check-deps - Check for required dependencies"
	@echo "  run        - Build and run the program"
	@echo "  debug      - Build debug version"
	@echo "  release    - Build optimized release version"
	@echo "  format     - Format source code"
	@echo "  analyze    - Run static code analysis"
	@echo "  help       - Show this help message"

.PHONY: all clean install uninstall check-deps run debug release format analyze help directories