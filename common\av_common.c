#include "av_common.h"
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include <ctype.h>
#include <time.h>
#include <unistd.h>

/* 全局日志配置 */
static av_log_level_t g_log_level = AV_LOG_INFO;
static FILE* g_log_file = NULL;

/* 错误码到字符串的映射 */
const char* av_error_string(av_error_code_t error) {
    switch (error) {
        case AV_SUCCESS:
            return "Success";
        case AV_ERROR_MEMORY_ALLOC:
            return "Memory allocation failed";
        case AV_ERROR_PERMISSION:
            return "Permission denied";
        case AV_ERROR_XEN_INTERFACE:
            return "Xen interface error";
        case AV_ERROR_VM_NOT_FOUND:
            return "Virtual machine not found";
        case AV_ERROR_XENSTORE:
            return "XenStore operation failed";
        case AV_ERROR_TIMEOUT:
            return "Operation timeout";
        case AV_ERROR_PROTOCOL:
            return "Protocol error";
        case AV_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        case AV_ERROR_NOT_INITIALIZED:
            return "Not initialized";
        case AV_ERROR_ALREADY_EXISTS:
            return "Already exists";
        case AV_ERROR_XC_INTERFACE:
            return "Xenctrl interface error";
        case AV_ERROR_XENSTORE_OPEN:
            return "XenStore open failed";
        case AV_ERROR_XENSTORE_READ:
            return "XenStore read failed";
        case AV_ERROR_XENSTORE_WATCH:
            return "XenStore watch failed";
        case AV_ERROR_BUFFER_TOO_SMALL:
            return "Buffer too small";
        case AV_ERROR_MEMORY_MAP:
            return "Memory mapping failed";
        case AV_ERROR_MEMORY_UNMAP:
            return "Memory unmapping failed";
        case AV_ERROR_MUTEX_INIT:
            return "Mutex initialization failed";
        case AV_ERROR_COND_INIT:
            return "Condition variable initialization failed";
        case AV_ERROR_THREAD_CREATE:
            return "Thread creation failed";
        case AV_ERROR_FORK:
            return "Fork failed";
        case AV_ERROR_SETSID:
            return "Setsid failed";
        case AV_ERROR_CHDIR:
            return "Chdir failed";
        default:
            return "Unknown error";
    }
}

/* 日志初始化函数 */
void av_log_init(av_log_level_t level, const char* log_file) {
    g_log_level = level;
    
    if (log_file) {
        g_log_file = fopen(log_file, "a");
        if (!g_log_file) {
            fprintf(stderr, "Warning: Failed to open log file %s, using stdout\n", log_file);
            g_log_file = stdout;
        }
    } else {
        g_log_file = stdout;
    }
}

/* 日志清理函数 */
void av_log_cleanup(void) {
    if (g_log_file && g_log_file != stdout && g_log_file != stderr) {
        fclose(g_log_file);
        g_log_file = NULL;
    }
}

/* 日志输出函数 */
void av_log(av_log_level_t level, const char* format, ...) {
    if (level > g_log_level) {
        return;  /* 过滤低优先级日志 */
    }
    
    const char* level_str[] = {"ERROR", "WARN", "INFO", "DEBUG"};
    time_t now;
    struct tm* tm_info;
    char timestamp[64];
    va_list args;
    FILE* output = g_log_file ? g_log_file : stdout;
    
    /* 获取当前时间 */
    time(&now);
    tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);
    
    /* 输出日志级别和时间戳 */
    fprintf(output, "[%s] %s [PID:%d]: ", level_str[level], timestamp, getpid());
    
    /* 输出格式化消息 */
    va_start(args, format);
    vfprintf(output, format, args);
    va_end(args);
    
    fprintf(output, "\n");
    fflush(output);
}

/* UUID格式验证 */
int av_validate_uuid(const char* uuid) {
    if (!uuid) {
        return AV_ERROR_INVALID_PARAM;
    }
    
    /* UUID格式: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx */
    if (strlen(uuid) != 36) {
        return AV_ERROR_INVALID_PARAM;
    }
    
    /* 检查连字符位置 */
    if (uuid[8] != '-' || uuid[13] != '-' || uuid[18] != '-' || uuid[23] != '-') {
        return AV_ERROR_INVALID_PARAM;
    }
    
    /* 检查其他字符是否为十六进制 */
    for (int i = 0; i < 36; i++) {
        if (i == 8 || i == 13 || i == 18 || i == 23) {
            continue; /* 跳过连字符 */
        }
        if (!isxdigit(uuid[i])) {
            return AV_ERROR_INVALID_PARAM;
        }
    }
    
    return AV_SUCCESS;
}

/* 构建XenStore路径 */
int av_build_xenstore_path(const char* vm_uuid, const char* key, char* path, size_t path_size) {
    if (!vm_uuid || !key || !path || path_size == 0) {
        return AV_ERROR_INVALID_PARAM;
    }
    
    /* 验证UUID格式 */
    int ret = av_validate_uuid(vm_uuid);
    if (ret != AV_SUCCESS) {
        return ret;
    }
    
    /* 构建路径: /guest/<vm_uuid>/data/<key> */
    int written = snprintf(path, path_size, "%s/%s%s/%s", 
                          AV_XENSTORE_BASE_PATH, vm_uuid, 
                          AV_XENSTORE_DATA_PATH, key);
    
    if (written < 0 || (size_t)written >= path_size) {
        return AV_ERROR_MEMORY_ALLOC;
    }
    
    return AV_SUCCESS;
}