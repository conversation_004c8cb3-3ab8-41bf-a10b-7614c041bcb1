# XenServer防病毒Linux客户端 - 真实静态编译版本（修复版）
# 基于成功的Makefile.real，包含信号处理修复

CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE -static

# 检查是否有Xen库
XEN_LIBS_AVAILABLE := $(shell pkg-config --exists xencontrol xenstore 2>/dev/null && echo yes || echo no)

# 如果没有Xen库，尝试继续编译（可能在WSL环境中）
ifeq ($(XEN_LIBS_AVAILABLE),yes)
    # 获取Xen库的编译和链接标志
    XEN_CFLAGS := $(shell pkg-config --cflags xencontrol xenstore 2>/dev/null)
    XEN_LDFLAGS := $(shell pkg-config --libs --static xencontrol xenstore 2>/dev/null)
    
    # 如果pkg-config不支持--static，手动指定静态库
    ifeq ($(XEN_LDFLAGS),)
        XEN_LDFLAGS = -lxenctrl -lxenstore -lxengnttab -lxentoollog -lxencall -lxenevtchn -lxenforeignmemory -lxendevicemodel -lxentoolcore
    endif
    
    CFLAGS += $(XEN_CFLAGS)
    LDFLAGS = $(XEN_LDFLAGS) -lpthread -static
else
    # WSL或非Xen环境，使用基本链接
    LDFLAGS = -lpthread -static
    $(warning Xen libraries not found - building without Xen support)
endif

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj_real_static_fixed
BINDIR = bin

# 源文件和目标文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
CLIENT_SOURCES = av_linux_client_real.c main.c
SOURCES = $(COMMON_SOURCES) $(CLIENT_SOURCES)
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/av_linux_client_real_static_fixed

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I.

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Linking real client static executable with fixes..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Real client static build with fixes completed: $(TARGET)"
	@echo "Built as static binary with fixes"
	@echo "Binary size: $$(ls -lh $(TARGET) | awk '{print $$5}')"
	@echo "Dependencies: $$(ldd $(TARGET) 2>/dev/null || echo 'Static binary - no dependencies')"

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $< (real client version with fixes)..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning up real client version..."
	rm -rf $(OBJDIR) $(BINDIR)/av_linux_client_real_static_fixed

# 安装
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(TARGET)
	@echo "Installed to /usr/local/bin/$(TARGET)"

# 测试编译
test: $(TARGET)
	@echo "Testing real client static binary..."
	@echo "File info:"
	file $(TARGET)
	@echo "Size:"
	ls -lh $(TARGET)
	@echo "Dependencies:"
	ldd $(TARGET) 2>/dev/null || echo "Static binary - no dependencies"
	@echo "Testing help output:"
	./$(TARGET) --help || true

# 创建发布包
package: $(TARGET)
	@echo "Creating real client release package..."
	mkdir -p release-real-client
	cp $(TARGET) release-real-client/
	cp README.md release-real-client/ 2>/dev/null || echo "README.md not found"
	tar -czf xenserver-antivirus-client-real-static-fixed.tar.gz release-real-client/
	rm -rf release-real-client/
	@echo "Real client release package created: xenserver-antivirus-client-real-static-fixed.tar.gz"

# 调试版本
debug: CFLAGS += -DDEBUG -g3 -O0
debug: $(TARGET)

# 显示编译信息
info:
	@echo "=== Real Client Build Configuration ==="
	@echo "CC: $(CC)"
	@echo "CFLAGS: $(CFLAGS)"
	@echo "STATIC_CFLAGS: $(STATIC_CFLAGS)"
	@echo "INCLUDES: $(INCLUDES)"
	@echo "LIBS: $(LIBS)"
	@echo "Sources: $(CLIENT_SOURCES) $(COMMON_SOURCES)"
	@echo "Target: $(TARGET)"
	@echo "======================================"

.PHONY: all clean install test package debug info