#!/bin/bash

# XenServer防病毒系统 - Citrix Hypervisor xenstore访问修复
# 解决Citrix环境中缺少/dev/xen/xenstore的问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "=================================================================="
echo "Citrix Hypervisor xenstore访问修复"
echo "=================================================================="

# 检查root权限
if [ $(id -u) -ne 0 ]; then
    log_error "必须以root权限运行"
    exit 1
fi

log_info "开始修复Citrix Hypervisor xenstore访问..."

# 1. 检查Citrix环境
log_info "检查Citrix Hypervisor环境..."
if [ -f "/etc/xensource-inventory" ]; then
    log_success "✅ 确认Citrix Hypervisor环境"
    PRODUCT_VERSION=$(grep "PRODUCT_VERSION" /etc/xensource-inventory | cut -d= -f2 | tr -d "'")
    echo "产品版本: $PRODUCT_VERSION"
else
    log_warning "⚠️  未检测到标准Citrix Hypervisor环境"
fi
echo ""

# 2. 检查oxenstored和xenstore访问方式
log_info "检查xenstore访问方式..."

# 检查oxenstored进程
if pgrep -x "oxenstored" > /dev/null; then
    log_success "✅ oxenstored正在运行"
    OXENSTORED_PID=$(pgrep -x oxenstored)
    echo "oxenstored PID: $OXENSTORED_PID"
    
    # 检查oxenstored的socket
    if [ -S "/var/run/xenstored/socket" ]; then
        log_success "✅ xenstore socket存在: /var/run/xenstored/socket"
    elif [ -S "/var/lib/xenstored/socket" ]; then
        log_success "✅ xenstore socket存在: /var/lib/xenstored/socket"
    else
        log_warning "⚠️  未找到xenstore socket"
    fi
else
    log_error "❌ oxenstored未运行"
fi
echo ""

# 3. 创建xenstore设备的替代方案
log_info "创建xenstore访问的替代方案..."

# 方案1: 创建到socket的符号链接
if [ -S "/var/run/xenstored/socket" ]; then
    if [ ! -e "/dev/xen/xenstore" ]; then
        ln -sf /var/run/xenstored/socket /dev/xen/xenstore 2>/dev/null && {
            log_success "✅ 创建xenstore符号链接到socket"
        } || {
            log_warning "⚠️  无法创建xenstore符号链接"
        }
    fi
elif [ -S "/var/lib/xenstored/socket" ]; then
    if [ ! -e "/dev/xen/xenstore" ]; then
        ln -sf /var/lib/xenstored/socket /dev/xen/xenstore 2>/dev/null && {
            log_success "✅ 创建xenstore符号链接到socket"
        } || {
            log_warning "⚠️  无法创建xenstore符号链接"
        }
    fi
fi

# 方案2: 检查/proc/xen/xenstore
if [ -e "/proc/xen/xenstore" ]; then
    log_success "✅ /proc/xen/xenstore存在"
    if [ ! -e "/dev/xen/xenstore" ]; then
        ln -sf /proc/xen/xenstore /dev/xen/xenstore 2>/dev/null && {
            log_success "✅ 创建xenstore符号链接到/proc/xen/xenstore"
        } || {
            log_warning "⚠️  无法创建xenstore符号链接"
        }
    fi
else
    log_warning "⚠️  /proc/xen/xenstore不存在"
fi

# 方案3: 创建字符设备（如果知道设备号）
if [ ! -e "/dev/xen/xenstore" ]; then
    log_info "尝试创建xenstore字符设备..."
    # 在某些系统中，xenstore的主设备号是10，次设备号是60
    mknod /dev/xen/xenstore c 10 60 2>/dev/null && {
        chmod 666 /dev/xen/xenstore
        log_success "✅ 创建xenstore字符设备成功"
    } || {
        log_warning "⚠️  无法创建xenstore字符设备"
    }
fi

echo ""

# 4. 检查创建结果
log_info "检查xenstore设备状态..."
if [ -e "/dev/xen/xenstore" ]; then
    log_success "✅ /dev/xen/xenstore现在存在"
    ls -la /dev/xen/xenstore
    
    # 测试设备可访问性
    if [ -r "/dev/xen/xenstore" ] && [ -w "/dev/xen/xenstore" ]; then
        log_success "✅ xenstore设备可读写"
    else
        log_warning "⚠️  xenstore设备权限问题"
        chmod 666 /dev/xen/xenstore 2>/dev/null || true
    fi
else
    log_error "❌ 仍然无法创建/dev/xen/xenstore"
fi
echo ""

# 5. 设置Citrix特定的环境变量
log_info "设置Citrix Hypervisor环境变量..."
export XENSTORED_ROOTDIR="/var/lib/xenstored"
export XENSTORE_DOMAIN_INTERFACE=1
export XEN_DOMAIN_INTERFACE=1
export XENSTORED_PATH="/var/run/xenstored"

# 创建环境变量文件
cat > /etc/profile.d/xenserver-antivirus.sh << 'EOF'
# XenServer防病毒系统环境变量
export XENSTORED_ROOTDIR="/var/lib/xenstored"
export XENSTORE_DOMAIN_INTERFACE=1
export XEN_DOMAIN_INTERFACE=1
export XENSTORED_PATH="/var/run/xenstored"
EOF

log_success "✅ 环境变量已设置并保存到/etc/profile.d/xenserver-antivirus.sh"
echo ""

# 6. 测试xenstore访问
log_info "测试xenstore访问..."

# 使用xenstore-ls命令测试
if command -v xenstore-ls >/dev/null 2>&1; then
    if xenstore-ls / >/dev/null 2>&1; then
        log_success "✅ xenstore-ls命令正常"
    else
        log_warning "⚠️  xenstore-ls命令失败"
    fi
else
    log_warning "⚠️  xenstore-ls命令不可用"
fi

# 使用xe命令测试xenstore
if command -v xe >/dev/null 2>&1; then
    if xe host-param-list >/dev/null 2>&1; then
        log_success "✅ xe命令可以访问xenstore数据"
    else
        log_warning "⚠️  xe命令访问xenstore失败"
    fi
fi

echo ""

# 7. 创建libxenctrl测试程序（使用Citrix特定方法）
log_info "测试libxenctrl在Citrix环境中的访问..."

cat > /tmp/citrix_xen_test.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>
#include <unistd.h>
#include <xenctrl.h>
#include <xenstore.h>

int main() {
    printf("Testing Citrix Hypervisor Xen access...\n");
    
    // 设置环境变量
    setenv("XENSTORED_ROOTDIR", "/var/lib/xenstored", 1);
    setenv("XENSTORE_DOMAIN_INTERFACE", "1", 1);
    setenv("XEN_DOMAIN_INTERFACE", "1", 1);
    
    // 测试xenctrl接口
    printf("1. Testing xc_interface_open...\n");
    xc_interface *xc_handle = xc_interface_open(NULL, NULL, 0);
    if (xc_handle == NULL) {
        printf("ERROR: Cannot open xc interface: %s\n", strerror(errno));
        return 1;
    }
    printf("SUCCESS: XC interface opened\n");
    
    // 测试域列表获取
    printf("2. Testing xc_domain_getinfolist...\n");
    xc_domaininfo_t info[10];
    int ret = xc_domain_getinfolist(xc_handle, 0, 10, info);
    if (ret < 0) {
        printf("ERROR: xc_domain_getinfolist failed: %s (errno: %d)\n", strerror(errno), errno);
        
        // 尝试不同的方法
        printf("3. Trying alternative method...\n");
        ret = xc_domain_getinfolist(xc_handle, 0, 1, info);
        if (ret < 0) {
            printf("ERROR: Alternative method also failed\n");
            xc_interface_close(xc_handle);
            return 1;
        }
    }
    
    printf("SUCCESS: Found %d domains\n", ret);
    for (int i = 0; i < ret; i++) {
        printf("  Domain %d: ID=%d, flags=0x%x\n", i, info[i].domid, info[i].flags);
    }
    
    xc_interface_close(xc_handle);
    
    // 测试xenstore接口
    printf("4. Testing xenstore interface...\n");
    struct xs_handle *xs = xs_open(0);
    if (xs == NULL) {
        printf("WARNING: Cannot open xenstore interface: %s\n", strerror(errno));
    } else {
        printf("SUCCESS: Xenstore interface opened\n");
        xs_close(xs);
    }
    
    return 0;
}
EOF

# 编译并运行测试
if gcc -o /tmp/citrix_xen_test /tmp/citrix_xen_test.c -lxenctrl -lxenstore 2>/dev/null; then
    log_info "运行Citrix Xen访问测试..."
    if /tmp/citrix_xen_test; then
        log_success "✅ Citrix Xen访问测试通过"
    else
        log_error "❌ Citrix Xen访问测试失败"
    fi
    rm -f /tmp/citrix_xen_test
else
    log_warning "⚠️  无法编译测试程序"
fi
rm -f /tmp/citrix_xen_test.c

echo ""

# 8. 测试防病毒程序
PROGRAM_PATH="/usr/local/bin/xenserver-antivirus-glibc217-static"
if [ -f "$PROGRAM_PATH" ]; then
    log_info "使用Citrix环境设置测试防病毒程序..."
    
    # 加载环境变量
    source /etc/profile.d/xenserver-antivirus.sh
    
    echo "运行程序测试（15秒超时）..."
    timeout 15s "$PROGRAM_PATH" --test 2>&1 | head -30
    
    echo ""
    log_info "如果测试成功，使用以下命令运行程序:"
    echo "source /etc/profile.d/xenserver-antivirus.sh"
    echo "sudo $PROGRAM_PATH -v"
else
    log_warning "⚠️  程序未找到: $PROGRAM_PATH"
fi

echo ""
echo "=================================================================="
echo "Citrix Hypervisor xenstore修复完成！"
echo "=================================================================="

echo ""
log_info "修复摘要:"
echo "- xenstore设备: $([ -e /dev/xen/xenstore ] && echo '已创建' || echo '创建失败')"
echo "- oxenstored状态: $(pgrep -x oxenstored >/dev/null && echo '运行中' || echo '未运行')"
echo "- 环境变量: 已设置到 /etc/profile.d/xenserver-antivirus.sh"

echo ""
log_info "使用方法:"
echo "1. 加载环境变量: source /etc/profile.d/xenserver-antivirus.sh"
echo "2. 运行程序: sudo $PROGRAM_PATH -v"

echo ""
log_info "如果问题仍然存在，可能需要:"
echo "1. 检查Citrix Hypervisor的特殊权限配置"
echo "2. 联系Citrix技术支持获取xenstore访问方法"
echo "3. 考虑使用Citrix提供的API而不是直接的libxenctrl"
