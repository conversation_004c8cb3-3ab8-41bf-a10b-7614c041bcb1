#!/bin/bash

# XenServer防病毒系统 - Xen权限诊断和修复脚本
# 解决 "Permission denied" 错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示脚本信息
show_header() {
    echo "=================================================================="
    echo "XenServer防病毒系统 - Xen权限诊断和修复"
    echo "=================================================================="
    echo "目标: 解决 'Permission denied' 错误"
    echo "环境: Citrix Hypervisor / XenServer"
    echo "时间: $(date)"
    echo "=================================================================="
    echo ""
}

# 检查基本权限
check_basic_permissions() {
    log_info "检查基本权限..."
    
    # 检查当前用户
    CURRENT_USER=$(whoami)
    CURRENT_UID=$(id -u)
    
    echo "当前用户: $CURRENT_USER (UID: $CURRENT_UID)"
    
    if [ "$CURRENT_UID" -ne 0 ]; then
        log_error "必须以root权限运行此脚本"
        log_info "请使用: sudo $0"
        exit 1
    else
        log_success "✅ 以root权限运行"
    fi
    
    # 检查用户组
    log_info "检查用户组..."
    groups
    
    echo ""
}

# 检查Xen服务状态
check_xen_services() {
    log_info "检查Xen服务状态..."
    
    # 检查xenstored
    if pgrep -x "xenstored" > /dev/null; then
        log_success "✅ xenstored 服务正在运行"
        echo "xenstored PID: $(pgrep -x xenstored)"
    else
        log_error "❌ xenstored 服务未运行"
        log_info "尝试启动xenstored..."
        if systemctl start xenstored 2>/dev/null || service xenstored start 2>/dev/null; then
            log_success "✅ xenstored 服务已启动"
        else
            log_warning "⚠️  无法启动xenstored服务"
        fi
    fi
    
    # 检查xenconsoled
    if pgrep -x "xenconsoled" > /dev/null; then
        log_success "✅ xenconsoled 服务正在运行"
    else
        log_warning "⚠️  xenconsoled 服务未运行"
    fi
    
    # 检查xl命令
    if command -v xl >/dev/null 2>&1; then
        log_success "✅ xl 命令可用"
        if xl info >/dev/null 2>&1; then
            log_success "✅ xl info 命令正常"
        else
            log_error "❌ xl info 命令失败"
        fi
    else
        log_error "❌ xl 命令不可用"
    fi
    
    echo ""
}

# 检查Xen设备文件
check_xen_devices() {
    log_info "检查Xen设备文件..."
    
    # 检查/dev/xen目录
    if [ -d "/dev/xen" ]; then
        log_success "✅ /dev/xen 目录存在"
        echo "目录权限: $(ls -ld /dev/xen)"
        
        # 检查xenstore设备
        if [ -e "/dev/xen/xenstore" ]; then
            log_success "✅ /dev/xen/xenstore 设备存在"
            echo "设备权限: $(ls -l /dev/xen/xenstore)"
            
            # 测试读取权限
            if [ -r "/dev/xen/xenstore" ]; then
                log_success "✅ /dev/xen/xenstore 可读"
            else
                log_error "❌ /dev/xen/xenstore 不可读"
            fi
            
            # 测试写入权限
            if [ -w "/dev/xen/xenstore" ]; then
                log_success "✅ /dev/xen/xenstore 可写"
            else
                log_error "❌ /dev/xen/xenstore 不可写"
            fi
        else
            log_error "❌ /dev/xen/xenstore 设备不存在"
        fi
        
        # 检查其他Xen设备
        echo "其他Xen设备:"
        ls -la /dev/xen/ 2>/dev/null || echo "无法列出/dev/xen/内容"
    else
        log_error "❌ /dev/xen 目录不存在"
    fi
    
    echo ""
}

# 检查Xen模块
check_xen_modules() {
    log_info "检查Xen内核模块..."
    
    # 检查已加载的Xen模块
    if lsmod | grep -q xen; then
        log_success "✅ Xen模块已加载"
        echo "已加载的Xen模块:"
        lsmod | grep xen
    else
        log_warning "⚠️  未检测到Xen模块"
    fi
    
    # 检查/proc/xen
    if [ -d "/proc/xen" ]; then
        log_success "✅ /proc/xen 目录存在"
        echo "/proc/xen 内容:"
        ls -la /proc/xen/ 2>/dev/null || echo "无法列出/proc/xen/内容"
    else
        log_warning "⚠️  /proc/xen 目录不存在"
    fi
    
    echo ""
}

# 修复权限问题
fix_permissions() {
    log_info "尝试修复权限问题..."
    
    # 修复/dev/xen/xenstore权限
    if [ -e "/dev/xen/xenstore" ]; then
        log_info "修复 /dev/xen/xenstore 权限..."
        chmod 666 /dev/xen/xenstore 2>/dev/null && log_success "✅ 权限已修复" || log_warning "⚠️  权限修复失败"
    fi
    
    # 确保当前用户在xen组中
    if getent group xen >/dev/null 2>&1; then
        log_info "将当前用户添加到xen组..."
        usermod -a -G xen root 2>/dev/null && log_success "✅ 已添加到xen组" || log_warning "⚠️  添加到xen组失败"
    else
        log_warning "⚠️  xen组不存在"
    fi
    
    # 重启相关服务
    log_info "重启Xen相关服务..."
    if systemctl restart xenstored 2>/dev/null || service xenstored restart 2>/dev/null; then
        log_success "✅ xenstored 服务已重启"
    else
        log_warning "⚠️  xenstored 服务重启失败"
    fi
    
    echo ""
}

# 测试Xen功能
test_xen_functionality() {
    log_info "测试Xen功能..."
    
    # 测试xl info
    log_info "测试 xl info..."
    if xl info >/dev/null 2>&1; then
        log_success "✅ xl info 测试通过"
        echo "Xen版本信息:"
        xl info | head -5
    else
        log_error "❌ xl info 测试失败"
        xl info 2>&1 | head -5
    fi
    
    # 测试xl list
    log_info "测试 xl list..."
    if xl list >/dev/null 2>&1; then
        log_success "✅ xl list 测试通过"
        echo "虚拟机列表:"
        xl list
    else
        log_error "❌ xl list 测试失败"
        xl list 2>&1 | head -5
    fi
    
    echo ""
}

# 生成诊断报告
generate_diagnostic_report() {
    log_info "生成诊断报告..."
    
    REPORT_FILE="xen_diagnostic_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$REPORT_FILE" << EOF
XenServer防病毒系统 - Xen权限诊断报告
生成时间: $(date)
主机名: $(hostname)
系统信息: $(uname -a)

=== 用户信息 ===
当前用户: $(whoami)
用户ID: $(id)
用户组: $(groups)

=== Xen服务状态 ===
xenstored: $(pgrep -x xenstored >/dev/null && echo "运行中" || echo "未运行")
xenconsoled: $(pgrep -x xenconsoled >/dev/null && echo "运行中" || echo "未运行")

=== Xen设备文件 ===
/dev/xen 目录: $([ -d /dev/xen ] && echo "存在" || echo "不存在")
/dev/xen/xenstore: $([ -e /dev/xen/xenstore ] && echo "存在" || echo "不存在")
EOF

    if [ -d "/dev/xen" ]; then
        echo "/dev/xen 内容:" >> "$REPORT_FILE"
        ls -la /dev/xen/ >> "$REPORT_FILE" 2>&1
    fi

    cat >> "$REPORT_FILE" << EOF

=== Xen模块 ===
$(lsmod | grep xen || echo "未找到Xen模块")

=== xl命令测试 ===
xl info 结果:
$(xl info 2>&1 | head -10)

xl list 结果:
$(xl list 2>&1)

=== 系统日志 ===
最近的Xen相关日志:
$(dmesg | grep -i xen | tail -10)
EOF

    log_success "✅ 诊断报告已生成: $REPORT_FILE"
    echo ""
}

# 显示修复建议
show_recommendations() {
    echo "=================================================================="
    echo "修复建议"
    echo "=================================================================="
    
    echo "1. 确保以root权限运行程序:"
    echo "   sudo /usr/local/bin/xenserver-antivirus-glibc217-static"
    echo ""
    
    echo "2. 如果仍有权限问题，尝试以下命令:"
    echo "   chmod 666 /dev/xen/xenstore"
    echo "   systemctl restart xenstored"
    echo ""
    
    echo "3. 检查SELinux状态（如果适用）:"
    echo "   getenforce"
    echo "   setenforce 0  # 临时禁用"
    echo ""
    
    echo "4. 重启Xen服务:"
    echo "   systemctl restart xenstored"
    echo "   systemctl restart xenconsoled"
    echo ""
    
    echo "5. 如果问题持续，重启系统:"
    echo "   reboot"
    echo ""
    
    echo "6. 验证修复:"
    echo "   xl info"
    echo "   xl list"
    echo "   sudo /usr/local/bin/xenserver-antivirus-glibc217-static --test"
    echo ""
}

# 主函数
main() {
    show_header
    check_basic_permissions
    check_xen_services
    check_xen_devices
    check_xen_modules
    fix_permissions
    test_xen_functionality
    generate_diagnostic_report
    show_recommendations
    
    echo "=================================================================="
    log_success "权限诊断和修复完成！"
    echo "=================================================================="
}

# 运行主函数
main "$@"
