#include "test_common.h"
#include "../host/av_host.h"
#include <string.h>
#include <signal.h>
#include <unistd.h>
#include <sys/wait.h>

/* 测试资源管理器初始化和清理 */
int test_resource_manager_init_cleanup(void) {
    /* 测试初始化 */
    int ret = av_resource_manager_init();
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Resource manager init should succeed");
    
    /* 测试重复初始化 */
    ret = av_resource_manager_init();
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Repeated init should succeed");
    
    /* 测试清理 */
    av_resource_manager_cleanup();
    
    return 0;
}

/* 测试安全内存分配和释放 */
int test_safe_memory_allocation(void) {
    /* 初始化资源管理器 */
    av_resource_manager_init();
    
    /* 测试正常分配 */
    void* ptr1 = av_safe_malloc(1024, "test allocation 1");
    TEST_ASSERT(ptr1 != NULL, "Safe malloc should succeed");
    
    void* ptr2 = av_safe_malloc(2048, "test allocation 2");
    TEST_ASSERT(ptr2 != NULL, "Safe malloc should succeed");
    
    /* 测试获取统计信息 */
    av_resource_stats_t stats;
    int ret = av_resource_get_stats(&stats);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Get stats should succeed");
    TEST_ASSERT(stats.total_memory >= 3072, "Total memory should include allocations");
    TEST_ASSERT(stats.resource_count >= 2, "Resource count should include allocations");
    
    /* 测试释放 */
    av_safe_free(ptr1);
    av_safe_free(ptr2);
    
    /* 测试释放NULL指针 */
    av_safe_free(NULL);
    
    /* 清理 */
    av_resource_manager_cleanup();
    
    return 0;
}

/* 测试资源统计报告 */
int test_resource_statistics(void) {
    /* 初始化资源管理器 */
    av_resource_manager_init();
    
    /* 分配一些资源 */
    void* ptr1 = av_safe_malloc(512, "test memory 1");
    void* ptr2 = av_safe_malloc(1024, "test memory 2");
    
    /* 生成报告（验证不会崩溃） */
    av_resource_report();
    
    /* 获取统计信息 */
    av_resource_stats_t stats;
    int ret = av_resource_get_stats(&stats);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Get stats should succeed");
    
    /* 验证统计信息 */
    TEST_ASSERT(stats.total_memory >= 1536, "Total memory should be correct");
    TEST_ASSERT(stats.peak_memory >= stats.total_memory, "Peak memory should be >= current");
    TEST_ASSERT(stats.resource_count >= 2, "Resource count should be correct");
    
    /* 清理 */
    av_safe_free(ptr1);
    av_safe_free(ptr2);
    av_resource_manager_cleanup();
    
    return 0;
}

/* 测试资源限制设置 */
int test_resource_limits(void) {
    int ret = av_set_resource_limits();
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Set resource limits should succeed");
    
    return 0;
}

/* 测试错误字符串功能 */
int test_error_strings(void) {
    /* 测试所有定义的错误码 */
    const char* str = av_error_string(AV_SUCCESS);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Success", str, "Success string should match");
    
    str = av_error_string(AV_ERROR_MEMORY_ALLOC);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Memory allocation failed", str, "Memory error string should match");
    
    str = av_error_string(AV_ERROR_PERMISSION);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Permission denied", str, "Permission error string should match");
    
    str = av_error_string(AV_ERROR_XEN_INTERFACE);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Xen interface error", str, "Xen error string should match");
    
    str = av_error_string(AV_ERROR_VM_NOT_FOUND);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Virtual machine not found", str, "VM error string should match");
    
    str = av_error_string(AV_ERROR_XENSTORE);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("XenStore operation failed", str, "XenStore error string should match");
    
    str = av_error_string(AV_ERROR_TIMEOUT);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Operation timeout", str, "Timeout error string should match");
    
    str = av_error_string(AV_ERROR_PROTOCOL);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Protocol error", str, "Protocol error string should match");
    
    str = av_error_string(AV_ERROR_INVALID_PARAM);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Invalid parameter", str, "Invalid param error string should match");
    
    str = av_error_string(AV_ERROR_NOT_INITIALIZED);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Not initialized", str, "Not initialized error string should match");
    
    str = av_error_string(AV_ERROR_ALREADY_EXISTS);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Already exists", str, "Already exists error string should match");
    
    /* 测试未知错误码 */
    str = av_error_string(-999);
    TEST_ASSERT(str != NULL, "Error string should not be NULL");
    TEST_ASSERT_STRING_EQUAL("Unknown error", str, "Unknown error string should match");
    
    return 0;
}

/* 测试重试机制 */
static int retry_test_counter = 0;
static int retry_test_operation(void* params) {
    (void)params;
    retry_test_counter++;
    
    if (retry_test_counter < 3) {
        return AV_ERROR_TIMEOUT; /* 前两次失败 */
    }
    
    return AV_SUCCESS; /* 第三次成功 */
}

int test_retry_mechanism(void) {
    /* 重置计数器 */
    retry_test_counter = 0;
    
    /* 测试成功的重试 */
    int ret = av_retry_operation(retry_test_operation, NULL, 5, 1);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Retry operation should eventually succeed");
    TEST_ASSERT_EQUAL(3, retry_test_counter, "Operation should be called 3 times");
    
    /* 测试重试次数不足的情况 */
    retry_test_counter = 0;
    ret = av_retry_operation(retry_test_operation, NULL, 1, 1);
    TEST_ASSERT_EQUAL(AV_ERROR_TIMEOUT, ret, "Retry operation should fail with insufficient retries");
    TEST_ASSERT_EQUAL(2, retry_test_counter, "Operation should be called 2 times");
    
    /* 测试无效参数 */
    ret = av_retry_operation(NULL, NULL, 5, 1);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL operation");
    
    ret = av_retry_operation(retry_test_operation, NULL, -1, 1);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject negative retries");
    
    return 0;
}

/* 测试错误处理函数 */
int test_error_handling_function(void) {
    /* 测试错误处理（主要验证不会崩溃） */
    av_handle_error(AV_ERROR_MEMORY_ALLOC, "test context");
    av_handle_error(AV_ERROR_PERMISSION, "test context");
    av_handle_error(AV_ERROR_XEN_INTERFACE, "test context");
    av_handle_error(AV_ERROR_VM_NOT_FOUND, "test context");
    av_handle_error(AV_ERROR_XENSTORE, "test context");
    av_handle_error(AV_ERROR_TIMEOUT, "test context");
    av_handle_error(AV_ERROR_PROTOCOL, "test context");
    av_handle_error(AV_SUCCESS, "test context");
    
    /* 测试无上下文的错误处理 */
    av_handle_error(AV_ERROR_INVALID_PARAM, NULL);
    
    return 0;
}

/* 测试重试错误判断 */
int test_retry_error_judgment(void) {
    /* 测试可重试的错误 */
    TEST_ASSERT_EQUAL(1, av_should_retry_error(AV_ERROR_XENSTORE), "XenStore error should be retryable");
    TEST_ASSERT_EQUAL(1, av_should_retry_error(AV_ERROR_XEN_INTERFACE), "Xen interface error should be retryable");
    TEST_ASSERT_EQUAL(1, av_should_retry_error(AV_ERROR_TIMEOUT), "Timeout error should be retryable");
    TEST_ASSERT_EQUAL(1, av_should_retry_error(AV_ERROR_MEMORY_ALLOC), "Memory alloc error should be retryable");
    
    /* 测试不可重试的错误 */
    TEST_ASSERT_EQUAL(0, av_should_retry_error(AV_ERROR_INVALID_PARAM), "Invalid param error should not be retryable");
    TEST_ASSERT_EQUAL(0, av_should_retry_error(AV_ERROR_PERMISSION), "Permission error should not be retryable");
    TEST_ASSERT_EQUAL(0, av_should_retry_error(AV_ERROR_NOT_INITIALIZED), "Not initialized error should not be retryable");
    TEST_ASSERT_EQUAL(0, av_should_retry_error(AV_ERROR_VM_NOT_FOUND), "VM not found error should not be retryable");
    TEST_ASSERT_EQUAL(0, av_should_retry_error(AV_ERROR_PROTOCOL), "Protocol error should not be retryable");
    TEST_ASSERT_EQUAL(0, av_should_retry_error(AV_ERROR_ALREADY_EXISTS), "Already exists error should not be retryable");
    
    /* 测试未知错误 */
    TEST_ASSERT_EQUAL(0, av_should_retry_error(-999), "Unknown error should not be retryable");
    
    return 0;
}

/* 测试信号处理器安装 */
int test_signal_handler_installation(void) {
    /* 测试信号处理器安装 */
    int ret = av_install_signal_handlers();
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Signal handler installation should succeed");
    
    /* 测试信号处理器恢复 */
    av_restore_signal_handlers();
    
    return 0;
}

/* 主测试函数 */
int main(void) {
    test_init();
    
    /* 运行测试 */
    RUN_TEST(test_resource_manager_init_cleanup);
    RUN_TEST(test_safe_memory_allocation);
    RUN_TEST(test_resource_statistics);
    RUN_TEST(test_resource_limits);
    RUN_TEST(test_error_strings);
    RUN_TEST(test_retry_mechanism);
    RUN_TEST(test_error_handling_function);
    RUN_TEST(test_retry_error_judgment);
    RUN_TEST(test_signal_handler_installation);
    
    /* 输出测试结果 */
    test_summary();
    
    return (tests_failed > 0) ? 1 : 0;
}