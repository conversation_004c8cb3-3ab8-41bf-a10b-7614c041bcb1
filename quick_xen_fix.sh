#!/bin/bash

# XenServer防病毒系统 - 快速权限修复脚本
# 专门解决 "Permission denied" 错误

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "=================================================================="
echo "XenServer防病毒系统 - 快速权限修复"
echo "=================================================================="

# 检查root权限
if [ $(id -u) -ne 0 ]; then
    log_error "必须以root权限运行"
    echo "使用方法: sudo $0"
    exit 1
fi

log_info "开始权限修复..."

# 1. 检查并启动xenstored服务
log_info "检查xenstored服务..."
if ! pgrep -x "xenstored" > /dev/null; then
    log_warning "xenstored服务未运行，正在启动..."
    systemctl start xenstored 2>/dev/null || service xenstored start 2>/dev/null || {
        log_error "无法启动xenstored服务"
        exit 1
    }
    sleep 3
    if pgrep -x "xenstored" > /dev/null; then
        log_success "xenstored服务已启动"
    else
        log_error "xenstored服务启动失败"
        exit 1
    fi
else
    log_success "xenstored服务正在运行"
fi

# 2. 修复/dev/xen/xenstore权限
log_info "修复设备文件权限..."
if [ -e "/dev/xen/xenstore" ]; then
    chmod 666 /dev/xen/xenstore 2>/dev/null && {
        log_success "/dev/xen/xenstore权限已修复"
    } || {
        log_warning "/dev/xen/xenstore权限修复失败"
    }
    
    # 显示当前权限
    echo "当前权限: $(ls -l /dev/xen/xenstore)"
else
    log_error "/dev/xen/xenstore设备不存在"
    log_info "检查/dev/xen目录..."
    if [ -d "/dev/xen" ]; then
        echo "/dev/xen目录内容:"
        ls -la /dev/xen/
    else
        log_error "/dev/xen目录不存在"
    fi
fi

# 3. 确保用户在xen组中
log_info "检查用户组..."
if getent group xen >/dev/null 2>&1; then
    usermod -a -G xen root 2>/dev/null && {
        log_success "root用户已添加到xen组"
    } || {
        log_warning "添加到xen组失败"
    }
else
    log_warning "xen组不存在"
fi

# 4. 重启xenstore相关服务
log_info "重启相关服务..."
systemctl restart xenstored 2>/dev/null || service xenstored restart 2>/dev/null || {
    log_warning "重启xenstored失败"
}
sleep 2

# 5. 测试Xen功能
log_info "测试Xen功能..."
if xl info >/dev/null 2>&1; then
    log_success "✅ xl info 测试通过"
else
    log_error "❌ xl info 测试失败"
    echo "错误信息:"
    xl info 2>&1 | head -3
fi

if xl list >/dev/null 2>&1; then
    log_success "✅ xl list 测试通过"
    echo "虚拟机列表:"
    xl list
else
    log_error "❌ xl list 测试失败"
    echo "错误信息:"
    xl list 2>&1 | head -3
fi

# 6. 测试防病毒程序
PROGRAM_PATH="/usr/local/bin/xenserver-antivirus-glibc217-static"
if [ -f "$PROGRAM_PATH" ]; then
    log_info "测试防病毒程序..."
    if timeout 10s "$PROGRAM_PATH" --help >/dev/null 2>&1; then
        log_success "✅ 防病毒程序帮助信息正常"
    else
        log_warning "⚠️  防病毒程序帮助信息测试失败"
    fi
    
    if timeout 10s "$PROGRAM_PATH" --test >/dev/null 2>&1; then
        log_success "✅ 防病毒程序测试模式正常"
    else
        log_warning "⚠️  防病毒程序测试模式失败"
        echo "尝试运行程序查看详细错误:"
        echo "sudo $PROGRAM_PATH --test"
    fi
else
    log_warning "防病毒程序未安装在 $PROGRAM_PATH"
fi

echo ""
echo "=================================================================="
echo "修复完成！"
echo "=================================================================="

echo "如果问题仍然存在，请尝试以下步骤："
echo ""
echo "1. 重启系统:"
echo "   reboot"
echo ""
echo "2. 手动检查权限:"
echo "   ls -la /dev/xen/"
echo "   ps aux | grep xenstored"
echo ""
echo "3. 查看系统日志:"
echo "   dmesg | grep -i xen"
echo "   journalctl -u xenstored"
echo ""
echo "4. 运行完整诊断:"
echo "   # 下载并运行完整诊断脚本"
echo ""
echo "5. 联系技术支持并提供以下信息:"
echo "   - 系统版本: $(cat /etc/redhat-release 2>/dev/null || uname -a)"
echo "   - Xen版本: $(xl info 2>/dev/null | grep xen_version || echo '无法获取')"
echo "   - 错误日志: 程序运行时的完整错误信息"

echo ""
log_info "现在可以尝试运行防病毒程序:"
echo "sudo $PROGRAM_PATH -v"
