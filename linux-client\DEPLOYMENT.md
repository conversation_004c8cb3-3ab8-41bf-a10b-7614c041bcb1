# XenServer防病毒Linux客户端部署指南

## 部署到Xen Linux虚拟机

### 1. 文件准备
需要复制到虚拟机的文件：
```
xenserver-antivirus/
├── linux-client/
│   ├── bin/av_linux_client_real          # 编译好的可执行文件
│   ├── av_linux_client_real.c            # 源代码（可选）
│   ├── main.c                            # 主程序源代码（可选）
│   ├── Makefile.real                     # 编译配置（可选）
│   └── README.md                         # 文档
└── common/
    ├── av_common.c                       # 公共库源代码（可选）
    └── av_common.h                       # 公共库头文件（可选）
```

### 2. 系统要求
- Linux虚拟机运行在XenServer/Xen环境中
- 具有root权限
- 安装了必要的Xen库（通常已预装）

### 3. 部署步骤

#### 方法1：直接使用编译好的二进制文件
```bash
# 1. 复制可执行文件到虚拟机
scp bin/av_linux_client_real user@vm-ip:/tmp/

# 2. 在虚拟机中安装
ssh user@vm-ip
sudo cp /tmp/av_linux_client_real /usr/local/bin/
sudo chmod +x /usr/local/bin/av_linux_client_real

# 3. 运行客户端
sudo /usr/local/bin/av_linux_client_real --help
```

#### 方法2：在虚拟机中重新编译
```bash
# 1. 复制源代码到虚拟机
scp -r xenserver-antivirus/ user@vm-ip:/tmp/

# 2. 在虚拟机中编译
ssh user@vm-ip
cd /tmp/xenserver-antivirus/linux-client

# 检查依赖
make -f Makefile.real check-deps

# 编译
make -f Makefile.real

# 安装
sudo cp bin/av_linux_client_real /usr/local/bin/
sudo chmod +x /usr/local/bin/av_linux_client_real
```

### 4. 运行测试

#### 基本测试
```bash
# 显示帮助信息
sudo av_linux_client_real --help

# 以详细模式运行（前台）
sudo av_linux_client_real -v

# 以守护进程模式运行
sudo av_linux_client_real -d -f /var/log/av_client.log
```

#### 检查运行状态
```bash
# 查看进程
ps aux | grep av_linux_client

# 查看日志
tail -f /var/log/av_client.log

# 检查XenStore连接
sudo xenstore-ls /local/domain/0/antivirus/
```

### 5. 系统服务配置（可选）

创建systemd服务文件：
```bash
sudo tee /etc/systemd/system/xenserver-antivirus-client.service > /dev/null << 'EOF'
[Unit]
Description=XenServer Antivirus Client
After=network.target

[Service]
Type=forking
ExecStart=/usr/local/bin/av_linux_client_real -d -f /var/log/av_client.log
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=5
User=root

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable xenserver-antivirus-client
sudo systemctl start xenserver-antivirus-client

# 检查服务状态
sudo systemctl status xenserver-antivirus-client
```

### 6. 故障排除

#### 常见问题
1. **权限错误**
   ```bash
   # 确保以root权限运行
   sudo av_linux_client_real -v
   ```

2. **Xen接口访问失败**
   ```bash
   # 检查是否在Xen虚拟机中
   ls -la /proc/xen/
   
   # 检查Xen设备
   ls -la /dev/xen/
   ```

3. **XenStore连接失败**
   ```bash
   # 检查XenStore守护进程
   ps aux | grep xenstore
   
   # 测试XenStore访问
   sudo xenstore-read domid
   ```

4. **库依赖问题**
   ```bash
   # 检查库依赖
   ldd /usr/local/bin/av_linux_client_real
   
   # 安装缺失的库
   sudo apt update
   sudo apt install libxen-dev
   ```

### 7. 性能监控

#### 监控脚本
```bash
#!/bin/bash
# monitor_av_client.sh

while true; do
    echo "=== $(date) ==="
    
    # 检查进程状态
    if pgrep -f av_linux_client_real > /dev/null; then
        echo "✓ Client is running"
        
        # 显示资源使用情况
        ps -o pid,ppid,cmd,%mem,%cpu -p $(pgrep -f av_linux_client_real)
        
        # 显示最新日志
        echo "Recent logs:"
        tail -5 /var/log/av_client.log
    else
        echo "✗ Client is not running"
    fi
    
    echo "===================="
    sleep 30
done
```

### 8. 安全注意事项

1. **文件权限**
   ```bash
   # 设置适当的文件权限
   sudo chown root:root /usr/local/bin/av_linux_client_real
   sudo chmod 755 /usr/local/bin/av_linux_client_real
   ```

2. **日志轮转**
   ```bash
   # 创建logrotate配置
   sudo tee /etc/logrotate.d/xenserver-antivirus-client > /dev/null << 'EOF'
   /var/log/av_client.log {
       daily
       rotate 7
       compress
       delaycompress
       missingok
       notifempty
       postrotate
           systemctl reload xenserver-antivirus-client > /dev/null 2>&1 || true
       endscript
   }
   EOF
   ```

### 9. 与宿主机通信验证

在虚拟机中运行客户端后，可以通过以下方式验证通信：

```bash
# 检查XenStore中的防病毒相关路径
sudo xenstore-ls /local/domain/0/antivirus/

# 监听XenStore变化
sudo xenstore-watch /local/domain/0/antivirus/

# 检查共享内存
cat /proc/meminfo | grep -i shared
```

### 10. 卸载

```bash
# 停止服务
sudo systemctl stop xenserver-antivirus-client
sudo systemctl disable xenserver-antivirus-client

# 删除文件
sudo rm /usr/local/bin/av_linux_client_real
sudo rm /etc/systemd/system/xenserver-antivirus-client.service
sudo rm /var/log/av_client.log

# 重新加载systemd
sudo systemctl daemon-reload
```