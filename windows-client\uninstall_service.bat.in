@echo off
REM XenServer防病毒客户端服务卸载脚本

echo Uninstalling XenServer Antivirus Client Service...

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

REM 检查服务是否存在
sc query "XenServerAntivirusClient" >nul 2>&1
if %errorLevel% neq 0 (
    echo Service is not installed.
    pause
    exit /b 0
)

REM 停止服务
echo Stopping service...
sc stop "XenServerAntivirusClient"
timeout /t 5 /nobreak >nul

REM 删除服务
echo Removing service...
sc delete "XenServerAntivirusClient"

if %errorLevel% neq 0 (
    echo ERROR: Failed to remove service.
    pause
    exit /b 1
)

echo Service uninstalled successfully.
pause