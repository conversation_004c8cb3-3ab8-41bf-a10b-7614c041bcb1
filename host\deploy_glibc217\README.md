# XenServer防病毒宿主机服务 - glibc 2.17兼容版本

## 目标环境
- Citrix Hypervisor 8.2.1
- glibc 2.17
- Xen 4.13.x - 4.17.x

## 文件说明
- `xenserver-antivirus-glibc217-static`: 静态链接版本（推荐）
- `deploy_glibc217.sh`: 自动部署脚本

## 部署方法
1. 将此目录复制到Citrix Hypervisor宿主机
2. 运行: `sudo ./deploy_glibc217.sh`

## 手动安装
```bash
# 测试环境
./xenserver-antivirus-glibc217-static --test

# 安装
sudo install -m 755 xenserver-antivirus-glibc217-static /usr/local/bin/

# 运行
sudo /usr/local/bin/xenserver-antivirus-glibc217-static -v
```

## 兼容性说明
- 编译环境: WSL (glibc 2.31+)
- 目标环境: Citrix Hypervisor 8.2.1 (glibc 2.17)
- 构建时间: Fri Sep  5 13:47:32 CST 2025
- 特殊优化: 静态链接，避免glibc版本冲突
