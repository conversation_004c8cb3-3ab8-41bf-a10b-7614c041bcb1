/*
 * XenServer防病毒Linux客户端 - 纯真实Xen环境版本
 * 运行在Linux虚拟机内，与宿主机防病毒服务通信
 * 包含信号处理修复
 */

/* 首先定义必要的宏 */
#define __XEN_TOOLS__ 1
#define _GNU_SOURCE

/* 标准C库头文件 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <pthread.h>
#include <sys/mman.h>
#include <sys/select.h>
#include <sys/time.h>
#include <getopt.h>
#include <time.h>

/* Xen头文件 */
#include <xenctrl.h>
#include <xenstore.h>
#include <xengnttab.h>

/* 项目头文件 */
#include "../common/av_common.h"

/* 版本信息 */
#define AV_VERSION "1.0.0"

/* 错误码定义 */
#define AV_SUCCESS 0
#define AV_ERROR_INIT -1
#define AV_ERROR_MEMORY -2
#define AV_ERROR_XEN -3
#define AV_ERROR_TIMEOUT -4

/* Linux客户端结构体 */
typedef struct {
    xc_interface* xc_handle;        /* libxenctrl句柄 */
    struct xs_handle* xs_handle;    /* XenStore句柄 */
    xengnttab_handle* gnttab_handle; /* Grant table句柄 */
    char vm_uuid[64];               /* 虚拟机UUID */
    pthread_t monitor_thread;       /* XenStore监听线程 */
    volatile int shutdown_requested; /* 关闭请求标志 */
    void* shared_memory;            /* 共享内存地址 */
    size_t shared_memory_size;      /* 共享内存大小 */
    int communication_cycles;       /* 通信周期计数 */
} av_linux_client_t;

/* 全局客户端实例 */
static av_linux_client_t g_client;

/* 信号处理函数 */
void signal_handler(int signum) {
    static volatile sig_atomic_t signal_count = 0;
    
    switch (signum) {
        case SIGINT:
        case SIGTERM:
            signal_count++;
            av_log(AV_LOG_INFO, "Received shutdown signal %d (%s) - count: %d", 
                   signum, (signum == SIGINT) ? "SIGINT" : "SIGTERM", (int)signal_count);
            g_client.shutdown_requested = 1;
            
            /* 如果多次收到信号，强制退出 */
            if (signal_count >= 3) {
                av_log(AV_LOG_WARN, "Received %d shutdown signals, forcing exit", (int)signal_count);
                _exit(1);
            }
            break;
        default:
            av_log(AV_LOG_WARN, "Received unexpected signal: %d", signum);
            break;
    }
}

/* 获取虚拟机UUID */
int av_get_vm_uuid(av_linux_client_t* client, char* uuid_buffer, size_t buffer_size) {
    if (!client || !uuid_buffer || buffer_size < 37) {
        av_log(AV_LOG_ERROR, "Invalid parameters for UUID retrieval");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 从XenStore读取虚拟机UUID */
    char* uuid_path = "/vm";
    unsigned int len;
    char* uuid_value = xs_read(client->xs_handle, XBT_NULL, uuid_path, &len);
    
    if (!uuid_value) {
        av_log(AV_LOG_ERROR, "Failed to read VM UUID from XenStore path: %s", uuid_path);
        return AV_ERROR_XENSTORE_READ;
    }

    /* 检查UUID格式并复制 */
    if (len >= buffer_size) {
        av_log(AV_LOG_ERROR, "UUID buffer too small");
        free(uuid_value);
        return AV_ERROR_BUFFER_TOO_SMALL;
    }

    strncpy(uuid_buffer, uuid_value, buffer_size - 1);
    uuid_buffer[buffer_size - 1] = '\0';
    
    free(uuid_value);
    av_log(AV_LOG_INFO, "Successfully retrieved VM UUID: %s", uuid_buffer);
    
    return AV_SUCCESS;
}

/* XenStore监听线程函数 */
void* av_xenstore_monitor_thread(void* arg) {
    av_linux_client_t* client = (av_linux_client_t*)arg;
    fd_set readfds;
    int xs_fd;
    struct timeval timeout;
    char** watch_paths;
    unsigned int num_paths;
    
    if (!client || !client->xs_handle) {
        av_log(AV_LOG_ERROR, "Invalid client context in monitor thread");
        return NULL;
    }

    xs_fd = xs_fileno(client->xs_handle);
    if (xs_fd < 0) {
        av_log(AV_LOG_ERROR, "Failed to get XenStore file descriptor");
        return NULL;
    }

    av_log(AV_LOG_INFO, "XenStore monitor thread started");

    while (!client->shutdown_requested) {
        FD_ZERO(&readfds);
        FD_SET(xs_fd, &readfds);
        
        timeout.tv_sec = 1;  /* 1秒超时 */
        timeout.tv_usec = 0;

        int ret = select(xs_fd + 1, &readfds, NULL, NULL, &timeout);
        
        if (ret < 0) {
            if (errno == EINTR) {
                continue;  /* 被信号中断，继续监听 */
            }
            av_log(AV_LOG_ERROR, "select() failed in XenStore monitor: %s", strerror(errno));
            break;
        }
        
        if (ret == 0) {
            continue;  /* 超时，继续监听 */
        }

        if (FD_ISSET(xs_fd, &readfds)) {
            /* 读取XenStore事件 */
            watch_paths = xs_read_watch(client->xs_handle, &num_paths);
            if (watch_paths && num_paths >= 2) {
                char* path = watch_paths[XS_WATCH_PATH];
                char* token = watch_paths[XS_WATCH_TOKEN];
                
                av_log(AV_LOG_DEBUG, "XenStore watch triggered: path=%s, token=%s", path, token);
                
                /* 检查是否是共享内存ID路径 */
                if (strstr(path, "/antivirus/shm_id")) {
                    unsigned int value_len;
                    char* value = xs_read(client->xs_handle, XBT_NULL, path, &value_len);
                    if (value) {
                        av_log(AV_LOG_INFO, "Received shared memory ID from XenStore: %s", value);
                        free(value);
                    }
                }
                
                free(watch_paths);
            }
        }
    }

    av_log(AV_LOG_INFO, "XenStore monitor thread exiting");
    return NULL;
}

/* 初始化Linux客户端 */
int av_linux_client_init(av_linux_client_t* client) {
    if (!client) {
        av_log(AV_LOG_ERROR, "Invalid client parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    memset(client, 0, sizeof(av_linux_client_t));
    
    /* 初始化libxenctrl */
    client->xc_handle = xc_interface_open(NULL, NULL, 0);
    if (!client->xc_handle) {
        av_log(AV_LOG_ERROR, "Failed to open xenctrl interface");
        return AV_ERROR_XC_INTERFACE;
    }

    /* 初始化XenStore连接 */
    client->xs_handle = xs_open(0);
    if (!client->xs_handle) {
        av_log(AV_LOG_ERROR, "Failed to open XenStore connection");
        xc_interface_close(client->xc_handle);
        return AV_ERROR_XENSTORE_OPEN;
    }

    /* 初始化Grant table */
    client->gnttab_handle = xengnttab_open(NULL, 0);
    if (!client->gnttab_handle) {
        av_log(AV_LOG_ERROR, "Failed to open grant table");
        xs_close(client->xs_handle);
        xc_interface_close(client->xc_handle);
        return AV_ERROR_XEN;
    }

    client->shutdown_requested = 0;
    client->shared_memory_size = AV_SHARED_MEMORY_SIZE;

    av_log(AV_LOG_INFO, "Linux client initialized successfully");
    return AV_SUCCESS;
}

/* 启动Linux客户端 */
int av_linux_client_start(av_linux_client_t* client) {
    if (!client) {
        av_log(AV_LOG_ERROR, "Invalid client parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 获取虚拟机UUID */
    int ret = av_get_vm_uuid(client, client->vm_uuid, sizeof(client->vm_uuid));
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to get VM UUID");
        return ret;
    }

    /* 构建XenStore监听路径 */
    char xenstore_path[256];
    snprintf(xenstore_path, sizeof(xenstore_path), "/local/domain/0/antivirus/%s/shm_id", 
             client->vm_uuid);

    /* 设置XenStore监听 */
    if (!xs_watch(client->xs_handle, xenstore_path, "antivirus_watch")) {
        av_log(AV_LOG_ERROR, "Failed to set XenStore watch on path: %s", xenstore_path);
        return AV_ERROR_XENSTORE_WATCH;
    }

    av_log(AV_LOG_INFO, "Successfully set XenStore watch on path: %s", xenstore_path);

    /* 启动XenStore监听线程 */
    if (pthread_create(&client->monitor_thread, NULL, av_xenstore_monitor_thread, client) != 0) {
        av_log(AV_LOG_ERROR, "Failed to create XenStore monitor thread");
        return AV_ERROR_THREAD_CREATE;
    }

    av_log(AV_LOG_INFO, "Linux client started successfully");
    return AV_SUCCESS;
}

/* 停止Linux客户端 */
int av_linux_client_stop(av_linux_client_t* client) {
    if (!client) {
        av_log(AV_LOG_ERROR, "Invalid client parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 设置关闭标志 */
    client->shutdown_requested = 1;

    /* 等待监听线程结束 */
    if (client->monitor_thread != 0) {
        pthread_join(client->monitor_thread, NULL);
        client->monitor_thread = 0;
    }

    av_log(AV_LOG_INFO, "Linux client stopped successfully");
    return AV_SUCCESS;
}

/* 清理Linux客户端 */
void av_linux_client_cleanup(av_linux_client_t* client) {
    if (!client) {
        return;
    }

    /* 停止客户端 */
    av_linux_client_stop(client);

    /* 清理Grant table */
    if (client->gnttab_handle) {
        xengnttab_close(client->gnttab_handle);
        client->gnttab_handle = NULL;
    }

    /* 清理XenStore连接 */
    if (client->xs_handle) {
        xs_close(client->xs_handle);
        client->xs_handle = NULL;
    }

    /* 清理libxenctrl接口 */
    if (client->xc_handle) {
        xc_interface_close(client->xc_handle);
        client->xc_handle = NULL;
    }

    av_log(AV_LOG_INFO, "Linux client cleanup completed");
}

/* 客户端通信流程函数 */
int av_linux_client_run_communication(av_linux_client_t* client) {
    if (!client) {
        av_log(AV_LOG_ERROR, "Invalid client parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_INFO, "Starting communication flow for VM: %s", client->vm_uuid);

    /* 简单的通信循环 */
    while (!client->shutdown_requested) {
        av_log(AV_LOG_DEBUG, "Communication cycle %d", ++client->communication_cycles);
        
        /* 使用可中断的sleep */
        struct timespec sleep_time = {5, 0};
        struct timespec remaining;
        
        if (nanosleep(&sleep_time, &remaining) == -1) {
            if (errno == EINTR) {
                av_log(AV_LOG_DEBUG, "Communication sleep interrupted by signal");
                /* 检查是否需要退出 */
                if (client->shutdown_requested) {
                    break;
                }
            }
        }
    }

    av_log(AV_LOG_INFO, "Communication flow completed");
    return AV_SUCCESS;
}

/* 打印使用帮助 */
void print_usage(const char* program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("XenServer Antivirus Linux Client (Pure Real Version)\n\n");
    printf("Options:\n");
    printf("  -h, --help           Show this help message\n");
    printf("  -v, --verbose        Enable verbose logging\n");
    printf("  -d, --daemon         Run as daemon process\n");
    printf("  -l, --log-level LEVEL Set log level (0=ERROR, 1=WARNING, 2=INFO, 3=DEBUG)\n");
    printf("  -f, --log-file FILE  Write logs to file instead of stdout\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s                   Run in foreground with default settings\n", program_name);
    printf("  %s -v -l 3           Run with verbose debug logging\n", program_name);
    printf("  %s -d -f /var/log/av_client.log  Run as daemon with file logging\n", program_name);
    printf("\n");
    printf("Requirements:\n");
    printf("  - Must run as root\n");
    printf("  - Must run in Xen virtual machine\n");
    printf("  - Requires Xen libraries (libxenctrl, libxenstore, libxengnttab)\n");
    printf("\n");
    printf("Signal Handling:\n");
    printf("  SIGINT/SIGTERM       Graceful shutdown\n");
    printf("  3x SIGINT/SIGTERM    Force exit\n");
}

/* 守护进程化 */
int daemonize() {
    pid_t pid = fork();
    
    if (pid < 0) {
        av_log(AV_LOG_ERROR, "Failed to fork daemon process");
        return AV_ERROR_FORK;
    }
    
    if (pid > 0) {
        /* 父进程退出 */
        exit(0);
    }
    
    /* 子进程继续 */
    if (setsid() < 0) {
        av_log(AV_LOG_ERROR, "Failed to create new session");
        return AV_ERROR_SETSID;
    }
    
    /* 改变工作目录到根目录 */
    if (chdir("/") < 0) {
        av_log(AV_LOG_ERROR, "Failed to change working directory");
        return AV_ERROR_CHDIR;
    }
    
    /* 关闭标准文件描述符 */
    close(STDIN_FILENO);
    close(STDOUT_FILENO);
    close(STDERR_FILENO);
    
    av_log(AV_LOG_INFO, "Successfully daemonized");
    return AV_SUCCESS;
}

/* 主函数 */
int main(int argc, char* argv[]) {
    int ret = AV_SUCCESS;
    int daemon_mode = 0;
    int log_level = AV_LOG_INFO;
    char* log_file = NULL;
    
    /* 命令行参数解析 */
    static struct option long_options[] = {
        {"help",      no_argument,       0, 'h'},
        {"verbose",   no_argument,       0, 'v'},
        {"daemon",    no_argument,       0, 'd'},
        {"log-level", required_argument, 0, 'l'},
        {"log-file",  required_argument, 0, 'f'},
        {0, 0, 0, 0}
    };
    
    int option_index = 0;
    int c;
    
    while ((c = getopt_long(argc, argv, "hvdl:f:", long_options, &option_index)) != -1) {
        switch (c) {
            case 'h':
                print_usage(argv[0]);
                return 0;
            case 'v':
                log_level = AV_LOG_DEBUG;
                break;
            case 'd':
                daemon_mode = 1;
                break;
            case 'l':
                log_level = atoi(optarg);
                if (log_level < AV_LOG_ERROR || log_level > AV_LOG_DEBUG) {
                    fprintf(stderr, "Invalid log level: %s\n", optarg);
                    return 1;
                }
                break;
            case 'f':
                log_file = optarg;
                break;
            case '?':
                print_usage(argv[0]);
                return 1;
            default:
                break;
        }
    }
    
    /* 初始化日志系统 */
    av_log_init(log_level, log_file);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Linux Client starting...");
    av_log(AV_LOG_INFO, "Version: %s, Build: %s %s", AV_VERSION, __DATE__, __TIME__);
    
    /* 检查运行权限 */
    if (geteuid() != 0) {
        av_log(AV_LOG_ERROR, "This program must be run as root");
        return 1;
    }
    
    /* 守护进程化 */
    if (daemon_mode) {
        ret = daemonize();
        if (ret != AV_SUCCESS) {
            av_log(AV_LOG_ERROR, "Failed to daemonize process");
            return 1;
        }
    }
    
    /* 设置信号处理 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGPIPE, SIG_IGN);  /* 忽略SIGPIPE信号 */
    
    /* 初始化客户端 */
    ret = av_linux_client_init(&g_client);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to initialize Linux client");
        return 1;
    }
    
    /* 启动客户端 */
    ret = av_linux_client_start(&g_client);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to start Linux client");
        av_linux_client_cleanup(&g_client);
        return 1;
    }
    
    av_log(AV_LOG_INFO, "Linux client started successfully, entering main loop");
    
    /* 运行通信流程 */
    ret = av_linux_client_run_communication(&g_client);
    if (ret != AV_SUCCESS && !g_client.shutdown_requested) {
        av_log(AV_LOG_ERROR, "Communication flow failed");
    }
    
    /* 清理资源 */
    av_log(AV_LOG_INFO, "Shutting down Linux client...");
    av_linux_client_cleanup(&g_client);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Linux Client shutdown complete");
    av_log_cleanup();
    
    return (ret == AV_SUCCESS) ? 0 : 1;
}