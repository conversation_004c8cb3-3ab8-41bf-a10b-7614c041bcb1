#ifdef SIMPLE_BUILD
#include "av_host_simple.h"
#else
#include "av_host.h"
#endif
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <time.h>

/* 错误处理函数 */
void av_handle_error(av_error_code_t error, const char* context) {
    const char* error_msg = av_error_string(error);
    
    if (context) {
        av_log(AV_LOG_ERROR, "Error in %s: %s (code: %d)", context, error_msg, error);
    } else {
        av_log(AV_LOG_ERROR, "Error: %s (code: %d)", error_msg, error);
    }

    /* 根据错误类型执行特定的处理 */
    switch (error) {
        case AV_ERROR_MEMORY_ALLOC:
            av_log(AV_LOG_ERROR, "Memory allocation failed - system may be low on memory");
            break;
            
        case AV_ERROR_PERMISSION:
            av_log(AV_LOG_ERROR, "Permission denied - ensure running with root privileges");
            break;
            
        case AV_ERROR_XEN_INTERFACE:
            av_log(AV_LOG_ERROR, "Xen interface error - check if Xen hypervisor is running");
            break;
            
        case AV_ERROR_VM_NOT_FOUND:
            av_log(AV_LOG_ERROR, "Virtual machine not found - check if VM is running");
            break;
            
        case AV_ERROR_XENSTORE:
            av_log(AV_LOG_ERROR, "XenStore operation failed - check XenStore daemon status");
            break;
            
        case AV_ERROR_TIMEOUT:
            av_log(AV_LOG_ERROR, "Operation timed out - system may be overloaded");
            break;
            
        case AV_ERROR_PROTOCOL:
            av_log(AV_LOG_ERROR, "Protocol error - check communication format");
            break;
            
        default:
            /* 其他错误不需要特殊处理 */
            break;
    }
}

/* 重试操作函数 */
int av_retry_operation(int (*operation)(void*), void* params, int max_retries, int delay_ms) {
    if (!operation || max_retries < 0 || delay_ms < 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_retry_operation");
        return AV_ERROR_INVALID_PARAM;
    }

    int attempt = 0;
    int result = AV_ERROR_PROTOCOL;

    av_log(AV_LOG_DEBUG, "Starting retry operation (max_retries: %d, delay: %dms)", 
           max_retries, delay_ms);

    for (attempt = 0; attempt <= max_retries; attempt++) {
        if (attempt > 0) {
            av_log(AV_LOG_DEBUG, "Retry attempt %d/%d", attempt, max_retries);
            
            /* 延迟执行 */
            if (delay_ms > 0) {
                struct timespec ts;
                ts.tv_sec = delay_ms / 1000;
                ts.tv_nsec = (delay_ms % 1000) * 1000000;
                nanosleep(&ts, NULL);
            }
        }

        /* 执行操作 */
        result = operation(params);
        
        if (result == AV_SUCCESS) {
            if (attempt > 0) {
                av_log(AV_LOG_INFO, "Operation succeeded after %d retries", attempt);
            }
            return AV_SUCCESS;
        }

        /* 记录失败 */
        av_log(AV_LOG_DEBUG, "Operation failed on attempt %d: %s", 
               attempt + 1, av_error_string(result));

        /* 检查是否应该重试 */
        if (!av_should_retry_error(result)) {
            av_log(AV_LOG_DEBUG, "Error type not suitable for retry, aborting");
            break;
        }
    }

    av_log(AV_LOG_ERROR, "Operation failed after %d attempts: %s", 
           attempt, av_error_string(result));
    return result;
}

/* 判断错误是否适合重试 */
int av_should_retry_error(av_error_code_t error) {
    switch (error) {
        case AV_ERROR_XENSTORE:
        case AV_ERROR_XEN_INTERFACE:
        case AV_ERROR_TIMEOUT:
            /* 这些错误可能是临时的，适合重试 */
            return 1;
            
        case AV_ERROR_MEMORY_ALLOC:
            /* 内存分配失败可能是临时的 */
            return 1;
            
        case AV_ERROR_INVALID_PARAM:
        case AV_ERROR_PERMISSION:
        case AV_ERROR_NOT_INITIALIZED:
        case AV_ERROR_VM_NOT_FOUND:
        case AV_ERROR_PROTOCOL:
        case AV_ERROR_ALREADY_EXISTS:
            /* 这些错误通常不是临时的，不适合重试 */
            return 0;
            
        default:
            /* 未知错误，保守起见不重试 */
            return 0;
    }
}

/* 检查系统资源状态 */
int av_check_system_resources(void) {
    av_log(AV_LOG_DEBUG, "Checking system resources");

    /* 检查内存使用情况 */
    FILE* meminfo = fopen("/proc/meminfo", "r");
    if (meminfo) {
        char line[256];
        long mem_total = 0, mem_free = 0, mem_available = 0;
        
        while (fgets(line, sizeof(line), meminfo)) {
            if (sscanf(line, "MemTotal: %ld kB", &mem_total) == 1) {
                continue;
            }
            if (sscanf(line, "MemFree: %ld kB", &mem_free) == 1) {
                continue;
            }
            if (sscanf(line, "MemAvailable: %ld kB", &mem_available) == 1) {
                continue;
            }
        }
        fclose(meminfo);
        
        if (mem_total > 0) {
            int mem_usage_percent = (int)((mem_total - mem_available) * 100 / mem_total);
            av_log(AV_LOG_DEBUG, "Memory usage: %d%% (Total: %ld KB, Available: %ld KB)", 
                   mem_usage_percent, mem_total, mem_available);
            
            if (mem_usage_percent > 90) {
                av_log(AV_LOG_WARN, "High memory usage detected: %d%%", mem_usage_percent);
                return AV_ERROR_MEMORY_ALLOC;
            }
        }
    }

    /* 检查磁盘空间 */
    FILE* mounts = fopen("/proc/mounts", "r");
    if (mounts) {
        /* 简单检查，实际实现可以更详细 */
        fclose(mounts);
    }

    av_log(AV_LOG_DEBUG, "System resources check completed");
    return AV_SUCCESS;
}

/* 记录操作统计信息 */
typedef struct {
    int total_operations;
    int successful_operations;
    int failed_operations;
    int retry_operations;
    time_t start_time;
} av_operation_stats_t;

static av_operation_stats_t g_stats = {0};

void av_stats_init(void) {
    memset(&g_stats, 0, sizeof(g_stats));
    g_stats.start_time = time(NULL);
    av_log(AV_LOG_INFO, "Operation statistics initialized");
}

void av_stats_record_operation(int success, int retries) {
    g_stats.total_operations++;
    
    if (success) {
        g_stats.successful_operations++;
    } else {
        g_stats.failed_operations++;
    }
    
    if (retries > 0) {
        g_stats.retry_operations++;
    }
}

void av_stats_report(void) {
    time_t current_time = time(NULL);
    double uptime = difftime(current_time, g_stats.start_time);
    
    av_log(AV_LOG_INFO, "=== Operation Statistics ===");
    av_log(AV_LOG_INFO, "Uptime: %.0f seconds", uptime);
    av_log(AV_LOG_INFO, "Total operations: %d", g_stats.total_operations);
    av_log(AV_LOG_INFO, "Successful operations: %d", g_stats.successful_operations);
    av_log(AV_LOG_INFO, "Failed operations: %d", g_stats.failed_operations);
    av_log(AV_LOG_INFO, "Operations requiring retry: %d", g_stats.retry_operations);
    
    if (g_stats.total_operations > 0) {
        double success_rate = (double)g_stats.successful_operations * 100.0 / g_stats.total_operations;
        av_log(AV_LOG_INFO, "Success rate: %.2f%%", success_rate);
    }
    
    if (uptime > 0) {
        double ops_per_second = g_stats.total_operations / uptime;
        av_log(AV_LOG_INFO, "Operations per second: %.2f", ops_per_second);
    }
}

/* 验证系统环境 */
int av_validate_environment(void) {
    av_log(AV_LOG_INFO, "Validating system environment");

    /* 检查是否以root权限运行 */
    if (getuid() != 0) {
        av_log(AV_LOG_ERROR, "Program must be run as root (current UID: %d)", getuid());
        av_log(AV_LOG_ERROR, "Try: sudo %s", program_invocation_name ? program_invocation_name : "xenserver-antivirus-host");
        return AV_ERROR_PERMISSION;
    }

    /* 检查有效用户ID */
    if (geteuid() != 0) {
        av_log(AV_LOG_ERROR, "Effective UID is not root (current EUID: %d)", geteuid());
        return AV_ERROR_PERMISSION;
    }

    /* 检查Xen相关文件是否存在 */
    if (access("/proc/xen", F_OK) != 0) {
        av_log(AV_LOG_ERROR, "/proc/xen not found - Xen hypervisor may not be running");
        av_log(AV_LOG_ERROR, "Ensure this is running on a Xen Dom0 system");
        return AV_ERROR_XEN_INTERFACE;
    }

    if (access("/dev/xen/xenstore", F_OK) != 0) {
        av_log(AV_LOG_ERROR, "/dev/xen/xenstore not found - XenStore daemon may not be running");
        av_log(AV_LOG_ERROR, "Try: systemctl status xenstored");
        return AV_ERROR_XENSTORE;
    }

    /* 检查libxc设备文件权限 */
    if (access("/dev/xen/privcmd", R_OK | W_OK) != 0) {
        av_log(AV_LOG_ERROR, "/dev/xen/privcmd not accessible - check device permissions");
        av_log(AV_LOG_ERROR, "Current user may need to be in 'xen' group");
        return AV_ERROR_PERMISSION;
    }

    /* 检查是否在Dom0中运行 */
    FILE* domid_file = fopen("/proc/xen/domid", "r");
    if (domid_file) {
        int domid = -1;
        if (fscanf(domid_file, "%d", &domid) == 1) {
            if (domid != 0) {
                av_log(AV_LOG_ERROR, "Not running in Dom0 (current domain: %d)", domid);
                fclose(domid_file);
                return AV_ERROR_XEN_INTERFACE;
            }
        }
        fclose(domid_file);
    }

    /* 检查系统资源 */
    int ret = av_check_system_resources();
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_WARN, "System resource check failed, but continuing");
        /* 不返回错误，只是警告 */
    }

    av_log(AV_LOG_INFO, "System environment validation completed successfully");
    return AV_SUCCESS;
}