# XenServer Agentless Antivirus Host Service Configuration
# 
# This is the main configuration file for the XenServer antivirus host service.
# Lines starting with # are comments and are ignored.

# Log level: error, warn, info, debug
log_level=info

# PID file location
pid_file=/var/run/xenserver-antivirus-host.pid

# Log file location
log_file=/var/log/xenserver-antivirus-host.log

# Target VM UUID (optional - if specified, only monitor this VM)
# target_uuid=12345678-1234-1234-1234-123456789abc

# Monitor interval in milliseconds (how often to check for requests)
monitor_interval_ms=10

# Health check interval in seconds
health_check_interval_s=30

# Statistics report interval in seconds
stats_report_interval_s=300

# Performance tuning options
# max_concurrent_vms=100
# response_timeout_ms=100
# memory_pool_size=1048576

# Security options
# require_vm_validation=true
# allowed_vm_uuids_file=/etc/xenserver-antivirus/allowed-vms.txt

# Advanced options
# enable_performance_monitoring=true
# enable_detailed_logging=false
# xenstore_retry_count=3
# xenstore_retry_delay_ms=100