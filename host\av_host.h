#ifndef AV_HOST_H
#define AV_HOST_H

#include "../common/av_common.h"
#include <xenctrl.h>
#include <xenstore.h>
#include <xengnttab.h>
#include <sys/mman.h>
#include <pthread.h>

/* Xen相关类型定义 */
/* domid_t已在xen/xen.h中定义为uint16_t，不需要重新定义 */
typedef uint32_t grant_ref_t;

/* 共享内存统计信息 */
typedef struct {
    uint32_t active_regions;    /* 活跃的共享内存区域数量 */
    size_t total_size;          /* 总共享内存大小 */
    uint32_t read_operations;   /* 读操作次数 */
    uint32_t write_operations;  /* 写操作次数 */
} av_shared_memory_stats_t;

/* XenStore批量操作结构 */
typedef struct {
    char* path;
    char* value;
    int operation; /* 0=read, 1=write, 2=delete */
} av_xenstore_batch_op_t;

/* 宿主机服务上下文 */
typedef struct {
    xc_interface* xc_handle;        /* libxc句柄 */
    struct xs_handle* xs_handle;    /* XenStore句柄 */
    av_context_t* contexts;         /* 虚拟机上下文数组 */
    size_t context_count;           /* 上下文数量 */
    pthread_t monitor_thread;       /* 监听线程 */
    int shutdown_requested;         /* 关闭请求标志 */
    pthread_mutex_t context_mutex;  /* 上下文访问互斥锁 */
} av_host_service_t;

/* 虚拟机发现和验证接口 */
int av_find_vm_by_uuid(av_host_service_t* service, const char* uuid, domid_t* domid);
int av_verify_domain_state(av_host_service_t* service, domid_t domid);

/* 共享内存管理接口 */
int av_create_shared_memory(av_host_service_t* service, size_t size, grant_ref_t* grant_ref);
void* av_map_shared_memory(av_host_service_t* service, grant_ref_t grant_ref, size_t size);
int av_cleanup_shared_memory(av_host_service_t* service, void* addr, grant_ref_t grant_ref, size_t size);

/* 共享内存操作接口 */
int av_validate_shared_memory(void* addr, size_t size);
int av_read_shared_memory_command(void* addr, char* command, size_t command_size);
int av_write_shared_memory_command(void* addr, const char* command);
int av_check_shared_memory_command(void* addr, const char* expected_command);
int av_clear_shared_memory_command(void* addr);
int av_get_shared_memory_stats(av_host_service_t* service, av_shared_memory_stats_t* stats);
int av_test_shared_memory(av_host_service_t* service);

/* XenStore操作接口 */
int av_xenstore_write_shm_id(av_host_service_t* service, const char* vm_uuid, grant_ref_t shm_id);
int av_xenstore_read(av_host_service_t* service, const char* path, char* buffer, size_t buffer_size);
int av_xenstore_write(av_host_service_t* service, const char* path, const char* value);

/* 通信协议接口 */
int av_monitor_scan_requests(av_host_service_t* service, void* shm_addr);
int av_send_scan_response(av_host_service_t* service, void* shm_addr, const char* response);
void* av_monitor_thread_func(void* arg);

/* 监听线程管理接口 */
int av_start_monitor_thread(av_host_service_t* service);
int av_stop_monitor_thread(av_host_service_t* service);
int av_enable_vm_monitoring(av_host_service_t* service, const char* vm_uuid);
int av_disable_vm_monitoring(av_host_service_t* service, const char* vm_uuid);

/* 响应统计接口 */
void av_response_stats_init(void);
void av_response_stats_report(void);

/* 高级响应处理接口 */
typedef enum {
    AV_RESPONSE_TYPE_ACK = 0,       /* 简单确认响应 */
    AV_RESPONSE_TYPE_DATA = 1,      /* 包含数据的响应 */
    AV_RESPONSE_TYPE_ERROR = 2,     /* 错误响应 */
    AV_RESPONSE_TYPE_BUSY = 3       /* 系统忙响应 */
} av_response_type_t;

int av_send_typed_scan_response(av_host_service_t* service, void* shm_addr, 
                               const char* vm_uuid, av_response_type_t type);
int av_process_batch_responses(av_host_service_t* service);
int av_response_health_check(av_host_service_t* service);
int av_optimize_response_performance(av_host_service_t* service);

/* 服务管理接口 */
int av_host_service_init(av_host_service_t* service);
int av_host_service_start(av_host_service_t* service);
int av_host_service_stop(av_host_service_t* service);
void av_host_service_cleanup(av_host_service_t* service);

/* 上下文管理接口 */
int av_add_vm_context(av_host_service_t* service, const char* vm_uuid);
int av_remove_vm_context(av_host_service_t* service, const char* vm_uuid);
av_context_t* av_find_vm_context(av_host_service_t* service, const char* vm_uuid);

/* 错误处理和重试机制 */
int av_retry_operation(int (*operation)(void*), void* params, int max_retries, int delay_ms);
void av_handle_error(av_error_code_t error, const char* context);
int av_should_retry_error(av_error_code_t error);
int av_check_system_resources(void);
int av_validate_environment(void);

/* 操作统计 */
void av_stats_init(void);
void av_stats_record_operation(int success, int retries);
void av_stats_report(void);

/* 扩展的虚拟机发现接口 */
int av_get_domain_info(av_host_service_t* service, domid_t domid, xc_domaininfo_t* info);
int av_check_pv_drivers(av_host_service_t* service, domid_t domid);
int av_list_running_vms(av_host_service_t* service, domid_t** domids, int* count);

/* 扩展的XenStore接口 */
int av_xenstore_exists(av_host_service_t* service, const char* path);
int av_xenstore_mkdir(av_host_service_t* service, const char* path);
int av_xenstore_remove(av_host_service_t* service, const char* path);
int av_xenstore_set_permissions(av_host_service_t* service, const char* path, domid_t domid);
int av_xenstore_watch(av_host_service_t* service, const char* path, const char* token);
int av_xenstore_unwatch(av_host_service_t* service, const char* path, const char* token);
int av_xenstore_init_vm_structure(av_host_service_t* service, const char* vm_uuid, domid_t domid);

/* 高级XenStore接口 */
int av_xenstore_reconnect(av_host_service_t* service);
int av_xenstore_read_retry(av_host_service_t* service, const char* path, char* buffer, size_t buffer_size, int max_retries);
int av_xenstore_write_retry(av_host_service_t* service, const char* path, const char* value, int max_retries);
int av_xenstore_list_directory(av_host_service_t* service, const char* path, char*** entries, unsigned int* count);
void av_xenstore_free_directory_list(char** entries, unsigned int count);
int av_xenstore_walk_tree(av_host_service_t* service, const char* root_path, 
                         void (*callback)(const char* path, const char* value, void* user_data), 
                         void* user_data, int max_depth);
int av_xenstore_start_watch(av_host_service_t* service, const char* path, const char* token,
                           void (*callback)(const char* path, void* user_data), void* user_data,
                           pthread_t* watch_thread);
int av_xenstore_stop_watch(av_host_service_t* service, const char* path, const char* token, pthread_t watch_thread);
int av_xenstore_health_check(av_host_service_t* service);
int av_xenstore_batch_operations(av_host_service_t* service, av_xenstore_batch_op_t* operations, int count);

/* XenStore统计接口 */
void av_xenstore_stats_init(void);
void av_xenstore_stats_record_read(size_t bytes, int success);
void av_xenstore_stats_record_write(size_t bytes, int success);
void av_xenstore_stats_report(void);

/* 资源管理接口 */
typedef struct {
    size_t total_memory;
    size_t peak_memory;
    int resource_count;
    int resource_counts[6];  /* 各类型资源计数 */
} av_resource_stats_t;

int av_resource_manager_init(void);
void av_resource_manager_cleanup(void);
void* av_safe_malloc(size_t size, const char* description);
void av_safe_free(void* ptr);
int av_resource_get_stats(av_resource_stats_t* stats);
void av_resource_report(void);
int av_set_resource_limits(void);
void av_emergency_cleanup(void);

/* 信号处理接口 */
int av_install_signal_handlers(void);
void av_restore_signal_handlers(void);
void av_enhanced_signal_handler(int sig);

#endif /* AV_HOST_H */