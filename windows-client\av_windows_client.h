#ifndef AV_WINDOWS_CLIENT_H
#define AV_WINDOWS_CLIENT_H

#include "../common/av_common.h"
#include <windows.h>
#include <winsvc.h>
#include <string>
#include <thread>
#include <mutex>
#include <condition_variable>

/* Windows服务相关定义 */
#define AV_SERVICE_NAME L"XenServerAntivirusClient"
#define AV_SERVICE_DISPLAY_NAME L"XenServer Antivirus Client Service"
#define AV_SERVICE_DESCRIPTION L"XenServer agentless antivirus client service"

/* XenStore回调函数类型 */
typedef void (*XenStoreCallback)(const std::wstring& path, const std::wstring& value, void* user_data);

/* Windows客户端上下文 */
class AvWindowsClient {
private:
    HANDLE xen_handle_;                     /* Xen接口句柄 */
    av_context_t context_;                  /* 客户端上下文 */
    std::thread monitor_thread_;            /* XenStore监听线程 */
    bool shutdown_requested_;               /* 关闭请求标志 */
    std::mutex context_mutex_;              /* 上下文访问互斥锁 */
    std::condition_variable shm_ready_cv_;  /* 共享内存就绪条件变量 */
    bool shm_ready_;                        /* 共享内存就绪标志 */
    SERVICE_STATUS service_status_;         /* 服务状态 */
    SERVICE_STATUS_HANDLE service_handle_;  /* 服务句柄 */

public:
    AvWindowsClient();
    ~AvWindowsClient();

    /* UUID获取接口 */
    HRESULT GetVmUuid(std::wstring& uuid);

    /* 内存映射接口 */
    HRESULT MapMemory(DWORD shm_id, SIZE_T size, LPVOID* mapped_addr);
    HRESULT UnmapMemory(LPVOID addr, SIZE_T size);

    /* 通信接口 */
    HRESULT SendScanRequest(LPVOID shm_addr);
    HRESULT WaitForScanResponse(LPVOID shm_addr, std::string& response, DWORD timeout_ms);

    /* 客户端管理接口 */
    HRESULT Initialize();
    HRESULT Start();
    HRESULT Stop();
    void Cleanup();

    /* 通信流程接口 */
    HRESULT RunCommunication();

    /* Windows服务接口 */
    void ServiceMain(DWORD argc, LPTSTR* argv);
    void OnStart();
    void OnStop();
    void SetServiceStatus(DWORD state, DWORD exit_code = NO_ERROR);

private:
    /* XenStore监听相关 */
    void XenStoreMonitorThread();
    void OnXenStoreChange(const std::wstring& path, const std::wstring& value);

    /* 日志记录 */
    void LogEvent(WORD type, const std::wstring& message);
};

/* XenStore监听器类 */
class XenStoreMonitor {
private:
    HANDLE xen_handle_;
    std::thread monitor_thread_;
    bool monitoring_;
    std::mutex monitor_mutex_;
    XenStoreCallback callback_;
    void* user_data_;

public:
    XenStoreMonitor();
    ~XenStoreMonitor();

    HRESULT StartMonitoring(const std::wstring& path);
    HRESULT StopMonitoring();
    void RegisterCallback(XenStoreCallback callback, void* user_data);

private:
    void MonitorThread(const std::wstring& path);
};

/* 共享内存映射器类 */
class SharedMemoryMapper {
private:
    HANDLE file_mapping_;
    LPVOID mapped_view_;
    SIZE_T mapped_size_;

public:
    SharedMemoryMapper();
    ~SharedMemoryMapper();

    HRESULT MapMemory(DWORD shm_id, SIZE_T size, LPVOID* mapped_addr);
    HRESULT UnmapMemory(LPVOID addr, SIZE_T size);
    void Cleanup();
};

/* Windows服务基类 */
class ServiceBase {
protected:
    SERVICE_STATUS service_status_;
    SERVICE_STATUS_HANDLE service_handle_;
    std::wstring service_name_;

public:
    ServiceBase(const std::wstring& name);
    virtual ~ServiceBase();

    static void WINAPI ServiceMain(DWORD argc, LPTSTR* argv);
    static void WINAPI ServiceCtrlHandler(DWORD ctrl);

    virtual void OnStart() = 0;
    virtual void OnStop() = 0;
    virtual void OnPause() {}
    virtual void OnContinue() {}
    virtual void OnShutdown() {}

    void SetServiceStatus(DWORD state, DWORD exit_code = NO_ERROR);
    void WriteEventLogEntry(const std::wstring& message, WORD type);

    static ServiceBase* service_instance_;
};

/* 防病毒服务类 */
class AntivirusService : public ServiceBase {
private:
    AvWindowsClient client_;

public:
    AntivirusService();
    virtual ~AntivirusService();

    virtual void OnStart() override;
    virtual void OnStop() override;
};

/* 全局函数声明 */
extern "C" {
    /* 服务安装和卸载 */
    BOOL InstallService();
    BOOL UninstallService();
    
    /* 服务运行 */
    int RunService();
}

#endif /* AV_WINDOWS_CLIENT_H */