# Makefile for XenServer Antivirus Host Service - Cross Compilation
# Compile in WSL, Deploy to Real Xen Environment
# Target: Xen 4.13.x - 4.17.x compatibility

CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE
INCLUDES = -I../common -I.

# WSL环境的Xen库路径
XEN_LIB_PATH = /usr/lib/x86_64-linux-gnu
XEN_INCLUDE_PATH = /usr/include

# 动态链接库
LIBS = -lxenctrl -lxenstore -lxengnttab -lpthread

# 静态链接库（完整依赖）
STATIC_LIBS = -lxenctrl -lxenstore -lxengnttab -lxencall -lxentoollog \
              -lxenforeignmemory -lxendevicemodel -lxentoolcore -lpthread

# 编译标志
STATIC_CFLAGS = -static
CROSS_CFLAGS = -DCROSS_COMPILE -DTARGET_XEN_413

# 目录
OBJDIR = obj_cross
BINDIR = bin
DEPLOYDIR = deploy
COMMONDIR = ../common

# 源文件
MAIN_SOURCE = main_cross_compile.c
COMMON_SOURCE = $(COMMONDIR)/av_common.c
SOURCES = $(MAIN_SOURCE) $(COMMON_SOURCE)

# 目标文件
MAIN_OBJ = $(OBJDIR)/main_cross_compile.o
COMMON_OBJ = $(OBJDIR)/av_common.o
OBJECTS = $(MAIN_OBJ) $(COMMON_OBJ)

# 可执行文件
TARGET_DYNAMIC = $(BINDIR)/xenserver-antivirus-cross
TARGET_STATIC = $(BINDIR)/xenserver-antivirus-cross-static

# 默认目标
all: check-wsl-env $(TARGET_DYNAMIC) $(TARGET_STATIC) package

# 检查WSL编译环境
check-wsl-env:
	@echo "=== Checking WSL Cross-Compilation Environment ==="
	@echo "Compiler: $(CC)"
	@$(CC) --version | head -1
	@echo ""
	@echo "Xen libraries in WSL:"
	@dpkg -l | grep -i libxen | head -5 || echo "No Xen packages found"
	@echo ""
	@echo "Xen headers:"
	@ls -la $(XEN_INCLUDE_PATH)/xen*.h 2>/dev/null | head -3 || echo "Xen headers not found"
	@echo ""
	@echo "Xen libraries:"
	@ls -la $(XEN_LIB_PATH)/libxen*.so 2>/dev/null | head -3 || echo "Xen libraries not found"
	@echo "Environment check complete"
	@echo ""

# 创建目录
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(BINDIR):
	mkdir -p $(BINDIR)

$(DEPLOYDIR):
	mkdir -p $(DEPLOYDIR)

# 编译主程序
$(MAIN_OBJ): $(MAIN_SOURCE) | $(OBJDIR)
	@echo "Compiling $(MAIN_SOURCE) for cross-deployment..."
	$(CC) $(CFLAGS) $(CROSS_CFLAGS) $(INCLUDES) -c $< -o $@

# 编译通用模块
$(COMMON_OBJ): $(COMMON_SOURCE) | $(OBJDIR)
	@echo "Compiling $(COMMON_SOURCE)..."
	$(CC) $(CFLAGS) $(CROSS_CFLAGS) $(INCLUDES) -c $< -o $@

# 链接动态版本
$(TARGET_DYNAMIC): $(OBJECTS) | $(BINDIR)
	@echo "Linking $(TARGET_DYNAMIC) (dynamic, cross-compatible)..."
	$(CC) $(OBJECTS) -o $@ $(LIBS)
	@echo "Build complete: $@"
	@echo "Target: Xen 4.13.x - 4.17.x (dynamic linking)"
	@file $@
	@echo ""

# 链接静态版本
$(TARGET_STATIC): $(OBJECTS) | $(BINDIR)
	@echo "Linking $(TARGET_STATIC) (static, cross-compatible)..."
	$(CC) $(STATIC_CFLAGS) $(OBJECTS) -o $@ $(STATIC_LIBS)
	@echo "Build complete: $@"
	@echo "Target: Xen 4.13.x - 4.17.x (static linking)"
	@file $@
	@echo ""

# 打包部署文件
package: $(TARGET_DYNAMIC) $(TARGET_STATIC) | $(DEPLOYDIR)
	@echo "=== Creating Deployment Package ==="
	@cp $(TARGET_DYNAMIC) $(DEPLOYDIR)/
	@cp $(TARGET_STATIC) $(DEPLOYDIR)/
	@echo "#!/bin/bash" > $(DEPLOYDIR)/deploy.sh
	@echo "# XenServer Antivirus Deployment Script" >> $(DEPLOYDIR)/deploy.sh
	@echo "# Generated on: $$(date)" >> $(DEPLOYDIR)/deploy.sh
	@echo "" >> $(DEPLOYDIR)/deploy.sh
	@echo "echo '=== XenServer Antivirus Deployment ==='" >> $(DEPLOYDIR)/deploy.sh
	@echo "echo 'Checking target environment...'" >> $(DEPLOYDIR)/deploy.sh
	@echo "" >> $(DEPLOYDIR)/deploy.sh
	@echo "# Check if running as root" >> $(DEPLOYDIR)/deploy.sh
	@echo "if [ \$$(id -u) -ne 0 ]; then" >> $(DEPLOYDIR)/deploy.sh
	@echo "    echo 'ERROR: Must run as root'" >> $(DEPLOYDIR)/deploy.sh
	@echo "    exit 1" >> $(DEPLOYDIR)/deploy.sh
	@echo "fi" >> $(DEPLOYDIR)/deploy.sh
	@echo "" >> $(DEPLOYDIR)/deploy.sh
	@echo "# Check Xen environment" >> $(DEPLOYDIR)/deploy.sh
	@echo "if ! xl info >/dev/null 2>&1; then" >> $(DEPLOYDIR)/deploy.sh
	@echo "    echo 'ERROR: Xen environment not detected'" >> $(DEPLOYDIR)/deploy.sh
	@echo "    exit 1" >> $(DEPLOYDIR)/deploy.sh
	@echo "fi" >> $(DEPLOYDIR)/deploy.sh
	@echo "" >> $(DEPLOYDIR)/deploy.sh
	@echo "echo 'Installing XenServer Antivirus Host Service...'" >> $(DEPLOYDIR)/deploy.sh
	@echo "install -m 755 xenserver-antivirus-cross /usr/local/bin/" >> $(DEPLOYDIR)/deploy.sh
	@echo "install -m 755 xenserver-antivirus-cross-static /usr/local/bin/" >> $(DEPLOYDIR)/deploy.sh
	@echo "" >> $(DEPLOYDIR)/deploy.sh
	@echo "echo 'Testing installation...'" >> $(DEPLOYDIR)/deploy.sh
	@echo "/usr/local/bin/xenserver-antivirus-cross-static --test" >> $(DEPLOYDIR)/deploy.sh
	@echo "" >> $(DEPLOYDIR)/deploy.sh
	@echo "echo 'Installation complete!'" >> $(DEPLOYDIR)/deploy.sh
	@echo "echo 'Usage: /usr/local/bin/xenserver-antivirus-cross-static [options]'" >> $(DEPLOYDIR)/deploy.sh
	@chmod +x $(DEPLOYDIR)/deploy.sh
	@echo "Creating README..." > $(DEPLOYDIR)/README.md
	@echo "# XenServer Antivirus Host Service - Cross-Compiled" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "## Files" >> $(DEPLOYDIR)/README.md
	@echo "- \`xenserver-antivirus-cross\`: Dynamic version" >> $(DEPLOYDIR)/README.md
	@echo "- \`xenserver-antivirus-cross-static\`: Static version (recommended)" >> $(DEPLOYDIR)/README.md
	@echo "- \`deploy.sh\`: Automated deployment script" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "## Deployment" >> $(DEPLOYDIR)/README.md
	@echo "1. Copy this directory to your Xen host" >> $(DEPLOYDIR)/README.md
	@echo "2. Run: \`sudo ./deploy.sh\`" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "## Manual Installation" >> $(DEPLOYDIR)/README.md
	@echo "\`\`\`bash" >> $(DEPLOYDIR)/README.md
	@echo "# Test environment" >> $(DEPLOYDIR)/README.md
	@echo "./xenserver-antivirus-cross-static --test" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "# Install" >> $(DEPLOYDIR)/README.md
	@echo "sudo install -m 755 xenserver-antivirus-cross-static /usr/local/bin/" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "# Run" >> $(DEPLOYDIR)/README.md
	@echo "sudo /usr/local/bin/xenserver-antivirus-cross-static -v" >> $(DEPLOYDIR)/README.md
	@echo "\`\`\`" >> $(DEPLOYDIR)/README.md
	@echo "" >> $(DEPLOYDIR)/README.md
	@echo "## Compatibility" >> $(DEPLOYDIR)/README.md
	@echo "- Compiled with: Xen 4.17.x libraries (WSL)" >> $(DEPLOYDIR)/README.md
	@echo "- Target: Xen 4.13.x - 4.17.x systems" >> $(DEPLOYDIR)/README.md
	@echo "- Build date: $$(date)" >> $(DEPLOYDIR)/README.md
	@ls -la $(DEPLOYDIR)/
	@echo "✅ Deployment package ready in $(DEPLOYDIR)/"
	@echo ""

# 清理
clean:
	@echo "Cleaning cross-compilation build files..."
	rm -rf $(OBJDIR) $(BINDIR)/xenserver-antivirus-cross* $(DEPLOYDIR)

# 快速测试编译
test-compile: $(MAIN_OBJ) $(COMMON_OBJ)
	@echo "✅ Cross-compilation test successful"
	@echo "Objects created:"
	@ls -la $(OBJDIR)/

# 显示构建信息
show-info:
	@echo "=== Cross-Compilation Build Information ==="
	@echo "Source: $(MAIN_SOURCE)"
	@echo "Target Dynamic: $(TARGET_DYNAMIC)"
	@echo "Target Static: $(TARGET_STATIC)"
	@echo "WSL Xen Version: $$(dpkg -l | grep libxen-dev | awk '{print $$3}')"
	@echo "Target Xen Version: 4.13.x - 4.17.x"
	@echo "Compiler: $(CC)"
	@echo "Flags: $(CFLAGS) $(CROSS_CFLAGS)"
	@echo "Libraries: $(STATIC_LIBS)"
	@echo "Deploy Directory: $(DEPLOYDIR)"
	@echo "============================================"

# 创建压缩包
archive: package
	@echo "Creating deployment archive..."
	@tar -czf xenserver-antivirus-cross-$$(date +%Y%m%d-%H%M).tar.gz -C $(DEPLOYDIR) .
	@echo "✅ Archive created: xenserver-antivirus-cross-$$(date +%Y%m%d-%H%M).tar.gz"

.PHONY: all check-wsl-env package clean test-compile show-info archive
