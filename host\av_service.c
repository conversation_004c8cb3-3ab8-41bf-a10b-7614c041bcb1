#ifdef SIMPLE_BUILD
#include "av_host_simple.h"
#else
#include "av_host.h"
#endif
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <pthread.h>

/* 初始化宿主机服务 */
int av_host_service_init(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_INFO, "Initializing host service...");

    /* 清零服务结构 */
    memset(service, 0, sizeof(av_host_service_t));

    /* 初始化libxc接口 */
    service->xc_handle = xc_interface_open(NULL, NULL, 0);
    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "Failed to open libxc interface: %s", strerror(errno));
        return AV_ERROR_XEN_INTERFACE;
    }

    av_log(AV_LOG_DEBUG, "libxc interface opened successfully");

    /* 初始化XenStore接口 */
    service->xs_handle = xs_open(0);
    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "Failed to open XenStore interface: %s", strerror(errno));
        xc_interface_close(service->xc_handle);
        service->xc_handle = NULL;
        return AV_ERROR_XENSTORE;
    }

    av_log(AV_LOG_DEBUG, "XenStore interface opened successfully");

    /* 初始化互斥锁 */
    int ret = pthread_mutex_init(&service->context_mutex, NULL);
    if (ret != 0) {
        av_log(AV_LOG_ERROR, "Failed to initialize context mutex: %s", strerror(ret));
        xs_close(service->xs_handle);
        xc_interface_close(service->xc_handle);
        service->xs_handle = NULL;
        service->xc_handle = NULL;
        return AV_ERROR_MEMORY_ALLOC;
    }

    av_log(AV_LOG_DEBUG, "Context mutex initialized successfully");

    /* 初始化上下文数组 */
    service->contexts = NULL;
    service->context_count = 0;
    service->shutdown_requested = 0;

    av_log(AV_LOG_INFO, "Host service initialized successfully");
    return AV_SUCCESS;
}

/* 启动宿主机服务 */
int av_host_service_start(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xc_handle || !service->xs_handle) {
        av_log(AV_LOG_ERROR, "Service not properly initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_INFO, "Starting host service...");

    /* 启动监听线程 */
    int ret = av_start_monitor_thread(service);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to start monitor thread: %s", av_error_string(ret));
        return ret;
    }

    av_log(AV_LOG_INFO, "Host service started successfully");
    return AV_SUCCESS;
}

/* 停止宿主机服务 */
int av_host_service_stop(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_INFO, "Stopping host service...");

    /* 停止监听线程 */
    int ret = av_stop_monitor_thread(service);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_WARN, "Failed to stop monitor thread cleanly: %s", av_error_string(ret));
    }

    av_log(AV_LOG_INFO, "Host service stopped successfully");
    return AV_SUCCESS;
}

/* 清理宿主机服务 */
void av_host_service_cleanup(av_host_service_t* service) {
    if (!service) {
        return;
    }

    av_log(AV_LOG_INFO, "Cleaning up host service...");

    /* 停止服务 */
    av_host_service_stop(service);

    /* 清理上下文数组 */
    if (service->contexts) {
        pthread_mutex_lock(&service->context_mutex);
        
        for (size_t i = 0; i < service->context_count; i++) {
            av_context_t* ctx = &service->contexts[i];
            if (ctx->mapped_addr) {
                /* 这里应该清理共享内存映射 */
                av_log(AV_LOG_DEBUG, "Cleaning up context for VM %s", ctx->vm_uuid);
            }
        }
        
        free(service->contexts);
        service->contexts = NULL;
        service->context_count = 0;
        
        pthread_mutex_unlock(&service->context_mutex);
    }

    /* 销毁互斥锁 */
    pthread_mutex_destroy(&service->context_mutex);

    /* 关闭XenStore接口 */
    if (service->xs_handle) {
        xs_close(service->xs_handle);
        service->xs_handle = NULL;
        av_log(AV_LOG_DEBUG, "XenStore interface closed");
    }

    /* 关闭libxc接口 */
    if (service->xc_handle) {
        xc_interface_close(service->xc_handle);
        service->xc_handle = NULL;
        av_log(AV_LOG_DEBUG, "libxc interface closed");
    }

    av_log(AV_LOG_INFO, "Host service cleanup completed");
}

/* 添加VM上下文 */
int av_add_vm_context(av_host_service_t* service, const char* vm_uuid) {
    if (!service || !vm_uuid) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_add_vm_context");
        return AV_ERROR_INVALID_PARAM;
    }

    /* 验证UUID格式 */
    int ret = av_validate_uuid(vm_uuid);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Invalid UUID format: %s", vm_uuid);
        return ret;
    }

    pthread_mutex_lock(&service->context_mutex);

    /* 检查是否已存在 */
    for (size_t i = 0; i < service->context_count; i++) {
        if (strcmp(service->contexts[i].vm_uuid, vm_uuid) == 0) {
            pthread_mutex_unlock(&service->context_mutex);
            av_log(AV_LOG_WARN, "Context for VM %s already exists", vm_uuid);
            return AV_ERROR_ALREADY_EXISTS;
        }
    }

    /* 扩展上下文数组 */
    av_context_t* new_contexts = realloc(service->contexts, 
                                        (service->context_count + 1) * sizeof(av_context_t));
    if (!new_contexts) {
        pthread_mutex_unlock(&service->context_mutex);
        av_log(AV_LOG_ERROR, "Failed to allocate memory for new context");
        return AV_ERROR_MEMORY_ALLOC;
    }

    service->contexts = new_contexts;

    /* 初始化新上下文 */
    av_context_t* new_ctx = &service->contexts[service->context_count];
    memset(new_ctx, 0, sizeof(av_context_t));
    strncpy(new_ctx->vm_uuid, vm_uuid, sizeof(new_ctx->vm_uuid) - 1);
    new_ctx->initialized = 1;

    service->context_count++;

    pthread_mutex_unlock(&service->context_mutex);

    av_log(AV_LOG_INFO, "Added context for VM %s", vm_uuid);
    return AV_SUCCESS;
}

/* 移除VM上下文 */
int av_remove_vm_context(av_host_service_t* service, const char* vm_uuid) {
    if (!service || !vm_uuid) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_remove_vm_context");
        return AV_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&service->context_mutex);

    /* 查找上下文 */
    for (size_t i = 0; i < service->context_count; i++) {
        if (strcmp(service->contexts[i].vm_uuid, vm_uuid) == 0) {
            /* 清理上下文资源 */
            av_context_t* ctx = &service->contexts[i];
            if (ctx->mapped_addr) {
                /* 这里应该清理共享内存映射 */
                av_log(AV_LOG_DEBUG, "Cleaning up resources for VM %s", vm_uuid);
            }

            /* 移动后续元素 */
            if (i < service->context_count - 1) {
                memmove(&service->contexts[i], &service->contexts[i + 1],
                       (service->context_count - i - 1) * sizeof(av_context_t));
            }

            service->context_count--;

            /* 缩小数组 */
            if (service->context_count > 0) {
                av_context_t* new_contexts = realloc(service->contexts,
                                                    service->context_count * sizeof(av_context_t));
                if (new_contexts) {
                    service->contexts = new_contexts;
                }
            } else {
                free(service->contexts);
                service->contexts = NULL;
            }

            pthread_mutex_unlock(&service->context_mutex);
            av_log(AV_LOG_INFO, "Removed context for VM %s", vm_uuid);
            return AV_SUCCESS;
        }
    }

    pthread_mutex_unlock(&service->context_mutex);
    av_log(AV_LOG_WARN, "Context for VM %s not found", vm_uuid);
    return AV_ERROR_VM_NOT_FOUND;
}

/* 查找VM上下文 */
av_context_t* av_find_vm_context(av_host_service_t* service, const char* vm_uuid) {
    if (!service || !vm_uuid) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_find_vm_context");
        return NULL;
    }

    pthread_mutex_lock(&service->context_mutex);

    for (size_t i = 0; i < service->context_count; i++) {
        if (strcmp(service->contexts[i].vm_uuid, vm_uuid) == 0) {
            av_context_t* ctx = &service->contexts[i];
            pthread_mutex_unlock(&service->context_mutex);
            return ctx;
        }
    }

    pthread_mutex_unlock(&service->context_mutex);
    return NULL;
}