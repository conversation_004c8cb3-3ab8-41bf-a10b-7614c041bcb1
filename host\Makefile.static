# XenServer宿主机防病毒服务Makefile - 静态链接版本

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE -static

# 检查是否有Xen库
XEN_LIBS_AVAILABLE := $(shell pkg-config --exists xencontrol xenstore 2>/dev/null && echo yes || echo no)

ifneq ($(XEN_LIBS_AVAILABLE),yes)
    $(error Xen libraries not found. Please install libxen-dev package)
endif

# 获取Xen库的编译和链接标志（静态版本）
XEN_CFLAGS := $(shell pkg-config --cflags xencontrol xenstore 2>/dev/null)
XEN_LDFLAGS := $(shell pkg-config --libs --static xencontrol xenstore 2>/dev/null)

# 如果pkg-config不支持--static，手动指定静态库
ifeq ($(XEN_LDFLAGS),)
    XEN_LDFLAGS = -lxenctrl -lxenstore -lxengnttab -lxentoollog -lxencall -lxenevtchn -lxenforeignmemory -lxendevicemodel -lxentoolcore
endif

CFLAGS += $(XEN_CFLAGS)
LDFLAGS = $(XEN_LDFLAGS) -lpthread -static

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj_static
BINDIR = bin

# 源文件和目标文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
HOST_SOURCES = main_real.c av_vm_discovery.c av_xenstore.c
SOURCES = $(COMMON_SOURCES) $(HOST_SOURCES)
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/xenserver-antivirus-host-static

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I.

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Linking $@ (static)..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Build complete: $@"
	@echo "Built as static binary with Xen libraries (version: $(shell pkg-config --modversion xencontrol))"
	@echo "Binary size: $(shell ls -lh $@ | awk '{print $$5}')"

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $<..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning build files..."
	rm -rf $(OBJDIR) $(BINDIR)/xenserver-antivirus-host-static

# 检查依赖
check-deps:
	@echo "Checking dependencies for static build..."
	@echo "✓ Xen libraries found:"
	@pkg-config --modversion xencontrol xenstore
	@echo "✓ Xen headers found at: /usr/include/xen"
	@echo "✓ Static libraries check:"
	@find /usr/lib* -name "libxenctrl.a" 2>/dev/null | head -1 || echo "⚠ libxenctrl.a not found"
	@find /usr/lib* -name "libxenstore.a" 2>/dev/null | head -1 || echo "⚠ libxenstore.a not found"
	@echo "✓ Compilation flags:"
	@echo "  CFLAGS: $(CFLAGS)"
	@echo "  LDFLAGS: $(LDFLAGS)"

# 检查二进制文件信息
info: $(TARGET)
	@echo "Binary information:"
	@echo "  File: $(TARGET)"
	@echo "  Size: $(shell ls -lh $(TARGET) | awk '{print $$5}')"
	@echo "  Type: $(shell file $(TARGET))"
	@echo "  Dependencies:"
	@ldd $(TARGET) 2>/dev/null || echo "  Static binary - no dynamic dependencies"
	@echo "  Symbols:"
	@nm $(TARGET) | grep -E "(xc_|xs_|xengnttab_)" | head -10 || echo "  No Xen symbols found"

# 安装程序
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/xenserver-antivirus-host
	sudo chmod +x /usr/local/bin/xenserver-antivirus-host
	
	# 创建配置目录
	sudo mkdir -p /etc/xenserver-antivirus
	
	# 创建日志目录
	sudo mkdir -p /var/log/xenserver-antivirus
	
	@echo "Installation complete"
	@echo "Static binary installed - no runtime dependencies required"

# 运行程序（需要root权限）
run: $(TARGET)
	@echo "Running $(TARGET) (requires root privileges)..."
	sudo $(TARGET)

# 测试运行
test: $(TARGET)
	@echo "Testing $(TARGET)..."
	sudo $(TARGET) --help
	@echo "Test completed"

# 创建部署包
package: $(TARGET)
	@echo "Creating deployment package..."
	@mkdir -p deploy/bin
	@mkdir -p deploy/scripts
	@mkdir -p deploy/docs
	
	# 复制二进制文件
	cp $(TARGET) deploy/bin/xenserver-antivirus-host
	
	# 复制部署脚本
	cp ../scripts/install-host.sh deploy/scripts/ 2>/dev/null || echo "#!/bin/bash\necho 'Install script not found'" > deploy/scripts/install-host.sh
	chmod +x deploy/scripts/install-host.sh
	
	# 复制文档
	cp ../docs/*.md deploy/docs/ 2>/dev/null || echo "Documentation not found"
	
	# 创建README
	echo "# XenServer Antivirus Host Service - Static Build" > deploy/README.md
	echo "" >> deploy/README.md
	echo "This package contains a statically linked version of the XenServer Antivirus Host Service." >> deploy/README.md
	echo "" >> deploy/README.md
	echo "## Installation" >> deploy/README.md
	echo "" >> deploy/README.md
	echo "1. Copy the binary to the XenServer host:" >> deploy/README.md
	echo "   scp bin/xenserver-antivirus-host root@xenserver-host:/usr/local/bin/" >> deploy/README.md
	echo "   chmod +x /usr/local/bin/xenserver-antivirus-host" >> deploy/README.md
	echo "" >> deploy/README.md
	echo "2. Run the service:" >> deploy/README.md
	echo "   /usr/local/bin/xenserver-antivirus-host --help" >> deploy/README.md
	
	# 创建压缩包
	tar -czf xenserver-antivirus-host-static.tar.gz -C deploy .
	@echo "Deployment package created: xenserver-antivirus-host-static.tar.gz"
	@echo "Package contents:"
	@tar -tzf xenserver-antivirus-host-static.tar.gz

.PHONY: all clean directories check-deps info install run test package