#!/bin/bash
# XenServer防病毒部署脚本 - glibc 2.17兼容版本
# 目标: Citrix Hypervisor 8.2.1
# 生成时间: Fri Sep  5 13:47:32 CST 2025

echo '=== XenServer防病毒部署 (glibc 2.17兼容) ==='
echo '检查目标环境...'

# 检查是否以root运行
if [ $(id -u) -ne 0 ]; then
    echo 'ERROR: 必须以root权限运行'
    exit 1
fi

# 检查glibc版本
GLIBC_VERSION=$(ldd --version | head -1 | grep -o '[0-9]\+\.[0-9]\+' | head -1)
echo "检测到glibc版本: $GLIBC_VERSION"
if [ "$GLIBC_VERSION" != "2.17" ]; then
    echo "WARNING: 此程序专为glibc 2.17编译，当前版本: $GLIBC_VERSION"
    echo "如果运行出现问题，请使用对应版本的程序"
fi

# 检查Xen环境
if ! xl info >/dev/null 2>&1; then
    echo 'ERROR: 未检测到Xen环境'
    echo '请确保在Citrix Hypervisor宿主机上运行'
    exit 1
fi

echo '安装XenServer防病毒宿主机服务...'
install -m 755 xenserver-antivirus-glibc217-static /usr/local/bin/

echo '测试安装...'
/usr/local/bin/xenserver-antivirus-glibc217-static --test

echo '安装完成！'
echo '使用方法: /usr/local/bin/xenserver-antivirus-glibc217-static [选项]'
