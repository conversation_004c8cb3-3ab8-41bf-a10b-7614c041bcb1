#!/bin/bash
# XenServer防病毒部署脚本 - glibc 2.17兼容版本
# 目标: Citrix Hypervisor 8.2.1
# 生成时间: Fri Sep  5 12:30:25 CST 2025

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo '=== XenServer防病毒部署 (glibc 2.17兼容) ==='
log_info '检查目标环境...'

# 检查是否以root运行
if [ $(id -u) -ne 0 ]; then
    log_error '必须以root权限运行'
    exit 1
fi

# 检查glibc版本
GLIBC_VERSION=$(ldd --version | head -1 | grep -o '[0-9]\+\.[0-9]\+' | head -1)
echo "检测到glibc版本: $GLIBC_VERSION"
if [ "$GLIBC_VERSION" != "2.17" ]; then
    echo "WARNING: 此程序专为glibc 2.17编译，当前版本: $GLIBC_VERSION"
    echo "如果运行出现问题，请使用对应版本的程序"
fi

# 检查和修复Xen权限
check_and_fix_xen_permissions() {
    log_info "检查Xen权限..."

    # 检查xenstored服务
    if ! pgrep -x "xenstored" > /dev/null; then
        log_warning "xenstored服务未运行，尝试启动..."
        systemctl start xenstored 2>/dev/null || service xenstored start 2>/dev/null || true
        sleep 2
    fi

    # 检查/dev/xen/xenstore权限
    if [ -e "/dev/xen/xenstore" ]; then
        if [ ! -r "/dev/xen/xenstore" ] || [ ! -w "/dev/xen/xenstore" ]; then
            log_warning "修复/dev/xen/xenstore权限..."
            chmod 666 /dev/xen/xenstore 2>/dev/null || true
        fi
    fi

    # 检查用户组
    if getent group xen >/dev/null 2>&1; then
        usermod -a -G xen root 2>/dev/null || true
    fi
}

# 检查Xen环境
if ! xl info >/dev/null 2>&1; then
    log_error '未检测到Xen环境'
    log_info '尝试修复权限问题...'
    check_and_fix_xen_permissions

    # 再次测试
    if ! xl info >/dev/null 2>&1; then
        log_error '仍无法访问Xen环境'
        log_info '请确保在Citrix Hypervisor宿主机上运行'
        log_info '如果问题持续，请运行权限修复脚本'
        exit 1
    else
        log_success 'Xen环境权限修复成功'
    fi
else
    log_success 'Xen环境检查通过'
fi

log_info '安装XenServer防病毒宿主机服务...'
install -m 755 xenserver-antivirus-glibc217-static /usr/local/bin/

log_info '测试安装...'
if /usr/local/bin/xenserver-antivirus-glibc217-static --test 2>/dev/null; then
    log_success '安装测试通过'
else
    log_warning '安装测试失败，但程序已安装'
    log_info '可能需要手动检查权限问题'
fi

log_success '安装完成！'
echo ''
log_info '使用方法:'
echo '  /usr/local/bin/xenserver-antivirus-glibc217-static --help'
echo '  sudo /usr/local/bin/xenserver-antivirus-glibc217-static -v'
echo ''
log_info '如果遇到权限问题，请以root权限运行程序'
