#include "test_common.h"
#include "../host/av_host.h"
#include <string.h>

/* 模拟的XenStore操作 */
static mock_function_t mock_xs_read;
static mock_function_t mock_xs_write;
static mock_function_t mock_xs_directory;

/* 测试XenStore路径构建和验证 */
int test_xenstore_path_operations(void) {
    const char* test_uuid = "12345678-1234-1234-1234-123456789abc";
    char path[512];
    
    /* 测试正常路径构建 */
    int ret = av_build_xenstore_path(test_uuid, AV_XENSTORE_SHM_ID_KEY, path, sizeof(path));
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Path building should succeed");
    
    char expected_path[512];
    snprintf(expected_path, sizeof(expected_path), "/guest/%s/data/av_shm_id", test_uuid);
    TEST_ASSERT_STRING_EQUAL(expected_path, path, "Built path should match expected");
    
    /* 测试不同的键 */
    ret = av_build_xenstore_path(test_uuid, AV_XENSTORE_STATUS_KEY, path, sizeof(path));
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Status path building should succeed");
    
    snprintf(expected_path, sizeof(expected_path), "/guest/%s/data/av_status", test_uuid);
    TEST_ASSERT_STRING_EQUAL(expected_path, path, "Status path should match expected");
    
    /* 测试配置键 */
    ret = av_build_xenstore_path(test_uuid, AV_XENSTORE_CONFIG_KEY, path, sizeof(path));
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Config path building should succeed");
    
    snprintf(expected_path, sizeof(expected_path), "/guest/%s/data/av_config", test_uuid);
    TEST_ASSERT_STRING_EQUAL(expected_path, path, "Config path should match expected");
    
    return 0;
}

/* 测试XenStore路径验证 */
int test_xenstore_path_validation(void) {
    char path[512];
    
    /* 测试无效UUID */
    int ret = av_build_xenstore_path("invalid-uuid", AV_XENSTORE_SHM_ID_KEY, path, sizeof(path));
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Invalid UUID should fail");
    
    /* 测试NULL参数 */
    ret = av_build_xenstore_path(NULL, AV_XENSTORE_SHM_ID_KEY, path, sizeof(path));
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "NULL UUID should fail");
    
    ret = av_build_xenstore_path("12345678-1234-1234-1234-123456789abc", NULL, path, sizeof(path));
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "NULL key should fail");
    
    ret = av_build_xenstore_path("12345678-1234-1234-1234-123456789abc", AV_XENSTORE_SHM_ID_KEY, NULL, sizeof(path));
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "NULL path buffer should fail");
    
    /* 测试缓冲区太小 */
    char small_buffer[10];
    ret = av_build_xenstore_path("12345678-1234-1234-1234-123456789abc", AV_XENSTORE_SHM_ID_KEY, small_buffer, sizeof(small_buffer));
    TEST_ASSERT_EQUAL(AV_ERROR_MEMORY_ALLOC, ret, "Small buffer should fail");
    
    return 0;
}

/* 测试XenStore常量定义 */
int test_xenstore_constants(void) {
    /* 验证基础路径常量 */
    TEST_ASSERT_STRING_EQUAL("/guest", AV_XENSTORE_BASE_PATH, "Base path should be correct");
    TEST_ASSERT_STRING_EQUAL("/data", AV_XENSTORE_DATA_PATH, "Data path should be correct");
    
    /* 验证键常量 */
    TEST_ASSERT_STRING_EQUAL("av_shm_id", AV_XENSTORE_SHM_ID_KEY, "SHM ID key should be correct");
    TEST_ASSERT_STRING_EQUAL("av_status", AV_XENSTORE_STATUS_KEY, "Status key should be correct");
    TEST_ASSERT_STRING_EQUAL("av_config", AV_XENSTORE_CONFIG_KEY, "Config key should be correct");
    
    /* 验证键长度合理 */
    TEST_ASSERT(strlen(AV_XENSTORE_SHM_ID_KEY) > 0, "SHM ID key should not be empty");
    TEST_ASSERT(strlen(AV_XENSTORE_STATUS_KEY) > 0, "Status key should not be empty");
    TEST_ASSERT(strlen(AV_XENSTORE_CONFIG_KEY) > 0, "Config key should not be empty");
    
    return 0;
}

/* 测试批量操作结构 */
int test_xenstore_batch_operations_structure(void) {
    av_xenstore_batch_op_t ops[3];
    
    /* 初始化批量操作 */
    ops[0].path = strdup("/test/path1");
    ops[0].value = strdup("value1");
    ops[0].operation = 1; /* write */
    
    ops[1].path = strdup("/test/path2");
    ops[1].value = NULL;
    ops[1].operation = 0; /* read */
    
    ops[2].path = strdup("/test/path3");
    ops[2].value = NULL;
    ops[2].operation = 2; /* delete */
    
    /* 验证结构 */
    TEST_ASSERT_STRING_EQUAL("/test/path1", ops[0].path, "First operation path should be correct");
    TEST_ASSERT_STRING_EQUAL("value1", ops[0].value, "First operation value should be correct");
    TEST_ASSERT_EQUAL(1, ops[0].operation, "First operation type should be write");
    
    TEST_ASSERT_STRING_EQUAL("/test/path2", ops[1].path, "Second operation path should be correct");
    TEST_ASSERT(ops[1].value == NULL, "Second operation value should be NULL for read");
    TEST_ASSERT_EQUAL(0, ops[1].operation, "Second operation type should be read");
    
    TEST_ASSERT_STRING_EQUAL("/test/path3", ops[2].path, "Third operation path should be correct");
    TEST_ASSERT(ops[2].value == NULL, "Third operation value should be NULL for delete");
    TEST_ASSERT_EQUAL(2, ops[2].operation, "Third operation type should be delete");
    
    /* 清理内存 */
    for (int i = 0; i < 3; i++) {
        free(ops[i].path);
        if (ops[i].value) {
            free(ops[i].value);
        }
    }
    
    return 0;
}

/* 测试XenStore统计功能 */
int test_xenstore_statistics(void) {
    /* 初始化统计 */
    av_xenstore_stats_init();
    
    /* 记录一些操作 */
    av_xenstore_stats_record_read(100, 1); /* 成功读取100字节 */
    av_xenstore_stats_record_read(50, 0);  /* 失败读取 */
    av_xenstore_stats_record_write(200, 1); /* 成功写入200字节 */
    av_xenstore_stats_record_write(75, 1);  /* 成功写入75字节 */
    av_xenstore_stats_record_write(30, 0);  /* 失败写入 */
    
    /* 生成报告（这里只是确保不会崩溃） */
    av_xenstore_stats_report();
    
    return 0;
}

/* 测试XenStore路径工具函数 */
int test_xenstore_path_utilities(void) {
    const char* test_paths[] = {
        "/guest/12345678-1234-1234-1234-123456789abc/data/av_shm_id",
        "/guest/87654321-4321-4321-4321-cba987654321/data/av_status",
        "/guest/aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee/data/av_config"
    };
    
    /* 测试路径格式验证 */
    for (int i = 0; i < 3; i++) {
        const char* path = test_paths[i];
        
        /* 检查路径是否以正确的前缀开始 */
        TEST_ASSERT(strncmp(path, "/guest/", 7) == 0, "Path should start with /guest/");
        
        /* 检查路径是否包含/data/ */
        TEST_ASSERT(strstr(path, "/data/") != NULL, "Path should contain /data/");
        
        /* 检查路径长度合理 */
        TEST_ASSERT(strlen(path) > 20, "Path should be reasonably long");
        TEST_ASSERT(strlen(path) < 200, "Path should not be too long");
    }
    
    return 0;
}

/* 测试错误处理 */
int test_xenstore_error_handling(void) {
    /* 测试各种错误情况的处理 */
    
    /* 测试空指针处理 */
    char buffer[100];
    int ret;
    
    /* 这些测试需要模拟的XenStore服务，这里只测试参数验证 */
    ret = av_build_xenstore_path(NULL, "key", buffer, sizeof(buffer));
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "NULL UUID should return invalid param error");
    
    ret = av_build_xenstore_path("valid-uuid", NULL, buffer, sizeof(buffer));
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "NULL key should return invalid param error");
    
    ret = av_build_xenstore_path("valid-uuid", "key", NULL, sizeof(buffer));
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "NULL buffer should return invalid param error");
    
    ret = av_build_xenstore_path("valid-uuid", "key", buffer, 0);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Zero buffer size should return invalid param error");
    
    return 0;
}

/* 测试XenStore键值对格式 */
int test_xenstore_key_value_format(void) {
    /* 测试共享内存ID格式 */
    char shm_id_str[32];
    uint32_t test_shm_id = 12345;
    
    snprintf(shm_id_str, sizeof(shm_id_str), "%u", test_shm_id);
    TEST_ASSERT_STRING_EQUAL("12345", shm_id_str, "SHM ID should format correctly");
    
    /* 测试状态值格式 */
    const char* status_values[] = {"active", "inactive", "error", "initializing"};
    
    for (int i = 0; i < 4; i++) {
        const char* status = status_values[i];
        TEST_ASSERT(strlen(status) > 0, "Status value should not be empty");
        TEST_ASSERT(strlen(status) < 50, "Status value should be reasonably short");
    }
    
    return 0;
}

/* 主测试函数 */
int main(void) {
    test_init();
    
    /* 初始化模拟函数 */
    mock_reset(&mock_xs_read);
    mock_reset(&mock_xs_write);
    mock_reset(&mock_xs_directory);
    
    /* 运行测试 */
    RUN_TEST(test_xenstore_path_operations);
    RUN_TEST(test_xenstore_path_validation);
    RUN_TEST(test_xenstore_constants);
    RUN_TEST(test_xenstore_batch_operations_structure);
    RUN_TEST(test_xenstore_statistics);
    RUN_TEST(test_xenstore_path_utilities);
    RUN_TEST(test_xenstore_error_handling);
    RUN_TEST(test_xenstore_key_value_format);
    
    /* 输出测试结果 */
    test_summary();
    
    return (tests_failed > 0) ? 1 : 0;
}