#ifndef AV_COMMON_H
#define AV_COMMON_H

#include <stdint.h>
#include <stddef.h>

/* 版本信息 */
#define AV_VERSION_MAJOR 1
#define AV_VERSION_MINOR 0
#define AV_VERSION_PATCH 0
#define AV_VERSION "1.0.0"

/* 共享内存大小 */
#define AV_SHARED_MEMORY_SIZE 4096

/* 命令字段大小 */
#define AV_COMMAND_SIZE 16
#define AV_RESERVED_SIZE 48

/* 协议命令定义 */
#define AV_SCAN_REQUEST_CMD "SCAN_REQ"
#define AV_SCAN_RESPONSE_CMD "SCAN_ACK"

/* 超时定义 */
#define AV_RESPONSE_TIMEOUT_MS 5000

/* UUID字符串长度 (包含终止符) */
#define AV_UUID_STRING_LENGTH 37

/* XenStore路径定义 */
#define AV_XENSTORE_BASE_PATH "/guest"
#define AV_XENSTORE_DATA_PATH "/data"
#define AV_XENSTORE_SHM_ID_KEY "av_shm_id"
#define AV_XENSTORE_STATUS_KEY "av_status"
#define AV_XENSTORE_CONFIG_KEY "av_config"

/* 错误码定义 */
typedef enum {
    AV_SUCCESS = 0,
    AV_ERROR_MEMORY_ALLOC = -1,
    AV_ERROR_PERMISSION = -2,
    AV_ERROR_XEN_INTERFACE = -3,
    AV_ERROR_VM_NOT_FOUND = -4,
    AV_ERROR_XENSTORE = -5,
    AV_ERROR_TIMEOUT = -6,
    AV_ERROR_PROTOCOL = -7,
    AV_ERROR_INVALID_PARAM = -8,
    AV_ERROR_NOT_INITIALIZED = -9,
    AV_ERROR_ALREADY_EXISTS = -10,
    AV_ERROR_XC_INTERFACE = -11,
    AV_ERROR_XENSTORE_OPEN = -12,
    AV_ERROR_XENSTORE_READ = -13,
    AV_ERROR_XENSTORE_WATCH = -14,
    AV_ERROR_BUFFER_TOO_SMALL = -15,
    AV_ERROR_MEMORY_MAP = -16,
    AV_ERROR_MEMORY_UNMAP = -17,
    AV_ERROR_MUTEX_INIT = -18,
    AV_ERROR_COND_INIT = -19,
    AV_ERROR_THREAD_CREATE = -20,
    AV_ERROR_FORK = -21,
    AV_ERROR_SETSID = -22,
    AV_ERROR_CHDIR = -23,
    AV_ERROR_INIT = -24
} av_error_code_t;

/* 共享内存布局 */
typedef struct {
    char command[AV_COMMAND_SIZE];      /* 命令字段: "SCAN_REQ" 或 "SCAN_ACK" */
    char reserved[AV_RESERVED_SIZE];    /* 保留字段，用于未来扩展 */
    /* 剩余空间可用于传输扫描数据或结果 */
} av_shared_memory_layout_t;

/* 客户端类型定义 */
typedef enum {
    AV_CLIENT_LINUX = 1,
    AV_CLIENT_WINDOWS = 2
} av_client_type_t;

/* 防病毒上下文结构 */
typedef struct {
    char vm_uuid[AV_UUID_STRING_LENGTH];    /* 虚拟机UUID */
    uint32_t domain_id;                     /* 域ID */
    uint32_t shm_id;                        /* 共享内存ID */
    void* mapped_addr;                      /* 映射地址 */
    int monitoring_active;                  /* 监听状态标志 */
    int initialized;                        /* 初始化状态标志 */
    av_client_type_t client_type;           /* 客户端类型 */
} av_context_t;

/* 日志级别定义 */
typedef enum {
    AV_LOG_ERROR = 0,
    AV_LOG_WARN = 1,
    AV_LOG_INFO = 2,
    AV_LOG_DEBUG = 3
} av_log_level_t;

/* 函数声明 - 错误处理 */
const char* av_error_string(av_error_code_t error);
void av_log(av_log_level_t level, const char* format, ...);
void av_set_log_level(av_log_level_t level);
void av_log_init(av_log_level_t level, const char* log_file);
void av_log_cleanup(void);

/* 函数声明 - 工具函数 */
int av_validate_uuid(const char* uuid);
int av_build_xenstore_path(const char* vm_uuid, const char* key, char* path, size_t path_size);

#endif /* AV_COMMON_H */