/*
 * Xen接口模拟实现 - 用于开发和测试
 */

#include "av_host_simple.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>

/* 模拟的xc_interface结构 */
struct xc_interface_mock {
    int dummy;
};

/* 模拟的xs_handle结构 */
struct xs_handle_mock {
    int dummy;
};

/* 模拟的xengnttab_handle结构 */
struct xengnttab_handle_mock {
    int dummy;
};

/* 模拟xc_interface_open */
xc_interface* xc_interface_open(void* logger, void* dombuild_logger, unsigned open_flags) {
    (void)logger;
    (void)dombuild_logger;
    (void)open_flags;
    
    av_log(AV_LOG_INFO, "Mock: Opening xc interface");
    
    struct xc_interface_mock* mock = malloc(sizeof(struct xc_interface_mock));
    if (!mock) {
        errno = ENOMEM;
        return NULL;
    }
    
    mock->dummy = 1;
    return (xc_interface*)mock;
}

/* 模拟xc_interface_close */
int xc_interface_close(xc_interface* xch) {
    if (!xch) {
        return -1;
    }
    
    av_log(AV_LOG_INFO, "Mock: Closing xc interface");
    free(xch);
    return 0;
}

/* 模拟xs_open */
struct xs_handle* xs_open(unsigned long flags) {
    (void)flags;
    
    av_log(AV_LOG_INFO, "Mock: Opening XenStore");
    
    struct xs_handle_mock* mock = malloc(sizeof(struct xs_handle_mock));
    if (!mock) {
        errno = ENOMEM;
        return NULL;
    }
    
    mock->dummy = 1;
    return (struct xs_handle*)mock;
}

/* 模拟xs_close */
void xs_close(struct xs_handle* xsh) {
    if (!xsh) {
        return;
    }
    
    av_log(AV_LOG_INFO, "Mock: Closing XenStore");
    free(xsh);
}

/* 模拟xs_mkdir */
bool xs_mkdir(struct xs_handle* h, xs_transaction_t t, const char* path) {
    (void)h;
    (void)t;
    
    av_log(AV_LOG_INFO, "Mock: Creating XenStore path: %s", path);
    return true;
}

/* 模拟xengnttab_open */
xengnttab_handle* xengnttab_open(void* logger, unsigned open_flags) {
    (void)logger;
    (void)open_flags;
    
    av_log(AV_LOG_INFO, "Mock: Opening grant table");
    
    struct xengnttab_handle_mock* mock = malloc(sizeof(struct xengnttab_handle_mock));
    if (!mock) {
        errno = ENOMEM;
        return NULL;
    }
    
    mock->dummy = 1;
    return (xengnttab_handle*)mock;
}

/* 模拟xengnttab_close */
int xengnttab_close(xengnttab_handle* xgt) {
    if (!xgt) {
        return -1;
    }
    
    av_log(AV_LOG_INFO, "Mock: Closing grant table");
    free(xgt);
    return 0;
}