#ifdef SIMPLE_BUILD
#include "av_host_simple.h"
#else
#include "av_host.h"
#endif
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <signal.h>
#include <sys/resource.h>
#include <sys/time.h>

/* 资源跟踪结构 */
typedef struct av_resource {
    void* ptr;                      /* 资源指针 */
    size_t size;                    /* 资源大小 */
    int type;                       /* 资源类型 */
    char description[128];          /* 资源描述 */
    struct timespec alloc_time;     /* 分配时间 */
    struct av_resource* next;       /* 链表下一个节点 */
} av_resource_t;

/* 资源类型定义 */
typedef enum {
    AV_RESOURCE_TYPE_MEMORY = 1,
    AV_RESOURCE_TYPE_FILE_HANDLE = 2,
    AV_RESOURCE_TYPE_SHARED_MEMORY = 3,
    AV_RESOURCE_TYPE_THREAD = 4,
    AV_RESOURCE_TYPE_MUTEX = 5,
    AV_RESOURCE_TYPE_XEN_HANDLE = 6
} av_resource_type_t;

/* 全局资源管理器 */
typedef struct {
    av_resource_t* resources;       /* 资源链表头 */
    pthread_mutex_t mutex;          /* 保护资源链表的互斥锁 */
    int initialized;                /* 初始化标志 */
    size_t total_memory;            /* 总分配内存 */
    size_t peak_memory;             /* 峰值内存使用 */
    int resource_count;             /* 资源总数 */
    int leak_detection_enabled;    /* 内存泄漏检测开关 */
} av_resource_manager_t;

static av_resource_manager_t g_resource_manager = {0};

/* 初始化资源管理器 */
int av_resource_manager_init(void) {
    if (g_resource_manager.initialized) {
        return AV_SUCCESS;
    }

    memset(&g_resource_manager, 0, sizeof(g_resource_manager));
    
    int ret = pthread_mutex_init(&g_resource_manager.mutex, NULL);
    if (ret != 0) {
        av_log(AV_LOG_ERROR, "Failed to initialize resource manager mutex: %s", strerror(ret));
        return AV_ERROR_MEMORY_ALLOC;
    }

    g_resource_manager.initialized = 1;
    g_resource_manager.leak_detection_enabled = 1;
    
    av_log(AV_LOG_INFO, "Resource manager initialized");
    return AV_SUCCESS;
}

/* 清理资源管理器 */
void av_resource_manager_cleanup(void) {
    if (!g_resource_manager.initialized) {
        return;
    }

    av_log(AV_LOG_INFO, "Cleaning up resource manager...");

    pthread_mutex_lock(&g_resource_manager.mutex);

    /* 检查是否有未释放的资源 */
    av_resource_t* current = g_resource_manager.resources;
    int leak_count = 0;

    while (current) {
        leak_count++;
        av_log(AV_LOG_WARN, "Resource leak detected: %s (type: %d, size: %zu, ptr: %p)", 
               current->description, current->type, current->size, current->ptr);
        
        av_resource_t* next = current->next;
        
        /* 尝试清理资源 */
        switch (current->type) {
            case AV_RESOURCE_TYPE_MEMORY:
                free(current->ptr);
                break;
            case AV_RESOURCE_TYPE_FILE_HANDLE:
                if (current->ptr && *(int*)current->ptr != -1) {
                    close(*(int*)current->ptr);
                }
                break;
            case AV_RESOURCE_TYPE_SHARED_MEMORY:
                /* 共享内存需要特殊处理 */
                break;
            default:
                av_log(AV_LOG_WARN, "Unknown resource type for cleanup: %d", current->type);
                break;
        }
        
        free(current);
        current = next;
    }

    if (leak_count > 0) {
        av_log(AV_LOG_ERROR, "Resource manager found %d resource leaks", leak_count);
    } else {
        av_log(AV_LOG_INFO, "No resource leaks detected");
    }

    g_resource_manager.resources = NULL;
    g_resource_manager.initialized = 0;

    pthread_mutex_unlock(&g_resource_manager.mutex);
    pthread_mutex_destroy(&g_resource_manager.mutex);

    av_log(AV_LOG_INFO, "Resource manager cleanup completed");
}

/* 注册资源 */
int av_resource_register(void* ptr, size_t size, av_resource_type_t type, const char* description) {
    if (!g_resource_manager.initialized) {
        av_resource_manager_init();
    }

    if (!ptr || !description) {
        return AV_ERROR_INVALID_PARAM;
    }

    av_resource_t* resource = malloc(sizeof(av_resource_t));
    if (!resource) {
        av_log(AV_LOG_ERROR, "Failed to allocate memory for resource tracking");
        return AV_ERROR_MEMORY_ALLOC;
    }

    resource->ptr = ptr;
    resource->size = size;
    resource->type = type;
    strncpy(resource->description, description, sizeof(resource->description) - 1);
    resource->description[sizeof(resource->description) - 1] = '\0';
    clock_gettime(CLOCK_MONOTONIC, &resource->alloc_time);

    pthread_mutex_lock(&g_resource_manager.mutex);

    resource->next = g_resource_manager.resources;
    g_resource_manager.resources = resource;
    g_resource_manager.resource_count++;

    if (type == AV_RESOURCE_TYPE_MEMORY) {
        g_resource_manager.total_memory += size;
        if (g_resource_manager.total_memory > g_resource_manager.peak_memory) {
            g_resource_manager.peak_memory = g_resource_manager.total_memory;
        }
    }

    pthread_mutex_unlock(&g_resource_manager.mutex);

    av_log(AV_LOG_DEBUG, "Registered resource: %s (type: %d, size: %zu, ptr: %p)", 
           description, type, size, ptr);

    return AV_SUCCESS;
}

/* 注销资源 */
int av_resource_unregister(void* ptr) {
    if (!g_resource_manager.initialized || !ptr) {
        return AV_ERROR_INVALID_PARAM;
    }

    pthread_mutex_lock(&g_resource_manager.mutex);

    av_resource_t* current = g_resource_manager.resources;
    av_resource_t* prev = NULL;

    while (current) {
        if (current->ptr == ptr) {
            /* 找到资源，从链表中移除 */
            if (prev) {
                prev->next = current->next;
            } else {
                g_resource_manager.resources = current->next;
            }

            if (current->type == AV_RESOURCE_TYPE_MEMORY) {
                g_resource_manager.total_memory -= current->size;
            }

            g_resource_manager.resource_count--;

            av_log(AV_LOG_DEBUG, "Unregistered resource: %s (type: %d, size: %zu, ptr: %p)", 
                   current->description, current->type, current->size, ptr);

            free(current);
            pthread_mutex_unlock(&g_resource_manager.mutex);
            return AV_SUCCESS;
        }

        prev = current;
        current = current->next;
    }

    pthread_mutex_unlock(&g_resource_manager.mutex);

    av_log(AV_LOG_WARN, "Attempted to unregister unknown resource: %p", ptr);
    return AV_ERROR_VM_NOT_FOUND;
}

/* 安全内存分配 */
void* av_safe_malloc(size_t size, const char* description) {
    void* ptr = malloc(size);
    if (!ptr) {
        av_log(AV_LOG_ERROR, "Memory allocation failed: %zu bytes for %s", size, description);
        return NULL;
    }

    memset(ptr, 0, size);

    if (av_resource_register(ptr, size, AV_RESOURCE_TYPE_MEMORY, description) != AV_SUCCESS) {
        av_log(AV_LOG_WARN, "Failed to register allocated memory");
    }

    return ptr;
}

/* 安全内存释放 */
void av_safe_free(void* ptr) {
    if (!ptr) {
        return;
    }

    av_resource_unregister(ptr);
    free(ptr);
}

/* 获取资源使用统计 */
int av_resource_get_stats(av_resource_stats_t* stats) {
    if (!stats) {
        return AV_ERROR_INVALID_PARAM;
    }

    if (!g_resource_manager.initialized) {
        memset(stats, 0, sizeof(av_resource_stats_t));
        return AV_SUCCESS;
    }

    pthread_mutex_lock(&g_resource_manager.mutex);

    stats->total_memory = g_resource_manager.total_memory;
    stats->peak_memory = g_resource_manager.peak_memory;
    stats->resource_count = g_resource_manager.resource_count;

    /* 统计各类型资源数量 */
    memset(stats->resource_counts, 0, sizeof(stats->resource_counts));
    
    av_resource_t* current = g_resource_manager.resources;
    while (current) {
        if (current->type >= 1 && current->type <= 6) {
            stats->resource_counts[current->type - 1]++;
        }
        current = current->next;
    }

    pthread_mutex_unlock(&g_resource_manager.mutex);

    return AV_SUCCESS;
}

/* 报告资源使用情况 */
void av_resource_report(void) {
    av_resource_stats_t stats;
    
    if (av_resource_get_stats(&stats) != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to get resource statistics");
        return;
    }

    av_log(AV_LOG_INFO, "=== Resource Usage Report ===");
    av_log(AV_LOG_INFO, "Current memory usage: %zu bytes", stats.total_memory);
    av_log(AV_LOG_INFO, "Peak memory usage: %zu bytes", stats.peak_memory);
    av_log(AV_LOG_INFO, "Total resources: %d", stats.resource_count);
    av_log(AV_LOG_INFO, "  Memory allocations: %d", stats.resource_counts[0]);
    av_log(AV_LOG_INFO, "  File handles: %d", stats.resource_counts[1]);
    av_log(AV_LOG_INFO, "  Shared memory regions: %d", stats.resource_counts[2]);
    av_log(AV_LOG_INFO, "  Threads: %d", stats.resource_counts[3]);
    av_log(AV_LOG_INFO, "  Mutexes: %d", stats.resource_counts[4]);
    av_log(AV_LOG_INFO, "  Xen handles: %d", stats.resource_counts[5]);

    /* 获取系统资源使用情况 */
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        av_log(AV_LOG_INFO, "System resource usage:");
        av_log(AV_LOG_INFO, "  Max RSS: %ld KB", usage.ru_maxrss);
        av_log(AV_LOG_INFO, "  User time: %ld.%06ld s", usage.ru_utime.tv_sec, usage.ru_utime.tv_usec);
        av_log(AV_LOG_INFO, "  System time: %ld.%06ld s", usage.ru_stime.tv_sec, usage.ru_stime.tv_usec);
        av_log(AV_LOG_INFO, "  Page faults: %ld", usage.ru_majflt);
        av_log(AV_LOG_INFO, "  Context switches: %ld voluntary, %ld involuntary", 
               usage.ru_nvcsw, usage.ru_nivcsw);
    }
}

/* 设置资源限制 */
int av_set_resource_limits(void) {
    struct rlimit limit;

    /* 设置核心转储文件大小限制 */
    limit.rlim_cur = 0;
    limit.rlim_max = 0;
    if (setrlimit(RLIMIT_CORE, &limit) != 0) {
        av_log(AV_LOG_WARN, "Failed to disable core dumps: %s", strerror(errno));
    }

    /* 设置文件描述符限制 */
    if (getrlimit(RLIMIT_NOFILE, &limit) == 0) {
        av_log(AV_LOG_INFO, "Current file descriptor limit: %lu (soft), %lu (hard)", 
               limit.rlim_cur, limit.rlim_max);
        
        /* 尝试增加软限制 */
        if (limit.rlim_cur < 65536 && limit.rlim_max >= 65536) {
            limit.rlim_cur = 65536;
            if (setrlimit(RLIMIT_NOFILE, &limit) == 0) {
                av_log(AV_LOG_INFO, "Increased file descriptor limit to %lu", limit.rlim_cur);
            }
        }
    }

    /* 设置进程数限制 */
    if (getrlimit(RLIMIT_NPROC, &limit) == 0) {
        av_log(AV_LOG_INFO, "Current process limit: %lu (soft), %lu (hard)", 
               limit.rlim_cur, limit.rlim_max);
    }

    /* 设置内存限制（可选） */
    if (getrlimit(RLIMIT_AS, &limit) == 0) {
        av_log(AV_LOG_INFO, "Current memory limit: %lu (soft), %lu (hard)", 
               limit.rlim_cur, limit.rlim_max);
    }

    return AV_SUCCESS;
}

/* 紧急清理函数 */
void av_emergency_cleanup(void) {
    av_log(AV_LOG_WARN, "Performing emergency cleanup...");

    /* 强制清理资源管理器 */
    if (g_resource_manager.initialized) {
        av_resource_manager_cleanup();
    }

    /* 同步文件系统 */
    sync();

    av_log(AV_LOG_WARN, "Emergency cleanup completed");
}

/* 信号处理增强 */
static void (*g_original_handlers[32])(int) = {0};

void av_enhanced_signal_handler(int sig) {
    static volatile sig_atomic_t in_handler = 0;
    
    /* 防止信号处理器重入 */
    if (in_handler) {
        return;
    }
    in_handler = 1;

    av_log(AV_LOG_WARN, "Received signal %d (%s)", sig, strsignal(sig));

    switch (sig) {
        case SIGINT:
        case SIGTERM:
            av_log(AV_LOG_INFO, "Graceful shutdown requested");
            /* 设置全局关闭标志 */
            extern volatile int g_shutdown_requested;
            g_shutdown_requested = 1;
            break;

        case SIGSEGV:
        case SIGBUS:
        case SIGFPE:
        case SIGILL:
            av_log(AV_LOG_ERROR, "Fatal signal received, performing emergency cleanup");
            av_emergency_cleanup();
            
            /* 恢复默认处理器并重新发送信号 */
            signal(sig, SIG_DFL);
            raise(sig);
            break;

        case SIGHUP:
            av_log(AV_LOG_INFO, "Configuration reload requested");
            /* 这里可以添加配置重载逻辑 */
            break;

        case SIGUSR1:
            av_log(AV_LOG_INFO, "Resource report requested");
            av_resource_report();
            break;

        case SIGUSR2:
            av_log(AV_LOG_INFO, "Debug information requested");
            /* 这里可以添加调试信息输出 */
            break;

        default:
            av_log(AV_LOG_WARN, "Unhandled signal: %d", sig);
            break;
    }

    in_handler = 0;
}

/* 安装信号处理器 */
int av_install_signal_handlers(void) {
    struct sigaction sa;
    
    memset(&sa, 0, sizeof(sa));
    sa.sa_handler = av_enhanced_signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = SA_RESTART;

    /* 安装各种信号处理器 */
    int signals[] = {SIGINT, SIGTERM, SIGHUP, SIGUSR1, SIGUSR2, SIGSEGV, SIGBUS, SIGFPE, SIGILL, 0};
    
    for (int i = 0; signals[i] != 0; i++) {
        int sig = signals[i];
        struct sigaction old_sa;
        
        if (sigaction(sig, &sa, &old_sa) != 0) {
            av_log(AV_LOG_ERROR, "Failed to install handler for signal %d: %s", sig, strerror(errno));
            return AV_ERROR_PROTOCOL;
        }
        
        if (sig < 32) {
            g_original_handlers[sig] = old_sa.sa_handler;
        }
        
        av_log(AV_LOG_DEBUG, "Installed signal handler for signal %d", sig);
    }

    /* 忽略SIGPIPE */
    signal(SIGPIPE, SIG_IGN);

    av_log(AV_LOG_INFO, "Signal handlers installed successfully");
    return AV_SUCCESS;
}

/* 恢复信号处理器 */
void av_restore_signal_handlers(void) {
    int signals[] = {SIGINT, SIGTERM, SIGHUP, SIGUSR1, SIGUSR2, SIGSEGV, SIGBUS, SIGFPE, SIGILL, 0};
    
    for (int i = 0; signals[i] != 0; i++) {
        int sig = signals[i];
        if (sig < 32 && g_original_handlers[sig]) {
            signal(sig, g_original_handlers[sig]);
        } else {
            signal(sig, SIG_DFL);
        }
    }

    av_log(AV_LOG_DEBUG, "Signal handlers restored");
}