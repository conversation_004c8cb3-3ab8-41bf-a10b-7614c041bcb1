# 安装指南

## 系统要求

### XenServer宿主机
- XenServer 7.0或更高版本
- libxenctrl开发库
- libxenstore开发库
- GCC编译器
- Make工具
- Root权限

### Linux虚拟机
- 支持的Linux发行版（Ubuntu, CentOS, RHEL等）
- libxenctrl开发库
- libxenstore开发库
- GCC编译器
- Make工具
- Root权限

### Windows虚拟机
- Windows Server 2012或更高版本
- Windows Xen PV驱动
- Visual Studio 2017或更高版本（或MinGW）
- CMake 3.10或更高版本
- 管理员权限

## 编译安装

### 1. 获取源代码

```bash
git clone <repository-url>
cd xenserver-antivirus
```

### 2. 检查依赖

```bash
make check-deps
```

### 3. 编译所有组件

```bash
# 编译宿主机服务和Linux客户端
make all

# 或者分别编译
make host
make linux-client

# Windows客户端需要在Windows环境下编译
make windows-client
```

### 4. 安装

```bash
# 安装宿主机服务和Linux客户端
sudo make install

# 或者分别安装
sudo make install-host
sudo make install-linux-client
```

## 详细安装步骤

### XenServer宿主机安装

1. **安装依赖包**
```bash
# CentOS/RHEL
yum install xen-devel gcc make

# Ubuntu/Debian
apt-get install libxen-dev gcc make
```

2. **编译和安装**
```bash
cd host
make
sudo make install
```

3. **验证安装**
```bash
av_host_service --version
```

### Linux虚拟机客户端安装

1. **安装依赖包**
```bash
# CentOS/RHEL
yum install xen-devel gcc make

# Ubuntu/Debian
apt-get install libxen-dev gcc make
```

2. **编译和安装**
```bash
cd linux-client
make
sudo make install
```

3. **验证安装**
```bash
av_linux_client --version
```

### Windows虚拟机客户端安装

1. **安装依赖**
   - 安装Visual Studio 2017或更高版本
   - 安装CMake 3.10或更高版本
   - 确保已安装Xen PV驱动

2. **编译**
```cmd
cd windows-client
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

3. **安装服务**
```cmd
# 以管理员身份运行
install_service.bat
```

4. **验证安装**
```cmd
sc query XenServerAntivirusClient
```

## 配置

### 宿主机服务配置

创建配置文件 `/etc/av_host_service.conf`：

```ini
[general]
log_level = info
max_vms = 100

[xenstore]
timeout = 5000
retry_count = 3

[memory]
shared_memory_size = 4096
```

### Linux客户端配置

创建配置文件 `/etc/av_linux_client.conf`：

```ini
[general]
log_level = info
scan_timeout = 30000

[xenstore]
monitor_interval = 1000
```

### Windows客户端配置

配置通过注册表进行：

```reg
[HKEY_LOCAL_MACHINE\SOFTWARE\XenServerAntivirus]
"LogLevel"=dword:00000002
"ScanTimeout"=dword:00007530
```

## 启动服务

### 宿主机服务

```bash
# 直接运行
sudo av_host_service

# 或者作为系统服务运行
sudo systemctl enable av-host-service
sudo systemctl start av-host-service
```

### Linux客户端

```bash
# 直接运行
sudo av_linux_client

# 或者作为系统服务运行
sudo systemctl enable av-linux-client
sudo systemctl start av-linux-client
```

### Windows客户端

```cmd
# 服务已自动启动，也可以手动控制
net start XenServerAntivirusClient
net stop XenServerAntivirusClient
```

## 故障排除

### 常见问题

1. **编译错误：找不到xen头文件**
   - 确保安装了xen-devel或libxen-dev包
   - 检查头文件路径是否正确

2. **运行时错误：权限不足**
   - 确保以root/管理员权限运行
   - 检查文件权限设置

3. **通信失败**
   - 检查XenStore服务是否正常运行
   - 验证虚拟机UUID是否正确
   - 查看日志文件获取详细错误信息

### 日志文件位置

- 宿主机服务：`/var/log/av_host_service.log`
- Linux客户端：`/var/log/av_linux_client.log`
- Windows客户端：Windows事件日志

### 卸载

```bash
# 卸载所有组件
sudo make uninstall

# 或者分别卸载
sudo make uninstall-host
sudo make uninstall-linux-client
```

Windows客户端卸载：
```cmd
# 以管理员身份运行
uninstall_service.bat
```