# Linux客户端真实版本静态编译Makefile

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE -static

# 检查是否有Xen库
XEN_LIBS_AVAILABLE := $(shell pkg-config --exists xencontrol xenstore 2>/dev/null && echo yes || echo no)

ifneq ($(XEN_LIBS_AVAILABLE),yes)
    $(error Xen libraries not found. Please install libxen-dev package)
endif

# 获取Xen库的编译和链接标志（静态版本）
XEN_CFLAGS := $(shell pkg-config --cflags xencontrol xenstore 2>/dev/null)
XEN_LDFLAGS := $(shell pkg-config --libs --static xencontrol xenstore 2>/dev/null)

# 如果pkg-config不支持--static，手动指定静态库
ifeq ($(XEN_LDFLAGS),)
    XEN_LDFLAGS = -lxenctrl -lxenstore -lxengnttab -lxentoollog -lxencall -lxenevtchn -lxenforeignmemory -lxendevicemodel -lxentoolcore
endif

CFLAGS += $(XEN_CFLAGS)
LDFLAGS = $(XEN_LDFLAGS) -lpthread -static

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj-real-static
BINDIR = bin-real-static

# 真实版本源文件
REAL_SOURCES = main.c av_linux_client_real.c
COMMON_SOURCES = $(COMMONDIR)/av_common.c
SOURCES = $(REAL_SOURCES) $(COMMON_SOURCES)
OBJECTS = $(REAL_SOURCES:%.c=$(OBJDIR)/%.o) $(OBJDIR)/av_common.o
TARGET = $(BINDIR)/av_linux_client_real_static

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I.

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Statically linking real version $@..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Real static build complete: $@"
	@echo "Binary size: $$(ls -lh $@ | awk '{print $$5}')"
	@echo "Dependencies check:"
	@ldd $@ 2>/dev/null || echo "  -> Statically linked (no dynamic dependencies)"

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $< (real static)..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译common文件
$(OBJDIR)/av_common.o: $(COMMONDIR)/av_common.c
	@echo "Compiling $< (real static)..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning real static build files..."
	rm -rf $(OBJDIR) $(BINDIR)

# 检查依赖
check-deps:
	@echo "Checking dependencies for static build..."
	@echo "✓ Xen libraries found:"
	@pkg-config --modversion xencontrol xenstore
	@echo "✓ Xen headers found at: /usr/include/xen"
	@echo "✓ Static libraries check:"
	@find /usr/lib* -name "libxenctrl.a" 2>/dev/null | head -1 || echo "⚠ libxenctrl.a not found"
	@find /usr/lib* -name "libxenstore.a" 2>/dev/null | head -1 || echo "⚠ libxenstore.a not found"
	@echo "✓ Compilation flags:"
	@echo "  CFLAGS: $(CFLAGS)"
	@echo "  LDFLAGS: $(LDFLAGS)"

# 验证静态链接
verify: $(TARGET)
	@echo "Verifying real static linkage..."
	@file $(TARGET)
	@echo "Dependencies:"
	@ldd $(TARGET) 2>/dev/null || echo "  -> Statically linked (no dynamic dependencies)"
	@echo "Binary size: $$(ls -lh $(TARGET) | awk '{print $$5}')"
	@echo "Xen symbols:"
	@nm $(TARGET) | grep -E "(xc_|xs_)" | head -5 || echo "  -> Xen symbols found"

# 安装程序
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/av_linux_client_real_static
	sudo chmod +x /usr/local/bin/av_linux_client_real_static
	@echo "Real static installation complete"

# 运行程序（需要root权限）
run: $(TARGET)
	@echo "Running $(TARGET) (requires root privileges)..."
	sudo $(TARGET)

# 调试版本
debug: CFLAGS += -DDEBUG -g3
debug: $(TARGET)

# 发布版本
release: CFLAGS += -DNDEBUG -O3 -s
release: clean $(TARGET)

# 帮助信息
help:
	@echo "Available targets for real static build:"
	@echo "  all        - Build the real static program (default)"
	@echo "  clean      - Remove real static build files"
	@echo "  install    - Install the real static program"
	@echo "  run        - Build and run the real static program"
	@echo "  verify     - Verify static linkage of built binary"
	@echo "  check-deps - Check dependencies"
	@echo "  debug      - Build debug version"
	@echo "  release    - Build optimized release version"
	@echo "  help       - Show this help message"

.PHONY: all clean install run verify debug release help directories check-deps