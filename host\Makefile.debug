# Makefile for Xen Debug Tool

CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE
LIBS = -lxenctrl -lxenstore -lxengnttab -lxencall -lxentoollog -lxenforeignmemory -lxendevicemodel -lxentoolcore

# 目标
TARGET = xen_debug_tool
TARGET_STATIC = xen_debug_tool_static

# 默认目标
all: $(TARGET) $(TARGET_STATIC)

# 动态链接版本
$(TARGET): xen_debug_tool.c
	@echo "编译Xen诊断工具 (动态链接)..."
	$(CC) $(CFLAGS) -o $@ $< -lxenctrl -lxenstore -lxengnttab -lpthread

# 静态链接版本
$(TARGET_STATIC): xen_debug_tool.c
	@echo "编译Xen诊断工具 (静态链接)..."
	$(CC) $(CFLAGS) -static -o $@ $< $(LIBS) -lpthread

# 清理
clean:
	rm -f $(TARGET) $(TARGET_STATIC)

# 测试
test: $(TARGET_STATIC)
	@echo "运行Xen环境诊断..."
	./$(TARGET_STATIC)

.PHONY: all clean test
