# XenServer宿主机防病毒服务Makefile - 简化版本（用于开发测试）

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE
LDFLAGS = -lpthread

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj_simple
BINDIR = bin

# 源文件和目标文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
HOST_SOURCES = main_real.c xen_mock_impl.c
SOURCES = $(COMMON_SOURCES) $(HOST_SOURCES)
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/xenserver-antivirus-host-simple

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I.

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Linking $@..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Build complete: $@"
	@echo "Built with mock Xen implementation for testing"

# 编译源文件 - 使用简化头文件
$(OBJDIR)/main_real.o: main_real.c
	@echo "Compiling $< (with mock headers)..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -DUSE_MOCK_XEN -include av_host_simple.h -c $< -o $@

$(OBJDIR)/%.o: %.c
	@echo "Compiling $<..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning build files..."
	rm -rf $(OBJDIR) $(BINDIR)/xenserver-antivirus-host-simple

# 运行程序
run: $(TARGET)
	@echo "Running $(TARGET)..."
	$(TARGET)

# 测试运行
test: $(TARGET)
	@echo "Testing $(TARGET)..."
	$(TARGET) --help
	@echo "Test completed"

.PHONY: all clean directories run test