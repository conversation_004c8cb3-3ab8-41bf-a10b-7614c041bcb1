#include "av_linux_client.h"
#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <getopt.h>
#include <time.h>

/* 全局客户端实例 */
static av_linux_client_t g_client;
static volatile int g_shutdown_requested = 0;

/* 信号处理函数 */
void signal_handler(int signum) {
    static volatile sig_atomic_t signal_count = 0;
    
    switch (signum) {
        case SIGINT:
        case SIGTERM:
            signal_count++;
            av_log(AV_LOG_INFO, "Received shutdown signal %d (%s) - count: %d", 
                   signum, (signum == SIGINT) ? "SIGINT" : "SIGTERM", (int)signal_count);
            
            g_shutdown_requested = 1;
            g_client.shutdown_requested = 1;
            
            /* 如果多次收到信号，强制退出 */
            if (signal_count >= 3) {
                av_log(AV_LOG_WARN, "Received %d shutdown signals, forcing exit", (int)signal_count);
                _exit(1);
            }
            break;
            
        default:
            av_log(AV_LOG_WARN, "Received unexpected signal: %d", signum);
            break;
    }
}

/* 打印使用帮助 */
void print_usage(const char* program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("XenServer Antivirus Linux Client\n\n");
    printf("Options:\n");
    printf("  -h, --help           Show this help message\n");
    printf("  -v, --verbose        Enable verbose logging\n");
    printf("  -d, --daemon         Run as daemon process\n");
    printf("  -l, --log-level LEVEL Set log level (0=ERROR, 1=WARNING, 2=INFO, 3=DEBUG)\n");
    printf("  -f, --log-file FILE  Write logs to file instead of stdout\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s                   Run in foreground with default settings\n", program_name);
    printf("  %s -v -l 3           Run with verbose debug logging\n", program_name);
    printf("  %s -d -f /var/log/av_client.log  Run as daemon with file logging\n", program_name);
}

/* 守护进程化 */
int daemonize() {
    pid_t pid = fork();
    
    if (pid < 0) {
        av_log(AV_LOG_ERROR, "Failed to fork daemon process");
        return AV_ERROR_FORK;
    }
    
    if (pid > 0) {
        /* 父进程退出 */
        exit(0);
    }
    
    /* 子进程继续 */
    if (setsid() < 0) {
        av_log(AV_LOG_ERROR, "Failed to create new session");
        return AV_ERROR_SETSID;
    }
    
    /* 改变工作目录到根目录 */
    if (chdir("/") < 0) {
        av_log(AV_LOG_ERROR, "Failed to change working directory");
        return AV_ERROR_CHDIR;
    }
    
    /* 关闭标准文件描述符 */
    close(STDIN_FILENO);
    close(STDOUT_FILENO);
    close(STDERR_FILENO);
    
    av_log(AV_LOG_INFO, "Successfully daemonized");
    return AV_SUCCESS;
}

/* 主函数 */
int main(int argc, char* argv[]) {
    int ret = AV_SUCCESS;
    int daemon_mode = 0;
    int log_level = AV_LOG_INFO;
    char* log_file = NULL;
    
    /* 命令行参数解析 */
    static struct option long_options[] = {
        {"help",      no_argument,       0, 'h'},
        {"verbose",   no_argument,       0, 'v'},
        {"daemon",    no_argument,       0, 'd'},
        {"log-level", required_argument, 0, 'l'},
        {"log-file",  required_argument, 0, 'f'},
        {0, 0, 0, 0}
    };
    
    int option_index = 0;
    int c;
    
    while ((c = getopt_long(argc, argv, "hvdl:f:", long_options, &option_index)) != -1) {
        switch (c) {
            case 'h':
                print_usage(argv[0]);
                return 0;
            case 'v':
                log_level = AV_LOG_DEBUG;
                break;
            case 'd':
                daemon_mode = 1;
                break;
            case 'l':
                log_level = atoi(optarg);
                if (log_level < AV_LOG_ERROR || log_level > AV_LOG_DEBUG) {
                    fprintf(stderr, "Invalid log level: %s\n", optarg);
                    return 1;
                }
                break;
            case 'f':
                log_file = optarg;
                break;
            case '?':
                print_usage(argv[0]);
                return 1;
            default:
                break;
        }
    }
    
    /* 初始化日志系统 */
    av_log_init(log_level, log_file);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Linux Client starting...");
    av_log(AV_LOG_INFO, "Version: %s, Build: %s %s", AV_VERSION, __DATE__, __TIME__);
    
    /* 检查运行权限 */
    if (geteuid() != 0) {
        av_log(AV_LOG_ERROR, "This program must be run as root");
        return 1;
    }
    
    /* 守护进程化 */
    if (daemon_mode) {
        ret = daemonize();
        if (ret != AV_SUCCESS) {
            av_log(AV_LOG_ERROR, "Failed to daemonize process");
            return 1;
        }
    }
    
    /* 设置信号处理 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGPIPE, SIG_IGN);  /* 忽略SIGPIPE信号 */
    
    /* 初始化客户端 */
    ret = av_linux_client_init(&g_client);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to initialize Linux client");
        return 1;
    }
    
    /* 启动客户端 */
    ret = av_linux_client_start(&g_client);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to start Linux client");
        av_linux_client_cleanup(&g_client);
        return 1;
    }
    
    av_log(AV_LOG_INFO, "Linux client started successfully, entering main loop");
    
    /* 运行通信流程 */
    ret = av_linux_client_run_communication(&g_client);
    if (ret != AV_SUCCESS && !g_shutdown_requested) {
        av_log(AV_LOG_ERROR, "Communication flow failed");
    }
    
    /* 清理资源 */
    av_log(AV_LOG_INFO, "Shutting down Linux client...");
    av_linux_client_cleanup(&g_client);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Linux Client shutdown complete");
    av_log_cleanup();
    
    return (ret == AV_SUCCESS) ? 0 : 1;
}