#!/bin/bash
# 检查Xen环境脚本

echo "=== XenServer防病毒客户端环境检查 ==="
echo

# 检查是否在Xen环境中
echo "1. 检查Xen环境..."
if [ -d /proc/xen ]; then
    echo "✓ /proc/xen 存在 - 运行在Xen环境中"
    
    # 显示Xen信息
    if [ -f /proc/xen/capabilities ]; then
        echo "  Xen capabilities: $(cat /proc/xen/capabilities)"
    fi
    
    if [ -f /proc/xen/version ]; then
        echo "  Xen version: $(cat /proc/xen/version)"
    fi
else
    echo "✗ /proc/xen 不存在 - 可能不在Xen环境中"
fi

# 检查Xen设备
echo
echo "2. 检查Xen设备..."
if [ -d /dev/xen ]; then
    echo "✓ /dev/xen 存在"
    ls -la /dev/xen/
else
    echo "⚠ /dev/xen 不存在 - 可能是PV guest"
fi

# 检查XenStore
echo
echo "3. 检查XenStore..."
if command -v xenstore-read >/dev/null 2>&1; then
    echo "✓ xenstore-read 命令可用"
    
    # 尝试读取domain ID
    if domid=$(xenstore-read domid 2>/dev/null); then
        echo "  Domain ID: $domid"
    else
        echo "  无法读取domain ID (需要权限)"
    fi
else
    echo "✗ xenstore-read 命令不可用"
    echo "  尝试安装: apt install xenstore-utils"
fi

# 检查Xen库
echo
echo "4. 检查Xen库..."
if ldconfig -p | grep -q libxenctrl; then
    echo "✓ libxenctrl 库可用"
else
    echo "✗ libxenctrl 库不可用"
    echo "  尝试安装: apt install libxen-dev"
fi

if ldconfig -p | grep -q libxenstore; then
    echo "✓ libxenstore 库可用"
else
    echo "✗ libxenstore 库不可用"
    echo "  尝试安装: apt install libxen-dev"
fi

# 检查权限
echo
echo "5. 检查权限..."
if [ "$EUID" -eq 0 ]; then
    echo "✓ 以root权限运行"
else
    echo "⚠ 未以root权限运行 - 客户端需要root权限"
fi

# 检查网络
echo
echo "6. 检查网络连接..."
if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
    echo "✓ 网络连接正常"
else
    echo "⚠ 网络连接可能有问题"
fi

# 检查系统信息
echo
echo "7. 系统信息..."
echo "  OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "  Kernel: $(uname -r)"
echo "  Architecture: $(uname -m)"

echo
echo "=== 检查完成 ==="