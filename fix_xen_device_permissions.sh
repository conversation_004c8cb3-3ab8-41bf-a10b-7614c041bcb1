#!/bin/bash

# XenServer防病毒系统 - Xen设备权限专项修复
# 解决 xc_domain_getinfolist Permission denied 问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "=================================================================="
echo "XenServer防病毒系统 - Xen设备权限专项修复"
echo "=================================================================="

# 检查root权限
if [ $(id -u) -ne 0 ]; then
    log_error "必须以root权限运行"
    exit 1
fi

log_info "开始修复Xen设备权限..."

# 1. 检查当前设备权限
log_info "当前/dev/xen/设备权限:"
ls -la /dev/xen/
echo ""

# 2. 修复关键设备权限
log_info "修复关键设备权限..."

# 修复privcmd权限（已经是666，但确保正确）
if [ -e "/dev/xen/privcmd" ]; then
    chmod 666 /dev/xen/privcmd
    log_success "✅ privcmd权限已设置为666"
else
    log_error "❌ /dev/xen/privcmd不存在"
fi

# 修复xenbus权限
if [ -e "/dev/xen/xenbus" ]; then
    chmod 666 /dev/xen/xenbus
    log_success "✅ xenbus权限已修复为666"
else
    log_warning "⚠️  /dev/xen/xenbus不存在"
fi

# 修复xenbus_backend权限
if [ -e "/dev/xen/xenbus_backend" ]; then
    chmod 666 /dev/xen/xenbus_backend
    log_success "✅ xenbus_backend权限已修复为666"
else
    log_warning "⚠️  /dev/xen/xenbus_backend不存在"
fi

# 检查并创建xenstore设备（如果不存在）
if [ ! -e "/dev/xen/xenstore" ]; then
    log_warning "⚠️  /dev/xen/xenstore不存在，尝试创建..."
    
    # 在Citrix Hypervisor中，xenstore可能通过其他方式访问
    # 尝试创建符号链接到实际的xenstore接口
    if [ -e "/proc/xen/xenstore" ]; then
        ln -sf /proc/xen/xenstore /dev/xen/xenstore 2>/dev/null && {
            log_success "✅ 创建xenstore符号链接成功"
        } || {
            log_warning "⚠️  创建xenstore符号链接失败"
        }
    fi
fi

echo ""

# 3. 检查修复后的权限
log_info "修复后的设备权限:"
ls -la /dev/xen/
echo ""

# 4. 设置正确的环境变量
log_info "设置环境变量..."
export XEN_DOMAIN_INTERFACE=1
export XENSTORE_DOMAIN_INTERFACE=1
log_success "✅ 环境变量已设置"
echo ""

# 5. 测试libxenctrl访问
log_info "测试libxenctrl访问..."

# 创建简单的测试程序
cat > /tmp/test_domain_list.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <string.h>
#include <xenctrl.h>

int main() {
    printf("Testing xc_domain_getinfolist...\n");
    
    xc_interface *xc_handle = xc_interface_open(NULL, NULL, 0);
    if (xc_handle == NULL) {
        printf("ERROR: Cannot open xc interface: %s\n", strerror(errno));
        return 1;
    }
    
    printf("XC interface opened successfully\n");
    
    xc_domaininfo_t info[10];
    int ret = xc_domain_getinfolist(xc_handle, 0, 10, info);
    if (ret < 0) {
        printf("ERROR: xc_domain_getinfolist failed: %s (errno: %d)\n", strerror(errno), errno);
        xc_interface_close(xc_handle);
        return 1;
    }
    
    printf("SUCCESS: Found %d domains\n", ret);
    for (int i = 0; i < ret; i++) {
        printf("  Domain %d: ID=%d, flags=0x%x\n", i, info[i].domid, info[i].flags);
    }
    
    xc_interface_close(xc_handle);
    return 0;
}
EOF

# 编译并运行测试
if gcc -o /tmp/test_domain_list /tmp/test_domain_list.c -lxenctrl 2>/dev/null; then
    log_info "运行libxenctrl测试..."
    if /tmp/test_domain_list; then
        log_success "✅ libxenctrl访问测试通过"
    else
        log_error "❌ libxenctrl访问测试失败"
        echo "这可能是权限问题的根源"
    fi
    rm -f /tmp/test_domain_list
else
    log_warning "⚠️  无法编译测试程序"
fi
rm -f /tmp/test_domain_list.c

echo ""

# 6. 检查SELinux状态
log_info "检查SELinux状态..."
if command -v getenforce >/dev/null 2>&1; then
    SELINUX_STATUS=$(getenforce 2>/dev/null || echo "Unknown")
    echo "SELinux状态: $SELINUX_STATUS"
    
    if [ "$SELINUX_STATUS" = "Enforcing" ]; then
        log_warning "⚠️  SELinux处于强制模式，可能阻止访问"
        log_info "临时禁用SELinux进行测试..."
        setenforce 0 2>/dev/null && {
            log_success "✅ SELinux已临时禁用"
        } || {
            log_warning "⚠️  无法禁用SELinux"
        }
    fi
else
    log_info "SELinux未安装或不可用"
fi

echo ""

# 7. 尝试使用不同的权限运行程序
PROGRAM_PATH="/usr/local/bin/xenserver-antivirus-glibc217-static"
if [ -f "$PROGRAM_PATH" ]; then
    log_info "使用修复后的权限测试程序..."
    
    # 设置环境变量并运行
    export XEN_DOMAIN_INTERFACE=1
    export XENSTORE_DOMAIN_INTERFACE=1
    
    echo "运行程序测试（10秒超时）..."
    timeout 10s "$PROGRAM_PATH" --test 2>&1 | head -20
    
    echo ""
    log_info "如果上述测试成功，现在可以正常运行程序:"
    echo "sudo XEN_DOMAIN_INTERFACE=1 XENSTORE_DOMAIN_INTERFACE=1 $PROGRAM_PATH -v"
else
    log_warning "⚠️  程序未找到: $PROGRAM_PATH"
fi

echo ""

# 8. 创建持久化权限脚本
log_info "创建权限持久化脚本..."
cat > /usr/local/bin/fix-xen-permissions.sh << 'EOF'
#!/bin/bash
# Xen设备权限持久化脚本
# 在系统启动时自动修复权限

chmod 666 /dev/xen/privcmd 2>/dev/null || true
chmod 666 /dev/xen/xenbus 2>/dev/null || true  
chmod 666 /dev/xen/xenbus_backend 2>/dev/null || true

# 设置环境变量
export XEN_DOMAIN_INTERFACE=1
export XENSTORE_DOMAIN_INTERFACE=1
EOF

chmod +x /usr/local/bin/fix-xen-permissions.sh
log_success "✅ 权限持久化脚本已创建: /usr/local/bin/fix-xen-permissions.sh"

# 添加到系统启动
if [ -d "/etc/rc.d/rc.local" ] || [ -f "/etc/rc.local" ]; then
    echo "/usr/local/bin/fix-xen-permissions.sh" >> /etc/rc.local 2>/dev/null || true
    chmod +x /etc/rc.local 2>/dev/null || true
    log_success "✅ 已添加到系统启动脚本"
fi

echo ""
echo "=================================================================="
echo "权限修复完成！"
echo "=================================================================="

echo ""
log_info "修复摘要:"
echo "- privcmd权限: $(ls -l /dev/xen/privcmd 2>/dev/null | awk '{print $1}' || echo '不存在')"
echo "- xenbus权限: $(ls -l /dev/xen/xenbus 2>/dev/null | awk '{print $1}' || echo '不存在')"
echo "- xenbus_backend权限: $(ls -l /dev/xen/xenbus_backend 2>/dev/null | awk '{print $1}' || echo '不存在')"
echo "- SELinux状态: $(getenforce 2>/dev/null || echo '不可用')"

echo ""
log_info "现在请尝试运行程序:"
echo "sudo XEN_DOMAIN_INTERFACE=1 XENSTORE_DOMAIN_INTERFACE=1 $PROGRAM_PATH -v"

echo ""
log_info "如果问题仍然存在，可能需要:"
echo "1. 重启系统以重新加载设备权限"
echo "2. 检查Citrix Hypervisor的特殊权限要求"
echo "3. 联系Citrix技术支持"
