#ifdef SIMPLE_BUILD
#include "av_host_simple.h"
#else
#include "av_host.h"
#endif
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <signal.h>
#include <unistd.h>
#include <getopt.h>
#include <sys/stat.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/file.h>
#include <time.h>

/* 前向声明 */
extern char *program_invocation_name;

/* 配置结构 */
typedef struct {
    char config_file[256];
    char pid_file[256];
    char log_file[256];
    av_log_level_t log_level;
    int daemon_mode;
    int test_mode;
    char target_uuid[AV_UUID_STRING_LENGTH];
    int monitor_interval_ms;
    int health_check_interval_s;
    int stats_report_interval_s;
} av_config_t;

/* 全局变量 */
static av_host_service_t g_service;
static av_config_t g_config;
static volatile int g_shutdown_requested = 0;
static int g_pid_fd = -1;

/* 初始化默认配置 */
void init_default_config(av_config_t* config) {
    memset(config, 0, sizeof(av_config_t));
    
    strncpy(config->config_file, "/etc/xenserver-antivirus/host.conf", sizeof(config->config_file) - 1);
    strncpy(config->pid_file, "/var/run/xenserver-antivirus-host.pid", sizeof(config->pid_file) - 1);
    strncpy(config->log_file, "/var/log/xenserver-antivirus-host.log", sizeof(config->log_file) - 1);
    
    config->log_level = AV_LOG_INFO;
    config->daemon_mode = 0;
    config->test_mode = 0;
    config->monitor_interval_ms = 10;
    config->health_check_interval_s = 30;
    config->stats_report_interval_s = 300;
}

/* 读取配置文件 */
int read_config_file(av_config_t* config) {
    FILE* file = fopen(config->config_file, "r");
    if (!file) {
        if (errno == ENOENT) {
            av_log(AV_LOG_INFO, "Config file not found, using defaults: %s", config->config_file);
            return AV_SUCCESS;
        } else {
            av_log(AV_LOG_ERROR, "Failed to open config file %s: %s", config->config_file, strerror(errno));
            return AV_ERROR_PROTOCOL;
        }
    }
    
    av_log(AV_LOG_INFO, "Reading configuration from: %s", config->config_file);
    
    char line[512];
    int line_num = 0;
    
    while (fgets(line, sizeof(line), file)) {
        line_num++;
        
        /* 跳过注释和空行 */
        char* trimmed = line;
        while (*trimmed == ' ' || *trimmed == '\t') trimmed++;
        if (*trimmed == '#' || *trimmed == '\n' || *trimmed == '\0') {
            continue;
        }
        
        /* 移除行尾换行符 */
        char* newline = strchr(trimmed, '\n');
        if (newline) *newline = '\0';
        
        /* 解析键值对 */
        char* equals = strchr(trimmed, '=');
        if (!equals) {
            av_log(AV_LOG_WARN, "Invalid config line %d: %s", line_num, trimmed);
            continue;
        }
        
        *equals = '\0';
        char* key = trimmed;
        char* value = equals + 1;
        
        /* 移除键值两端的空格 */
        while (*key && (key[strlen(key)-1] == ' ' || key[strlen(key)-1] == '\t')) {
            key[strlen(key)-1] = '\0';
        }
        while (*value == ' ' || *value == '\t') value++;
        
        /* 处理配置项 */
        if (strcmp(key, "log_level") == 0) {
            if (strcmp(value, "error") == 0) {
                config->log_level = AV_LOG_ERROR;
            } else if (strcmp(value, "warn") == 0) {
                config->log_level = AV_LOG_WARN;
            } else if (strcmp(value, "info") == 0) {
                config->log_level = AV_LOG_INFO;
            } else if (strcmp(value, "debug") == 0) {
                config->log_level = AV_LOG_DEBUG;
            }
        } else if (strcmp(key, "pid_file") == 0) {
            strncpy(config->pid_file, value, sizeof(config->pid_file) - 1);
        } else if (strcmp(key, "log_file") == 0) {
            strncpy(config->log_file, value, sizeof(config->log_file) - 1);
        } else if (strcmp(key, "target_uuid") == 0) {
            strncpy(config->target_uuid, value, sizeof(config->target_uuid) - 1);
        } else if (strcmp(key, "monitor_interval_ms") == 0) {
            config->monitor_interval_ms = atoi(value);
        } else if (strcmp(key, "health_check_interval_s") == 0) {
            config->health_check_interval_s = atoi(value);
        } else if (strcmp(key, "stats_report_interval_s") == 0) {
            config->stats_report_interval_s = atoi(value);
        } else {
            av_log(AV_LOG_WARN, "Unknown config key: %s", key);
        }
    }
    
    fclose(file);
    av_log(AV_LOG_INFO, "Configuration loaded successfully");
    return AV_SUCCESS;
}

/* 创建PID文件 */
int create_pid_file(const char* pid_file) {
    g_pid_fd = open(pid_file, O_RDWR | O_CREAT, 0644);
    if (g_pid_fd == -1) {
        av_log(AV_LOG_ERROR, "Failed to create PID file %s: %s", pid_file, strerror(errno));
        return AV_ERROR_PROTOCOL;
    }
    
    if (flock(g_pid_fd, LOCK_EX | LOCK_NB) == -1) {
        if (errno == EWOULDBLOCK) {
            av_log(AV_LOG_ERROR, "Another instance is already running (PID file locked)");
        } else {
            av_log(AV_LOG_ERROR, "Failed to lock PID file: %s", strerror(errno));
        }
        close(g_pid_fd);
        g_pid_fd = -1;
        return AV_ERROR_ALREADY_EXISTS;
    }
    
    /* 写入当前进程ID */
    char pid_str[32];
    snprintf(pid_str, sizeof(pid_str), "%d\n", getpid());
    
    if (ftruncate(g_pid_fd, 0) == -1 || 
        write(g_pid_fd, pid_str, strlen(pid_str)) == -1) {
        av_log(AV_LOG_ERROR, "Failed to write PID to file: %s", strerror(errno));
        close(g_pid_fd);
        g_pid_fd = -1;
        return AV_ERROR_PROTOCOL;
    }
    
    av_log(AV_LOG_INFO, "PID file created: %s", pid_file);
    return AV_SUCCESS;
}

/* 清理PID文件 */
void cleanup_pid_file(const char* pid_file) {
    if (g_pid_fd != -1) {
        flock(g_pid_fd, LOCK_UN);
        close(g_pid_fd);
        g_pid_fd = -1;
        unlink(pid_file);
        av_log(AV_LOG_DEBUG, "PID file removed: %s", pid_file);
    }
}

/* 守护进程化 */
int daemonize(void) {
    pid_t pid = fork();
    
    if (pid < 0) {
        av_log(AV_LOG_ERROR, "Failed to fork: %s", strerror(errno));
        return AV_ERROR_PROTOCOL;
    }
    
    if (pid > 0) {
        /* 父进程退出 */
        exit(0);
    }
    
    /* 子进程继续 */
    if (setsid() < 0) {
        av_log(AV_LOG_ERROR, "Failed to create new session: %s", strerror(errno));
        return AV_ERROR_PROTOCOL;
    }
    
    /* 第二次fork */
    pid = fork();
    if (pid < 0) {
        av_log(AV_LOG_ERROR, "Failed to fork (second): %s", strerror(errno));
        return AV_ERROR_PROTOCOL;
    }
    
    if (pid > 0) {
        exit(0);
    }
    
    /* 改变工作目录 */
    if (chdir("/") < 0) {
        av_log(AV_LOG_ERROR, "Failed to change directory: %s", strerror(errno));
        return AV_ERROR_PROTOCOL;
    }
    
    /* 设置文件权限掩码 */
    umask(0);
    
    /* 关闭标准文件描述符 */
    close(STDIN_FILENO);
    close(STDOUT_FILENO);
    close(STDERR_FILENO);
    
    /* 重定向到/dev/null */
    open("/dev/null", O_RDONLY);
    open("/dev/null", O_WRONLY);
    open("/dev/null", O_WRONLY);
    
    av_log(AV_LOG_INFO, "Successfully daemonized");
    return AV_SUCCESS;
}

/* 信号处理函数 */
void signal_handler(int sig) {
    static volatile sig_atomic_t signal_count = 0;
    
    switch (sig) {
        case SIGINT:
        case SIGTERM:
            signal_count++;
            av_log(AV_LOG_INFO, "Received shutdown signal %d (%s) - count: %d", 
                   sig, (sig == SIGINT) ? "SIGINT" : "SIGTERM", (int)signal_count);
            
            g_shutdown_requested = 1;
            g_service.shutdown_requested = 1;
            
            /* 如果多次收到信号，强制退出 */
            if (signal_count >= 3) {
                av_log(AV_LOG_WARN, "Received %d shutdown signals, forcing exit", (int)signal_count);
                av_emergency_cleanup();
                _exit(1);
            }
            break;
            
        case SIGHUP:
            av_log(AV_LOG_INFO, "Received SIGHUP, reloading configuration");
            read_config_file(&g_config);
            break;
            
        default:
            av_log(AV_LOG_WARN, "Received unexpected signal: %d", sig);
            break;
    }
}

/* 显示帮助信息 */
void show_help(const char* program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("\n");
    printf("XenServer Agentless Antivirus Host Service\n");
    printf("\n");
    printf("Options:\n");
    printf("  -h, --help           Show this help message\n");
    printf("  -v, --version        Show version information\n");
    printf("  -d, --daemon         Run as daemon\n");
    printf("  -c, --config FILE    Configuration file path\n");
    printf("  -l, --log-level LEVEL Set log level (error, warn, info, debug)\n");
    printf("  -u, --uuid UUID      Target specific VM UUID\n");
    printf("  -p, --pid-file FILE  PID file path\n");
    printf("  --list-vms           List all running VMs and exit\n");
    printf("  --test-mode          Run in test mode (no actual operations)\n");
    printf("  --test-shm           Test shared memory functionality and exit\n");
    printf("  --test-xenstore      Test XenStore functionality and exit\n");
    printf("  --check-config       Check configuration file and exit\n");
    printf("  --status             Show service status and exit\n");
    printf("\n");
    printf("Configuration:\n");
    printf("  Default config file: /etc/xenserver-antivirus/host.conf\n");
    printf("  Default PID file:    /var/run/xenserver-antivirus-host.pid\n");
    printf("  Default log file:    /var/log/xenserver-antivirus-host.log\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s --list-vms\n", program_name);
    printf("  %s -u 12345678-1234-1234-1234-123456789abc\n", program_name);
    printf("  %s --daemon --config /etc/av-host.conf\n", program_name);
    printf("  %s --check-config\n", program_name);
    printf("  %s --status\n", program_name);
    printf("\n");
    printf("Signals:\n");
    printf("  SIGTERM, SIGINT      Graceful shutdown\n");
    printf("  SIGHUP               Reload configuration\n");
    printf("\n");
}

/* 显示版本信息 */
void show_version(void) {
    printf("XenServer Agentless Antivirus Host Service\n");
    printf("Version: %d.%d.%d\n", AV_VERSION_MAJOR, AV_VERSION_MINOR, AV_VERSION_PATCH);
    printf("Built: %s %s\n", __DATE__, __TIME__);
}

/* 列出所有运行的虚拟机 */
int list_running_vms(av_host_service_t* service) {
    av_log(AV_LOG_INFO, "Listing all running virtual machines...");
    
    domid_t* domids = NULL;
    int count = 0;
    
    int ret = av_list_running_vms(service, &domids, &count);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to list running VMs: %s", av_error_string(ret));
        return ret;
    }
    
    printf("\nRunning Virtual Machines:\n");
    printf("========================\n");
    
    if (count == 0) {
        printf("No running VMs found.\n");
    } else {
        for (int i = 0; i < count; i++) {
            domid_t domid = domids[i];
            
            /* 获取域信息 */
            xc_domaininfo_t domain_info;
            ret = av_get_domain_info(service, domid, &domain_info);
            if (ret == AV_SUCCESS) {
                printf("Domain ID: %u\n", domid);
                printf("  Memory: %lu KB\n", domain_info.tot_pages * 4);
                printf("  VCPUs: %u\n", domain_info.max_vcpu_id + 1);
                printf("  Flags: 0x%x\n", domain_info.flags);
                
                /* 尝试获取UUID */
                char xenstore_path[256];
                char vm_path[256];
                char uuid[AV_UUID_STRING_LENGTH];
                
                snprintf(xenstore_path, sizeof(xenstore_path), "/local/domain/%u/vm", domid);
                if (av_xenstore_read(service, xenstore_path, vm_path, sizeof(vm_path)) == AV_SUCCESS) {
                    char uuid_path[512];
                    snprintf(uuid_path, sizeof(uuid_path), "%s/uuid", vm_path);
                    if (av_xenstore_read(service, uuid_path, uuid, sizeof(uuid)) == AV_SUCCESS) {
                        printf("  UUID: %s\n", uuid);
                    }
                }
                
                printf("\n");
            }
        }
    }
    
    free(domids);
    return AV_SUCCESS;
}

/* 测试特定VM的连接 */
int test_vm_connection(av_host_service_t* service, const char* vm_uuid) {
    av_log(AV_LOG_INFO, "Testing connection to VM: %s", vm_uuid);
    
    /* 查找VM */
    domid_t domid;
    int ret = av_find_vm_by_uuid(service, vm_uuid, &domid);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to find VM %s: %s", vm_uuid, av_error_string(ret));
        return ret;
    }
    
    av_log(AV_LOG_INFO, "Found VM %s with domain ID %u", vm_uuid, domid);
    
    /* 验证域状态 */
    ret = av_verify_domain_state(service, domid);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Domain state verification failed: %s", av_error_string(ret));
        return ret;
    }
    
    av_log(AV_LOG_INFO, "Domain state verification successful");
    
    /* 检查PV驱动 */
    ret = av_check_pv_drivers(service, domid);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_WARN, "PV driver check failed: %s", av_error_string(ret));
        /* 不返回错误，继续执行 */
    }
    
    /* 初始化XenStore结构 */
    ret = av_xenstore_init_vm_structure(service, vm_uuid, domid);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to initialize XenStore structure: %s", av_error_string(ret));
        return ret;
    }
    
    av_log(AV_LOG_INFO, "VM connection test completed successfully");
    return AV_SUCCESS;
}

/* 检查服务状态 */
int check_service_status(const av_config_t* config) {
    FILE* pid_file = fopen(config->pid_file, "r");
    if (!pid_file) {
        printf("Service is not running (no PID file found)\n");
        return AV_SUCCESS;
    }
    
    char pid_str[32];
    if (fgets(pid_str, sizeof(pid_str), pid_file) == NULL) {
        printf("Service status unknown (invalid PID file)\n");
        fclose(pid_file);
        return AV_ERROR_PROTOCOL;
    }
    
    fclose(pid_file);
    
    pid_t pid = atoi(pid_str);
    if (pid <= 0) {
        printf("Service status unknown (invalid PID: %d)\n", pid);
        return AV_ERROR_PROTOCOL;
    }
    
    /* 检查进程是否存在 */
    if (kill(pid, 0) == 0) {
        printf("Service is running (PID: %d)\n", pid);
        
        /* 尝试获取更多状态信息 */
        char proc_path[256];
        snprintf(proc_path, sizeof(proc_path), "/proc/%d/stat", pid);
        
        FILE* stat_file = fopen(proc_path, "r");
        if (stat_file) {
            char stat_line[512];
            if (fgets(stat_line, sizeof(stat_line), stat_file)) {
                printf("Process info: %s", stat_line);
            }
            fclose(stat_file);
        }
    } else {
        printf("Service is not running (stale PID file)\n");
        unlink(config->pid_file);
    }
    
    return AV_SUCCESS;
}

/* 检查配置文件 */
int check_config(av_config_t* config) {
    printf("Checking configuration...\n");
    printf("Config file: %s\n", config->config_file);
    
    int ret = read_config_file(config);
    if (ret != AV_SUCCESS) {
        printf("Configuration check failed: %s\n", av_error_string(ret));
        return ret;
    }
    
    printf("Configuration is valid:\n");
    printf("  Log level: %d\n", config->log_level);
    printf("  PID file: %s\n", config->pid_file);
    printf("  Log file: %s\n", config->log_file);
    printf("  Monitor interval: %d ms\n", config->monitor_interval_ms);
    printf("  Health check interval: %d s\n", config->health_check_interval_s);
    printf("  Stats report interval: %d s\n", config->stats_report_interval_s);
    
    if (strlen(config->target_uuid) > 0) {
        printf("  Target UUID: %s\n", config->target_uuid);
    }
    
    return AV_SUCCESS;
}

/* 主函数 */
int main(int argc, char* argv[]) {
    int ret = 0;
    int test_shm = 0;
    int test_xenstore = 0;
    int list_vms = 0;
    int check_config_only = 0;
    int show_status = 0;
    
    /* 初始化默认配置 */
    init_default_config(&g_config);
    
    /* 命令行选项 */
    static struct option long_options[] = {
        {"help", no_argument, 0, 'h'},
        {"version", no_argument, 0, 'v'},
        {"daemon", no_argument, 0, 'd'},
        {"config", required_argument, 0, 'c'},
        {"log-level", required_argument, 0, 'l'},
        {"uuid", required_argument, 0, 'u'},
        {"pid-file", required_argument, 0, 'p'},
        {"list-vms", no_argument, 0, 1000},
        {"test-mode", no_argument, 0, 1001},
        {"test-shm", no_argument, 0, 1002},
        {"test-xenstore", no_argument, 0, 1003},
        {"check-config", no_argument, 0, 1004},
        {"status", no_argument, 0, 1005},
        {0, 0, 0, 0}
    };
    
    /* 解析命令行参数 */
    int c;
    while ((c = getopt_long(argc, argv, "hvdc:l:u:p:", long_options, NULL)) != -1) {
        switch (c) {
            case 'h':
                show_help(argv[0]);
                return 0;
            case 'v':
                show_version();
                return 0;
            case 'd':
                g_config.daemon_mode = 1;
                break;
            case 'c':
                strncpy(g_config.config_file, optarg, sizeof(g_config.config_file) - 1);
                break;
            case 'l':
                if (strcmp(optarg, "error") == 0) {
                    g_config.log_level = AV_LOG_ERROR;
                } else if (strcmp(optarg, "warn") == 0) {
                    g_config.log_level = AV_LOG_WARN;
                } else if (strcmp(optarg, "info") == 0) {
                    g_config.log_level = AV_LOG_INFO;
                } else if (strcmp(optarg, "debug") == 0) {
                    g_config.log_level = AV_LOG_DEBUG;
                } else {
                    fprintf(stderr, "Invalid log level: %s\n", optarg);
                    return 1;
                }
                break;
            case 'u':
                strncpy(g_config.target_uuid, optarg, sizeof(g_config.target_uuid) - 1);
                break;
            case 'p':
                strncpy(g_config.pid_file, optarg, sizeof(g_config.pid_file) - 1);
                break;
            case 1000:
                list_vms = 1;
                break;
            case 1001:
                g_config.test_mode = 1;
                break;
            case 1002:
                test_shm = 1;
                break;
            case 1003:
                test_xenstore = 1;
                break;
            case 1004:
                check_config_only = 1;
                break;
            case 1005:
                show_status = 1;
                break;
            case '?':
                fprintf(stderr, "Use --help for usage information\n");
                return 1;
            default:
                break;
        }
    }
    
    /* 读取配置文件 */
    ret = read_config_file(&g_config);
    if (ret != AV_SUCCESS && !check_config_only) {
        fprintf(stderr, "Failed to read configuration: %s\n", av_error_string(ret));
        return 1;
    }
    
    /* 处理仅检查配置的情况 */
    if (check_config_only) {
        return check_config(&g_config) == AV_SUCCESS ? 0 : 1;
    }
    
    /* 处理状态查询 */
    if (show_status) {
        return check_service_status(&g_config) == AV_SUCCESS ? 0 : 1;
    }
    
    /* 验证系统环境 */
    ret = av_validate_environment();
    if (ret != AV_SUCCESS) {
        fprintf(stderr, "Environment validation failed: %s\n", av_error_string(ret));
        return 1;
    }
    
    /* 守护进程化 */
    if (g_config.daemon_mode) {
        ret = daemonize();
        if (ret != AV_SUCCESS) {
            fprintf(stderr, "Failed to daemonize: %s\n", av_error_string(ret));
            return 1;
        }
    }
    
    /* 创建PID文件 */
    ret = create_pid_file(g_config.pid_file);
    if (ret != AV_SUCCESS) {
        fprintf(stderr, "Failed to create PID file: %s\n", av_error_string(ret));
        return 1;
    }
    
    /* 初始化服务 */
    memset(&g_service, 0, sizeof(g_service));
    ret = av_host_service_init(&g_service);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to initialize service: %s", av_error_string(ret));
        cleanup_pid_file(g_config.pid_file);
        return 1;
    }
    
    /* 初始化资源管理器 */
    ret = av_resource_manager_init();
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to initialize resource manager: %s", av_error_string(ret));
        cleanup_pid_file(g_config.pid_file);
        return 1;
    }

    /* 设置资源限制 */
    av_set_resource_limits();

    /* 安装增强的信号处理器 */
    ret = av_install_signal_handlers();
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to install signal handlers: %s", av_error_string(ret));
        av_resource_manager_cleanup();
        cleanup_pid_file(g_config.pid_file);
        return 1;
    }
    
    /* 初始化统计 */
    av_stats_init();
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Host Service starting...");
    av_log(AV_LOG_INFO, "Configuration: daemon=%d, test_mode=%d, log_level=%d", 
           g_config.daemon_mode, g_config.test_mode, g_config.log_level);
    
    /* 执行特定操作 */
    if (list_vms) {
        ret = list_running_vms(&g_service);
        goto cleanup;
    }
    
    if (test_shm) {
        ret = av_test_shared_memory(&g_service);
        if (ret == AV_SUCCESS) {
            av_log(AV_LOG_INFO, "Shared memory test completed successfully");
        } else {
            av_log(AV_LOG_ERROR, "Shared memory test failed: %s", av_error_string(ret));
        }
        goto cleanup;
    }
    
    if (test_xenstore) {
        ret = av_xenstore_health_check(&g_service);
        if (ret == AV_SUCCESS) {
            av_log(AV_LOG_INFO, "XenStore health check completed successfully");
            
            /* 初始化并报告XenStore统计 */
            av_xenstore_stats_init();
            av_xenstore_stats_record_read(100, 1);
            av_xenstore_stats_record_write(50, 1);
            av_xenstore_stats_report();
        } else {
            av_log(AV_LOG_ERROR, "XenStore health check failed: %s", av_error_string(ret));
        }
        goto cleanup;
    }
    
    /* 如果指定了目标UUID，测试连接 */
    if (strlen(g_config.target_uuid) > 0) {
        ret = test_vm_connection(&g_service, g_config.target_uuid);
        if (ret != AV_SUCCESS) {
            goto cleanup;
        }
        
        if (g_config.test_mode) {
            av_log(AV_LOG_INFO, "Test mode completed successfully");
            goto cleanup;
        }
        
        /* 添加VM上下文 */
        ret = av_add_vm_context(&g_service, g_config.target_uuid);
        if (ret != AV_SUCCESS) {
            av_log(AV_LOG_ERROR, "Failed to add VM context: %s", av_error_string(ret));
            goto cleanup;
        }
    }
    
    /* 启动服务 */
    ret = av_host_service_start(&g_service);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to start service: %s", av_error_string(ret));
        goto cleanup;
    }
    
    av_log(AV_LOG_INFO, "Service started successfully");
    
    /* 主服务循环 */
    int health_check_counter = 0;
    int stats_counter = 0;
    
    while (!g_shutdown_requested && !g_service.shutdown_requested) {
        /* 使用可中断的sleep */
        struct timespec sleep_time = {1, 0};
        struct timespec remaining;
        
        if (nanosleep(&sleep_time, &remaining) == -1) {
            if (errno == EINTR) {
                av_log(AV_LOG_DEBUG, "Sleep interrupted by signal");
                /* 检查是否需要退出 */
                if (g_shutdown_requested || g_service.shutdown_requested) {
                    break;
                }
            }
        }
        
        /* 定期健康检查 */
        if (++health_check_counter >= g_config.health_check_interval_s) {
            if (!g_shutdown_requested && !g_service.shutdown_requested) {
                av_response_health_check(&g_service);
            }
            health_check_counter = 0;
        }
        
        /* 定期统计报告 */
        if (++stats_counter >= g_config.stats_report_interval_s) {
            if (!g_shutdown_requested && !g_service.shutdown_requested) {
                av_stats_report();
                av_response_stats_report();
                av_resource_report();
            }
            stats_counter = 0;
        }
    }
    
cleanup:
    av_log(AV_LOG_INFO, "Shutting down service...");
    
    /* 停止服务 */
    av_host_service_stop(&g_service);
    
    /* 清理资源 */
    av_host_service_cleanup(&g_service);
    
    /* 最终统计报告 */
    av_stats_report();
    av_response_stats_report();
    av_resource_report();
    
    /* 恢复信号处理器 */
    av_restore_signal_handlers();
    
    /* 清理资源管理器 */
    av_resource_manager_cleanup();
    
    /* 清理PID文件 */
    cleanup_pid_file(g_config.pid_file);
    
    av_log(AV_LOG_INFO, "Service shutdown complete");
    
    return ret == AV_SUCCESS ? 0 : 1;
}