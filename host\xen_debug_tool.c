/*
 * Xen环境深度诊断工具
 * 用于排查权限和接口问题
 */

#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <grp.h>
#include <pwd.h>

/* Xen头文件 */
#include <xenctrl.h>
#include <xenstore.h>
#include <xengnttab.h>

void print_separator(const char* title) {
    printf("\n=== %s ===\n", title);
}

void check_user_info() {
    print_separator("用户信息检查");
    
    uid_t uid = getuid();
    uid_t euid = geteuid();
    gid_t gid = getgid();
    gid_t egid = getegid();
    
    printf("Real UID: %d, Effective UID: %d\n", uid, euid);
    printf("Real GID: %d, Effective GID: %d\n", gid, egid);
    
    struct passwd* pw = getpwuid(uid);
    if (pw) {
        printf("Username: %s\n", pw->pw_name);
    }
    
    printf("Groups: ");
    int ngroups = getgroups(0, NULL);
    if (ngroups > 0) {
        gid_t* groups = malloc(ngroups * sizeof(gid_t));
        if (getgroups(ngroups, groups) != -1) {
            for (int i = 0; i < ngroups; i++) {
                struct group* gr = getgrgid(groups[i]);
                printf("%s(%d) ", gr ? gr->gr_name : "unknown", groups[i]);
            }
        }
        free(groups);
    }
    printf("\n");
    
    if (euid != 0) {
        printf("❌ NOT running as root (euid=%d)\n", euid);
    } else {
        printf("✅ Running as root\n");
    }
}

void check_xen_devices() {
    print_separator("Xen设备文件检查");
    
    const char* xen_devices[] = {
        "/dev/xen/hypercall",
        "/dev/xen/privcmd", 
        "/dev/xen/evtchn",
        "/dev/xen/gntdev",
        "/dev/xen/gntalloc",
        "/dev/xen/xenbus",
        "/dev/xen/xenbus_backend",
        NULL
    };
    
    for (int i = 0; xen_devices[i]; i++) {
        struct stat st;
        if (stat(xen_devices[i], &st) == 0) {
            printf("✅ %s exists (mode: %o, uid: %d, gid: %d)\n", 
                   xen_devices[i], st.st_mode & 0777, st.st_uid, st.st_gid);
            
            // 测试访问权限
            if (access(xen_devices[i], R_OK | W_OK) == 0) {
                printf("   ✅ Read/Write access OK\n");
            } else {
                printf("   ❌ Read/Write access FAILED: %s\n", strerror(errno));
            }
        } else {
            printf("❌ %s NOT found: %s\n", xen_devices[i], strerror(errno));
        }
    }
}

void check_proc_xen() {
    print_separator("/proc/xen 检查");
    
    const char* proc_files[] = {
        "/proc/xen/capabilities",
        "/proc/xen/privcmd",
        "/proc/xen/xenbus",
        NULL
    };
    
    for (int i = 0; proc_files[i]; i++) {
        struct stat st;
        if (stat(proc_files[i], &st) == 0) {
            printf("✅ %s exists (mode: %o)\n", proc_files[i], st.st_mode & 0777);
            
            if (access(proc_files[i], R_OK) == 0) {
                printf("   ✅ Read access OK\n");
                
                // 读取capabilities
                if (strcmp(proc_files[i], "/proc/xen/capabilities") == 0) {
                    FILE* fp = fopen(proc_files[i], "r");
                    if (fp) {
                        char buffer[256];
                        if (fgets(buffer, sizeof(buffer), fp)) {
                            printf("   Content: %s", buffer);
                        }
                        fclose(fp);
                    }
                }
            } else {
                printf("   ❌ Read access FAILED: %s\n", strerror(errno));
            }
        } else {
            printf("❌ %s NOT found: %s\n", proc_files[i], strerror(errno));
        }
    }
}

void test_xc_interface() {
    print_separator("libxenctrl接口测试");
    
    printf("尝试打开xc_interface...\n");
    xc_interface* xc_handle = xc_interface_open(NULL, NULL, 0);
    
    if (!xc_handle) {
        printf("❌ xc_interface_open FAILED: %s (errno: %d)\n", strerror(errno), errno);
        
        // 详细错误分析
        switch (errno) {
            case EACCES:
            case EPERM:
                printf("   原因: 权限不足\n");
                printf("   解决: 确保以root身份运行\n");
                break;
            case ENOENT:
            case ENODEV:
                printf("   原因: Xen设备不存在\n");
                printf("   解决: 检查Xen是否正确安装和运行\n");
                break;
            case ECONNREFUSED:
                printf("   原因: 连接被拒绝\n");
                printf("   解决: 检查xenstored服务状态\n");
                break;
            default:
                printf("   原因: 未知错误 (errno: %d)\n", errno);
        }
        return;
    }
    
    printf("✅ xc_interface_open SUCCESS\n");
    
    // 测试获取版本信息
    printf("测试获取Xen版本信息...\n");
    xen_extraversion_t xen_extra;
    int ret = xc_version(xc_handle, XENVER_extraversion, &xen_extra);
    if (ret == 0) {
        printf("✅ Xen extra version: %s\n", xen_extra);
    } else {
        printf("❌ Failed to get Xen version: %s\n", strerror(errno));
    }
    
    // 测试获取域列表
    printf("测试获取域列表...\n");
    xc_domaininfo_t domain_info[10];
    int num_domains = xc_domain_getinfolist(xc_handle, 0, 10, domain_info);
    
    if (num_domains < 0) {
        printf("❌ xc_domain_getinfolist FAILED: %s (errno: %d)\n", strerror(errno), errno);
        
        // 详细错误分析
        switch (errno) {
            case EACCES:
            case EPERM:
                printf("   原因: 权限不足，无法访问域信息\n");
                printf("   解决: 检查是否有足够的Xen管理权限\n");
                break;
            case ESRCH:
                printf("   原因: 指定的域不存在\n");
                break;
            case EINVAL:
                printf("   原因: 参数无效，可能是库版本不兼容\n");
                printf("   解决: 检查编译时的库版本与运行时是否匹配\n");
                break;
            default:
                printf("   原因: 未知错误 (errno: %d)\n", errno);
        }
    } else {
        printf("✅ xc_domain_getinfolist SUCCESS, found %d domains\n", num_domains);
        
        for (int i = 0; i < num_domains; i++) {
            printf("   Domain %d: ID=%d, flags=0x%x\n", 
                   i, domain_info[i].domain, domain_info[i].flags);
        }
    }
    
    xc_interface_close(xc_handle);
}

void test_xenstore() {
    print_separator("XenStore接口测试");
    
    printf("尝试打开XenStore...\n");
    struct xs_handle* xs_handle = xs_open(0);
    
    if (!xs_handle) {
        printf("❌ xs_open FAILED: %s (errno: %d)\n", strerror(errno), errno);
        return;
    }
    
    printf("✅ xs_open SUCCESS\n");
    
    // 测试读取Dom0信息
    printf("测试读取Dom0信息...\n");
    unsigned int len;
    void* value = xs_read(xs_handle, XBT_NULL, "/local/domain/0/name", &len);
    
    if (value) {
        printf("✅ Dom0 name: %s\n", (char*)value);
        free(value);
    } else {
        printf("⚠️  无法读取Dom0名称 (这是正常的)\n");
    }
    
    // 测试列出域
    printf("测试列出所有域...\n");
    char** domains = xs_directory(xs_handle, XBT_NULL, "/local/domain", &len);
    if (domains) {
        printf("✅ 找到 %u 个域:\n", len);
        for (unsigned int i = 0; i < len; i++) {
            printf("   Domain: %s\n", domains[i]);
        }
        free(domains);
    } else {
        printf("❌ 无法列出域: %s\n", strerror(errno));
    }
    
    xs_close(xs_handle);
}

void test_xengnttab() {
    print_separator("Grant Table接口测试");
    
    printf("尝试打开Grant Table...\n");
    xengnttab_handle* gnttab_handle = xengnttab_open(NULL, 0);
    
    if (!gnttab_handle) {
        printf("❌ xengnttab_open FAILED: %s (errno: %d)\n", strerror(errno), errno);
        return;
    }
    
    printf("✅ xengnttab_open SUCCESS\n");
    xengnttab_close(gnttab_handle);
}

void check_system_info() {
    print_separator("系统信息");
    
    // 检查内核版本
    FILE* fp = fopen("/proc/version", "r");
    if (fp) {
        char buffer[512];
        if (fgets(buffer, sizeof(buffer), fp)) {
            printf("Kernel: %s", buffer);
        }
        fclose(fp);
    }
    
    // 检查Xen版本
    fp = fopen("/sys/hypervisor/version/major", "r");
    if (fp) {
        int major;
        if (fscanf(fp, "%d", &major) == 1) {
            fclose(fp);
            fp = fopen("/sys/hypervisor/version/minor", "r");
            if (fp) {
                int minor;
                if (fscanf(fp, "%d", &minor) == 1) {
                    printf("Xen Version: %d.%d\n", major, minor);
                }
                fclose(fp);
            }
        } else {
            fclose(fp);
        }
    }
    
    // 检查编译信息
    printf("Program compiled with Xen 4.17.x libraries\n");
}

int main() {
    printf("Xen环境深度诊断工具\n");
    printf("====================\n");
    
    check_user_info();
    check_system_info();
    check_xen_devices();
    check_proc_xen();
    test_xc_interface();
    test_xenstore();
    test_xengnttab();
    
    print_separator("诊断完成");
    printf("如果所有测试都通过，但程序仍然无法工作，\n");
    printf("可能是库版本兼容性问题或特定的权限配置问题。\n");
    
    return 0;
}
