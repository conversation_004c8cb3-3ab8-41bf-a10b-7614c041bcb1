[Unit]
Description=XenServer Agentless Antivirus Host Service
Documentation=man:xenserver-antivirus-host(8)
After=network.target xenstored.service
Wants=xenstored.service
RequiresMountsFor=/var/run /var/log

[Service]
Type=forking
User=root
Group=root
ExecStart=/usr/local/bin/xenserver-antivirus-host --daemon --config /etc/xenserver-antivirus/host.conf
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/var/run/xenserver-antivirus-host.pid
TimeoutStartSec=30
TimeoutStopSec=30
Restart=on-failure
RestartSec=5

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/run /var/log /tmp
PrivateTmp=true

# Resource limits
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target