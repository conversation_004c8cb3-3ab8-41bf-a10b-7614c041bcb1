# Linux客户端静态编译指南

## 概述

本文档说明如何为XenServer防病毒Linux客户端创建静态链接的二进制文件，以便在不同Linux环境中部署。

## 为什么需要静态编译

### 部署优势
- **单文件部署**：无需安装依赖库
- **跨平台兼容**：可在不同Linux发行版运行
- **版本独立**：不依赖系统库版本
- **容器友好**：适合容器化部署

### 使用场景
- 在多种Linux发行版上部署
- 容器化部署（scratch/distroless镜像）
- 嵌入式系统部署
- 离线环境部署

## 编译方法

### 方法1：简化版静态编译（推荐）

使用简化版Makefile，避开复杂的glibc依赖：

```bash
# 编译简化版静态程序
make -f Makefile.simple-static all

# 验证静态链接
make -f Makefile.simple-static verify

# 运行测试
make -f Makefile.simple-static run
```

**特点：**
- 二进制大小：~984KB
- 无动态依赖
- 包含核心功能
- 使用模拟接口（适合测试）

### 方法2：完整版静态编译

使用完整版Makefile，包含所有Xen依赖：

```bash
# 编译完整版静态程序
make -f Makefile.static all

# 验证静态链接
make -f Makefile.static verify
```

**注意：** 可能遇到glibc头文件问题，需要特殊处理。

### 方法3：使用构建脚本

使用自动化构建脚本：

```bash
# 使用构建脚本
./build_static.sh -r -v    # 构建发布版并验证
./build_static.sh -m -i    # 构建最小版并安装
```

## 编译结果对比

| 版本 | 文件大小 | 依赖 | Xen支持 | 适用场景 |
|------|----------|------|---------|----------|
| 简化版静态 | ~984KB | 无 | 模拟 | 测试、开发 |
| 完整版静态 | ~2-5MB | 无 | 完整 | 生产环境 |
| 动态链接 | ~50KB | 多个 | 完整 | 开发环境 |

## 验证静态链接

### 检查依赖
```bash
# 应该显示 "not a dynamic executable" 或无输出
ldd bin-simple-static/av_linux_client_simple_static

# 查看文件信息
file bin-simple-static/av_linux_client_simple_static
# 输出应包含 "statically linked"
```

### 测试运行
```bash
# 显示帮助信息
./bin-simple-static/av_linux_client_simple_static --help

# 运行测试（5秒后自动停止）
timeout 5s ./bin-simple-static/av_linux_client_simple_static -v
```

## 部署指南

### 本地安装
```bash
# 安装到系统
sudo make -f Makefile.simple-static install

# 运行已安装的程序
av_linux_client_simple_static --help
```

### 容器化部署
```dockerfile
# 使用scratch基础镜像
FROM scratch
COPY bin-simple-static/av_linux_client_simple_static /av_client
ENTRYPOINT ["/av_client"]
```

### 直接复制部署
```bash
# 直接复制到目标系统
scp bin-simple-static/av_linux_client_simple_static user@target:/usr/local/bin/
ssh user@target 'chmod +x /usr/local/bin/av_linux_client_simple_static'
```

## 故障排除

### glibc编译错误
如果遇到glibc头文件错误：
```
error: missing binary operator before token "("
#if __GLIBC_USE (IEC_60559_BFP_EXT)
```

**解决方案：**
1. 使用简化版Makefile（推荐）
2. 安装musl-gcc：`sudo apt install musl-tools`
3. 使用Alpine Linux构建环境

### 运行时错误
如果静态程序无法运行：
```bash
# 检查架构兼容性
file bin-simple-static/av_linux_client_simple_static

# 检查权限
chmod +x bin-simple-static/av_linux_client_simple_static

# 检查系统兼容性
./bin-simple-static/av_linux_client_simple_static --help
```

## 最佳实践

### 构建环境
- 使用较老的Linux发行版构建（向后兼容）
- 或使用musl-gcc（更好的静态链接支持）
- 或使用Alpine Linux容器构建

### 测试验证
- 在多个Linux发行版上测试
- 验证无动态依赖
- 测试核心功能正常

### 部署策略
- 为不同架构构建不同版本（x86_64, ARM等）
- 使用版本号管理二进制文件
- 提供校验和验证文件完整性

## 相关文件

- `Makefile.simple-static` - 简化版静态编译
- `Makefile.static` - 完整版静态编译  
- `build_static.sh` - 自动化构建脚本
- `static_comparison.md` - glibc vs musl对比
- `main_simple.c` - 简化版主程序
- `xen_mock_impl.c` - Xen接口模拟实现