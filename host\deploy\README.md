Creating README...
# XenServer Antivirus Host Service - Cross-Compiled

## Files
- `xenserver-antivirus-cross`: Dynamic version (compiled with Xen 4.17.x)
- `xenserver-antivirus-cross-static`: Static version (compiled with Xen 4.17.x)
- `xenserver-antivirus-native`: **Native Xen 4.13.x version (RECOMMENDED)**
- `xen_debug_tool_static`: Xen environment diagnostic tool
- `deploy.sh`: Automated deployment script

## Deployment
1. Copy this directory to your Xen host
2. Run: `sudo ./deploy.sh`

## Troubleshooting
If you encounter permission errors, run the diagnostic tool first:
```bash
# Run comprehensive Xen environment diagnosis
sudo ./xen_debug_tool_static
```

## Manual Installation

### Option 1: Native Version (Recommended for Xen 4.13.x)
```bash
# 1. Test environment
./xenserver-antivirus-native --test

# 2. Install
sudo install -m 755 xenserver-antivirus-native /usr/local/bin/

# 3. Run
sudo /usr/local/bin/xenserver-antivirus-native -v
```

### Option 2: Cross-compiled Version (if native fails)
```bash
# 1. Run diagnostic tool to check environment
sudo ./xen_debug_tool_static

# 2. Test environment
./xenserver-antivirus-cross-static --test

# 3. Install
sudo install -m 755 xenserver-antivirus-cross-static /usr/local/bin/

# 4. Run
sudo /usr/local/bin/xenserver-antivirus-cross-static -v
```

## Compatibility
- **xenserver-antivirus-native**: Native Xen 4.13.x, uses command-line tools
- **xenserver-antivirus-cross-static**: Cross-compiled with Xen 4.17.x libraries (WSL)
- **Target systems**: Xen 4.13.x - 4.17.x
- **Build date**: Fri Sep  5 12:19:12 CST 2025

## Key Differences
- **Native version**: Uses `xenstore-ls` and `xl` commands, avoids API conflicts
- **Cross-compiled version**: Uses direct libxenctrl API calls, may have compatibility issues
