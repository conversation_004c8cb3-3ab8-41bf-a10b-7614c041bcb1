#ifndef AV_LINUX_CLIENT_H
#define AV_LINUX_CLIENT_H

#include "../common/av_common.h"
#include <sys/mman.h>
#include <pthread.h>
#include <stdbool.h>

/* 条件编译：在有Xen环境时使用真实头文件，否则使用模拟定义 */
#ifdef HAVE_XEN_LIBS

/* 在包含Xen头文件之前设置必要的宏 */
#ifndef __XEN_TOOLS__
#define __XEN_TOOLS__ 1
#endif

/* 确保正确的包含顺序 */
#include <stdint.h>
#include <unistd.h>
#include <xenctrl.h>
#include <xenstore.h>

#else
/* 模拟Xen类型定义 */
typedef struct xc_interface xc_interface;
typedef struct xs_handle xs_handle;
typedef uint32_t domid_t;
typedef uint32_t grant_ref_t;
typedef uint32_t xs_transaction_t;
#endif

/* 版本信息 */
#define AV_VERSION "1.0.0"

/* 错误码定义 */
#define AV_SUCCESS 0
#define AV_ERROR_INIT -1
#define AV_ERROR_MEMORY -2
#define AV_ERROR_XEN -3
#define AV_ERROR_TIMEOUT -4
#define AV_ERROR_FORK -5
#define AV_ERROR_SETSID -6
#define AV_ERROR_CHDIR -7

/* 回调函数类型 */
typedef void (*xenstore_callback_t)(const char* path, const char* value, void* user_data);

/* Linux客户端结构体 */
typedef struct {
    xc_interface* xc_handle;        /* libxenctrl句柄 */
    struct xs_handle* xs_handle;    /* XenStore句柄 */
    char vm_uuid[64];               /* 虚拟机UUID */
    pthread_t monitor_thread;       /* XenStore监听线程 */
    volatile int shutdown_requested; /* 关闭请求标志 */
    void* shared_memory;            /* 共享内存地址 */
    size_t shared_memory_size;      /* 共享内存大小 */
} av_linux_client_t;

/* 函数声明 */
int av_get_vm_uuid(av_linux_client_t* client, char* uuid_buffer, size_t buffer_size);

/* XenStore监听相关函数 */
int av_monitor_xenstore_path(av_linux_client_t* client, const char* path, xenstore_callback_t callback);

/* 内存映射相关函数 */
#ifdef HAVE_XEN_LIBS
void* av_map_grant_memory(av_linux_client_t* client, grant_ref_t grant_ref, size_t size);
#else
void* av_map_grant_memory(av_linux_client_t* client, uint32_t grant_ref, size_t size);
#endif
int av_unmap_grant_memory(av_linux_client_t* client, void* addr, size_t size);

/* 通信协议相关函数 */
int av_send_scan_request(av_linux_client_t* client, void* shm_addr);
int av_wait_for_scan_response(av_linux_client_t* client, void* shm_addr, char* response, int timeout_ms);

/* 客户端生命周期管理函数 */
int av_linux_client_init(av_linux_client_t* client);
int av_linux_client_start(av_linux_client_t* client);
int av_linux_client_stop(av_linux_client_t* client);
void av_linux_client_cleanup(av_linux_client_t* client);

/* 主通信循环 */
int av_linux_client_run_communication(av_linux_client_t* client);

#endif /* AV_LINUX_CLIENT_H */