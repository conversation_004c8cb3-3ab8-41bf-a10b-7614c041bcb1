# Linux客户端部分静态编译Makefile - 只静态链接特定库

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE

# 检查是否有Xen库
XEN_LIBS_AVAILABLE := $(shell pkg-config --exists xencontrol xenstore 2>/dev/null && echo yes || echo no)

ifeq ($(XEN_LIBS_AVAILABLE),yes)
    CFLAGS += -DHAVE_XEN_LIBS
    # 部分静态链接：只静态链接Xen库，系统库保持动态链接
    LDFLAGS = -Wl,-Bstatic -lxencontrol -lxenstore -Wl,-Bdynamic -lpthread -lrt -ldl
else
    $(error "Xen libraries not found. Please install libxen-dev or xen-devel package")
endif

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj-partial-static
BINDIR = bin-partial-static

# 真实版本源文件
REAL_SOURCES = main_real.c av_linux_client_real.c
COMMON_SOURCES = $(COMMONDIR)/av_common.c
SOURCES = $(REAL_SOURCES) $(COMMON_SOURCES)
OBJECTS = $(REAL_SOURCES:%.c=$(OBJDIR)/%.o) $(OBJDIR)/av_common.o
TARGET = $(BINDIR)/av_linux_client_partial_static

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I/usr/include/xen

# 默认目标
all: check-xen directories $(TARGET)

# 检查Xen环境
check-xen:
	@echo "Checking Xen libraries..."
ifeq ($(XEN_LIBS_AVAILABLE),no)
	@echo "ERROR: Xen libraries not found!"
	@echo "Please install:"
	@echo "  Ubuntu/Debian: sudo apt install libxen-dev"
	@echo "  CentOS/RHEL: sudo yum install xen-devel"
	@exit 1
else
	@echo "Xen libraries found: OK"
endif

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Partial static linking $@..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Partial static build complete: $@"
	@echo "Binary size: $$(du -h $@ | cut -f1)"
	@echo "Dependencies check:"
	@ldd $@ | head -10

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $< (partial static)..."
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 编译common文件
$(OBJDIR)/av_common.o: $(COMMONDIR)/av_common.c
	@echo "Compiling $< (partial static)..."
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning partial static build files..."
	rm -rf $(OBJDIR) $(BINDIR)

# 安装程序
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/av_linux_client_partial_static
	sudo chmod +x /usr/local/bin/av_linux_client_partial_static
	@echo "Partial static installation complete"

# 卸载程序
uninstall:
	@echo "Uninstalling av_linux_client_partial_static..."
	sudo rm -f /usr/local/bin/av_linux_client_partial_static
	@echo "Uninstall complete"

# 运行程序（需要root权限）
run: $(TARGET)
	@echo "Running $(TARGET) (requires root privileges)..."
	sudo $(TARGET)

# 验证链接
verify: $(TARGET)
	@echo "Verifying partial static linkage..."
	@file $(TARGET)
	@echo "Dependencies:"
	@ldd $(TARGET)
	@echo "Binary size: $$(du -h $(TARGET) | cut -f1)"
	@echo "Xen libraries check:"
	@ldd $(TARGET) | grep -E "(xen|libxen)" || echo "  -> Xen libraries statically linked"

# 调试版本
debug: CFLAGS += -DDEBUG -g3
debug: $(TARGET)

# 发布版本
release: CFLAGS += -DNDEBUG -O3
release: clean $(TARGET)

# 帮助信息
help:
	@echo "Available targets for partial static build:"
	@echo "  all        - Build the partial static program (default)"
	@echo "  clean      - Remove partial static build files"
	@echo "  install    - Install the partial static program"
	@echo "  uninstall  - Remove the installed partial static program"
	@echo "  run        - Build and run the partial static program"
	@echo "  verify     - Verify partial static linkage of built binary"
	@echo "  debug      - Build debug version"
	@echo "  release    - Build optimized release version"
	@echo "  help       - Show this help message"

.PHONY: all clean install uninstall run verify debug release help directories check-xen