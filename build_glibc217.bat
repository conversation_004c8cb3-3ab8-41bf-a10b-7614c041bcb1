@echo off
REM XenServer防病毒系统 - glibc 2.17兼容构建脚本 (Windows批处理版本)
REM 调用WSL进行编译

echo ================================================================
echo XenServer防病毒系统 - glibc 2.17兼容构建
echo ================================================================
echo 目标环境: Citrix Hypervisor 8.2.1 (glibc 2.17)
echo 编译环境: WSL
echo ================================================================
echo.

REM 检查WSL是否可用
wsl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] WSL未安装或不可用
    echo 请安装WSL并确保Linux发行版已配置
    pause
    exit /b 1
)

echo [INFO] 检查WSL环境...
wsl bash -c "echo 'WSL环境正常'"

echo.
echo [INFO] 开始在WSL中构建...
echo.

REM 在WSL中运行构建脚本
wsl bash -c "cd /mnt/d/workspace/codebase/ai_project/qoder/xenserver-antivirus && chmod +x build_glibc217.sh && ./build_glibc217.sh"

if %errorlevel% equ 0 (
    echo.
    echo ================================================================
    echo 构建成功完成！
    echo ================================================================
    echo.
    echo 构建结果位于:
    echo - host/bin/xenserver-antivirus-glibc217-static
    echo - host/deploy_glibc217/
    echo.
    echo 部署说明:
    echo 1. 将 host/deploy_glibc217/ 目录复制到 Citrix Hypervisor 宿主机
    echo 2. 在宿主机上运行: sudo ./deploy_glibc217.sh
    echo.
) else (
    echo.
    echo [ERROR] 构建失败
    echo 请检查错误信息并重试
    echo.
)

pause
