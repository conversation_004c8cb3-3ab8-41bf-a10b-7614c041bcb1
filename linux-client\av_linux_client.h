#ifndef AV_LINUX_CLIENT_H
#define AV_LINUX_CLIENT_H

#include "../common/av_common.h"
#include <sys/mman.h>
#include <pthread.h>
#include <stdbool.h>

/* 条件编译：在有Xen环境时使用真实头文件，否则使用模拟定义 */
#ifdef HAVE_XEN_LIBS

/* 在包含Xen头文件之前设置必要的宏 */
#ifndef __XEN_TOOLS__
#define __XEN_TOOLS__ 1
#endif

/* 确保正确的包含顺序 */
#include <stdint.h>
#include <unistd.h>
#include <xenctrl.h>
#include <xenstore.h>

#else
/* 模拟Xen类型定义 */
typedef struct xc_interface xc_interface;
typedef struct xs_handle xs_handle;
typedef uint32_t domid_t;
typedef uint32_t grant_ref_t;
typedef uint32_t xs_transaction_t;

/* 模拟常量 */
#define XBT_NULL 0
#define XS_WATCH_PATH 0
#define XS_WATCH_TOKEN 1

/* 模拟函数声明 */
xc_interface* xc_interface_open(void* logger, void* dombuild_logger, unsigned open_flags);
int xc_interface_close(xc_interface* xch);
void* xc_map_foreign_range(xc_interface* xch, domid_t dom, int size, int prot, unsigned long mfn);

struct xs_handle* xs_open(unsigned long flags);
void xs_close(struct xs_handle* xsh);
char* xs_read(struct xs_handle* xsh, xs_transaction_t t, const char* path, unsigned int* len);
bool xs_watch(struct xs_handle* xsh, const char* path, const char* token);
int xs_fileno(struct xs_handle* xsh);
char** xs_read_watch(struct xs_handle* xsh, unsigned int* num);
#endif

/* Xen相关类型定义 - 这些类型已在xenctrl.h中定义 */

/* XenStore回调函数类型 */
typedef void (*xenstore_callback_t)(const char* path, const char* value, void* user_data);

/* Linux客户端上下文 */
typedef struct {
    xc_interface* xc_handle;        /* libxenctrl句柄 */
    struct xs_handle* xs_handle;    /* XenStore句柄 */
    av_context_t context;           /* 客户端上下文 */
    pthread_t monitor_thread;       /* XenStore监听线程 */
    int shutdown_requested;         /* 关闭请求标志 */
    pthread_mutex_t context_mutex;  /* 上下文访问互斥锁 */
    pthread_cond_t shm_ready_cond;  /* 共享内存就绪条件变量 */
    int shm_ready;                  /* 共享内存就绪标志 */
} av_linux_client_t;

/* UUID获取接口 */
int av_get_vm_uuid(av_linux_client_t* client, char* uuid_buffer, size_t buffer_size);

/* XenStore监听接口 */
int av_monitor_xenstore_path(av_linux_client_t* client, const char* path, xenstore_callback_t callback);
void* av_xenstore_monitor_thread(void* arg);
void av_xenstore_shm_callback(const char* path, const char* value, void* user_data);

/* 内存映射接口 */
void* av_map_grant_memory(av_linux_client_t* client, grant_ref_t grant_ref, size_t size);
int av_unmap_grant_memory(av_linux_client_t* client, void* addr, size_t size);

/* 通信接口 */
int av_send_scan_request(av_linux_client_t* client, void* shm_addr);
int av_wait_for_scan_response(av_linux_client_t* client, void* shm_addr, char* response, int timeout_ms);

/* 客户端管理接口 */
int av_linux_client_init(av_linux_client_t* client);
int av_linux_client_start(av_linux_client_t* client);
int av_linux_client_stop(av_linux_client_t* client);
void av_linux_client_cleanup(av_linux_client_t* client);

/* 通信流程接口 */
int av_linux_client_run_communication(av_linux_client_t* client);

#endif /* AV_LINUX_CLIENT_H */