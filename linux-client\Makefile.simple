# 简化版Linux客户端Makefile - 不依赖Xen库

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g -D_GNU_SOURCE
LDFLAGS = -lpthread

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj
BINDIR = bin

# 源文件和目标文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
CLIENT_SOURCES = av_linux_client.c xen_mock_impl.c main.c
SOURCES = $(COMMON_SOURCES) $(CLIENT_SOURCES)
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/av_linux_client

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I.

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Linking $@..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Build complete: $@"

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $<..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning build files..."
	rm -rf $(OBJDIR) $(BINDIR)

.PHONY: all clean directories