#!/bin/bash
#
# xenserver-antivirus-host    XenServer Agentless Antivirus Host Service
#
# chkconfig: 35 80 20
# description: XenServer Agentless Antivirus Host Service
#

. /etc/rc.d/init.d/functions

USER="root"
DAEMON="xenserver-antivirus-host"
ROOT_DIR="/usr/local/bin"

SERVER="$ROOT_DIR/$DAEMON"
LOCK_FILE="/var/lock/subsys/$DAEMON"
PID_FILE="/var/run/$DAEMON.pid"
CONFIG_FILE="/etc/xenserver-antivirus/host.conf"

do_start() {
    if [ ! -f "$LOCK_FILE" ] ; then
        echo -n "Starting $DAEMON: "
        runuser -l "$USER" -c "$SERVER --daemon --config $CONFIG_FILE" && echo_success || echo_failure
        RETVAL=$?
        echo
        [ $RETVAL -eq 0 ] && touch $LOCK_FILE
    else
        echo "$DAEMON is locked."
        RETVAL=1
    fi
}
do_stop() {
    echo -n $"Shutting down $DAEMON: "
    pid=`ps -aefw | grep "$DAEMON" | grep -v " grep " | awk '{print $2}'`
    kill -9 $pid > /dev/null 2>&1
    [ $? -eq 0 ] && echo_success || echo_failure
    RETVAL=$?
    echo
    [ $RETVAL -eq 0 ] && rm -f $LOCK_FILE
}

do_status() {
    if [ -f $PID_FILE ]; then
        PID=`cat $PID_FILE`
        if [ -z "`ps axf | grep ${PID} | grep -v grep`" ]; then
            echo "$DAEMON dead but pid file exists"
            RETVAL=1
        else
            echo "$DAEMON (pid $PID) is running..."
            RETVAL=0
        fi
    else
        echo "$DAEMON is stopped"
        RETVAL=3
    fi
}

do_reload() {
    echo -n "Reloading $DAEMON configuration: "
    if [ -f $PID_FILE ]; then
        PID=`cat $PID_FILE`
        kill -HUP $PID > /dev/null 2>&1
        [ $? -eq 0 ] && echo_success || echo_failure
        RETVAL=$?
    else
        echo_failure
        RETVAL=1
    fi
    echo
}

case "$1" in
    start)
        do_start
        ;;
    stop)
        do_stop
        ;;
    status)
        do_status
        ;;
    restart)
        do_stop
        do_start
        ;;
    reload)
        do_reload
        ;;
    *)
        echo "Usage: $0 {start|stop|status|restart|reload}"
        RETVAL=1
esac

exit $RETVAL