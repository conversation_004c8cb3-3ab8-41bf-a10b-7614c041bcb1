#!/bin/bash

# Linux客户端静态编译构建脚本
# 用法: ./build_static.sh [选项]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Linux客户端静态编译构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示此帮助信息"
    echo "  -c, --clean     清理构建文件"
    echo "  -d, --debug     构建调试版本"
    echo "  -r, --release   构建发布版本"
    echo "  -m, --minimal   构建最小化版本"
    echo "  -v, --verify    验证静态链接"
    echo "  -i, --install   安装到系统"
    echo "  -u, --uninstall 从系统卸载"
    echo "  --check-deps    检查依赖"
    echo ""
    echo "示例:"
    echo "  $0              # 默认构建"
    echo "  $0 -r           # 构建发布版本"
    echo "  $0 -m -v        # 构建最小化版本并验证"
    echo "  $0 -c -r -i     # 清理、构建发布版本并安装"
}

# 检查系统依赖
check_system_deps() {
    print_info "检查系统依赖..."
    
    # 检查编译器
    if ! command -v gcc &> /dev/null; then
        print_error "gcc编译器未找到，请安装build-essential包"
        exit 1
    fi
    
    # 检查make
    if ! command -v make &> /dev/null; then
        print_error "make工具未找到，请安装build-essential包"
        exit 1
    fi
    
    # 检查静态库
    if ! ldconfig -p | grep -q "libc.a"; then
        print_warning "静态C库可能未安装，建议安装libc6-dev包"
    fi
    
    print_success "系统依赖检查完成"
}

# 主要构建函数
build_static() {
    local target="$1"
    
    print_info "开始静态编译Linux客户端..."
    
    # 检查Makefile.static是否存在
    if [ ! -f "Makefile.static" ]; then
        print_error "Makefile.static文件不存在"
        exit 1
    fi
    
    # 执行构建
    print_info "执行make $target..."
    if make -f Makefile.static "$target"; then
        print_success "静态编译完成"
        
        # 显示构建结果
        if [ -f "bin-static/av_linux_client_static" ]; then
            print_info "构建结果:"
            echo "  二进制文件: bin-static/av_linux_client_static"
            echo "  文件大小: $(du -h bin-static/av_linux_client_static | cut -f1)"
            echo "  文件类型: $(file bin-static/av_linux_client_static)"
        fi
    else
        print_error "静态编译失败"
        exit 1
    fi
}

# 验证静态链接
verify_static() {
    print_info "验证静态链接..."
    if [ -f "bin-static/av_linux_client_static" ]; then
        make -f Makefile.static verify
    else
        print_error "静态二进制文件不存在，请先构建"
        exit 1
    fi
}

# 解析命令行参数
CLEAN=false
DEBUG=false
RELEASE=false
MINIMAL=false
VERIFY=false
INSTALL=false
UNINSTALL=false
CHECK_DEPS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -d|--debug)
            DEBUG=true
            shift
            ;;
        -r|--release)
            RELEASE=true
            shift
            ;;
        -m|--minimal)
            MINIMAL=true
            shift
            ;;
        -v|--verify)
            VERIFY=true
            shift
            ;;
        -i|--install)
            INSTALL=true
            shift
            ;;
        -u|--uninstall)
            UNINSTALL=true
            shift
            ;;
        --check-deps)
            CHECK_DEPS=true
            shift
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行操作
print_info "Linux客户端静态编译构建脚本启动"

# 检查系统依赖
check_system_deps

# 检查项目依赖
if [ "$CHECK_DEPS" = true ]; then
    make -f Makefile.static check-deps
fi

# 清理
if [ "$CLEAN" = true ]; then
    print_info "清理构建文件..."
    make -f Makefile.static clean
    print_success "清理完成"
fi

# 卸载
if [ "$UNINSTALL" = true ]; then
    print_info "卸载程序..."
    make -f Makefile.static uninstall
    print_success "卸载完成"
fi

# 构建
if [ "$DEBUG" = true ]; then
    build_static "debug"
elif [ "$RELEASE" = true ]; then
    build_static "release"
elif [ "$MINIMAL" = true ]; then
    build_static "minimal"
else
    # 如果没有指定其他操作，执行默认构建
    if [ "$CLEAN" = false ] && [ "$UNINSTALL" = false ] && [ "$CHECK_DEPS" = false ]; then
        build_static "all"
    fi
fi

# 验证
if [ "$VERIFY" = true ]; then
    verify_static
fi

# 安装
if [ "$INSTALL" = true ]; then
    print_info "安装程序..."
    make -f Makefile.static install
    print_success "安装完成"
fi

print_success "所有操作完成"