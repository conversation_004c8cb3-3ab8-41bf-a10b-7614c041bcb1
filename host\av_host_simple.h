#ifndef AV_HOST_SIMPLE_H
#define AV_HOST_SIMPLE_H

#include "../common/av_common.h"
#include <stdbool.h>
#include <stdint.h>
#include <pthread.h>

/* 模拟Xen类型定义 */
typedef struct xc_interface xc_interface;
typedef struct xs_handle xs_handle;
typedef struct xengnttab_handle xengnttab_handle;
typedef uint32_t xs_transaction_t;
typedef uint16_t domid_t;
typedef uint32_t grant_ref_t;

/* 模拟xc_domaininfo_t结构 */
typedef struct {
    domid_t domain;
    uint32_t flags;
    uint64_t tot_pages;
    uint32_t max_vcpu_id;
} xc_domaininfo_t;

/* 模拟Xen标志定义 */
#define XEN_DOMINF_dying     (1<<0)
#define XEN_DOMINF_shutdown  (1<<2)
#define XEN_DOMINF_running   (1<<6)

/* 宿主机服务结构体 */
typedef struct {
    xc_interface* xc_handle;        /* libxc句柄 */
    struct xs_handle* xs_handle;    /* XenStore句柄 */
    av_context_t* contexts;         /* 虚拟机上下文数组 */
    size_t context_count;           /* 上下文数量 */
    pthread_t monitor_thread;       /* 监听线程 */
    int shutdown_requested;         /* 关闭请求标志 */
    pthread_mutex_t context_mutex;  /* 上下文访问互斥锁 */
} av_host_service_t;

/* 模拟Xen函数声明 */
xc_interface* xc_interface_open(void* logger, void* dombuild_logger, unsigned open_flags);
int xc_interface_close(xc_interface* xch);
int xc_domain_getinfolist(xc_interface* xch, domid_t first_domain, unsigned int max_domains, xc_domaininfo_t* info);

struct xs_handle* xs_open(unsigned long flags);
void xs_close(struct xs_handle* xsh);
bool xs_mkdir(struct xs_handle* h, xs_transaction_t t, const char* path);

xengnttab_handle* xengnttab_open(void* logger, unsigned open_flags);
int xengnttab_close(xengnttab_handle* xgt);

/* 函数声明 */
int av_validate_environment(void);
int av_host_service_init(av_host_service_t* service);
int av_host_service_start(av_host_service_t* service);
int av_host_service_stop(av_host_service_t* service);
void av_host_service_cleanup(av_host_service_t* service);
int av_add_vm_context(av_host_service_t* service, const char* vm_uuid);
int av_test_shared_memory(av_host_service_t* service);
int av_xenstore_health_check(av_host_service_t* service);
int av_response_health_check(av_host_service_t* service);
int av_list_running_vms(av_host_service_t* service, domid_t** domids, int* count);

/* 统计和资源管理函数 */
void av_stats_init(void);
void av_stats_report(void);
void av_response_stats_report(void);
void av_resource_report(void);
int av_resource_manager_init(void);
void av_resource_manager_cleanup(void);
int av_set_resource_limits(void);
void av_emergency_cleanup(void);
int av_install_signal_handlers(void);
void av_restore_signal_handlers(void);

/* XenStore统计函数 */
void av_xenstore_stats_init(void);
void av_xenstore_stats_record_read(size_t bytes, int success);
void av_xenstore_stats_record_write(size_t bytes, int success);
void av_xenstore_stats_report(void);

#endif /* AV_HOST_SIMPLE_H */