#!/bin/bash

# XenServer防病毒主机服务修复验证脚本

echo "=== XenServer Antivirus Host Service Fix Verification ==="
echo

# 检查运行环境
echo "1. 检查运行环境..."

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "❌ 错误：必须以root权限运行此脚本"
    echo "   解决方案：sudo $0"
    exit 1
else
    echo "✅ 以root权限运行"
fi

# 检查Xen环境
echo "2. 检查Xen环境..."

if [ ! -d "/proc/xen" ]; then
    echo "❌ 错误：/proc/xen 不存在"
    echo "   可能原因：不在Xen Dom0环境中运行"
    echo "   解决方案：确保在XenServer宿主机上运行"
else
    echo "✅ /proc/xen 存在"
fi

if [ ! -c "/dev/xen/xenstore" ]; then
    echo "❌ 错误：/dev/xen/xenstore 不存在"
    echo "   可能原因：XenStore守护进程未运行"
    echo "   解决方案：systemctl start xenstored"
else
    echo "✅ /dev/xen/xenstore 存在"
fi

if [ ! -c "/dev/xen/privcmd" ]; then
    echo "❌ 错误：/dev/xen/privcmd 不存在"
    echo "   可能原因：Xen hypervisor未正确加载"
else
    echo "✅ /dev/xen/privcmd 存在"
fi

# 检查权限
echo "3. 检查设备权限..."

if [ -r "/dev/xen/privcmd" ] && [ -w "/dev/xen/privcmd" ]; then
    echo "✅ /dev/xen/privcmd 可读写"
else
    echo "❌ 错误：/dev/xen/privcmd 权限不足"
    echo "   解决方案：chmod 666 /dev/xen/privcmd 或将用户加入xen组"
fi

# 检查是否在Dom0中
echo "4. 检查域状态..."

if [ -f "/proc/xen/domid" ]; then
    DOMID=$(cat /proc/xen/domid 2>/dev/null)
    if [ "$DOMID" = "0" ]; then
        echo "✅ 运行在Dom0中 (域ID: $DOMID)"
    else
        echo "❌ 错误：不在Dom0中运行 (当前域ID: $DOMID)"
        echo "   解决方案：在XenServer宿主机上运行，而不是在虚拟机中"
    fi
else
    echo "❌ 警告：无法确定域ID"
fi

# 检查系统资源
echo "5. 检查系统资源..."

# 内存检查
TOTAL_MEM=$(grep MemTotal /proc/meminfo | awk '{print $2}')
AVAIL_MEM=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
MEM_USAGE=$((($TOTAL_MEM - $AVAIL_MEM) * 100 / $TOTAL_MEM))

echo "   内存使用率: ${MEM_USAGE}%"
if [ $MEM_USAGE -gt 90 ]; then
    echo "❌ 警告：内存使用率过高 (${MEM_USAGE}%)"
else
    echo "✅ 内存使用率正常"
fi

# 编译测试
echo "6. 编译测试..."

cd "$(dirname "$0")"

if [ -f "host/Makefile" ]; then
    echo "   尝试编译host程序..."
    cd host
    if make clean && make; then
        echo "✅ 编译成功"
        
        # 信号处理测试
        echo "7. 测试信号处理..."
        echo "   启动程序进行信号测试（5秒后自动终止）..."
        
        # 启动程序并获取PID
        timeout 10s ./xenserver-antivirus-host --test-mode &
        HOST_PID=$!
        
        sleep 2
        
        # 发送SIGINT信号
        echo "   发送SIGINT信号..."
        kill -INT $HOST_PID 2>/dev/null
        
        # 等待程序退出
        sleep 3
        
        if kill -0 $HOST_PID 2>/dev/null; then
            echo "❌ 程序未响应SIGINT信号"
            kill -KILL $HOST_PID 2>/dev/null
        else
            echo "✅ 程序正确响应SIGINT信号"
        fi
        
    else
        echo "❌ 编译失败"
        echo "   检查依赖库是否安装：libxenctrl-dev, libxenstore-dev"
    fi
    cd ..
else
    echo "❌ 未找到Makefile"
fi

echo
echo "=== 修复建议 ==="
echo

echo "针对xc_domain_getinfolist权限问题："
echo "1. 确保以root权限运行程序"
echo "2. 检查用户是否在xen组中：usermod -a -G xen \$USER"
echo "3. 确保在XenServer Dom0环境中运行"
echo "4. 检查xenstored服务状态：systemctl status xenstored"
echo "5. 检查设备权限：ls -la /dev/xen/"
echo

echo "针对Ctrl+C无法终止问题："
echo "1. 使用改进的信号处理机制"
echo "2. 使用可中断的nanosleep替代sleep"
echo "3. 在所有循环中检查shutdown标志"
echo "4. 如果程序仍无响应，可以连续按3次Ctrl+C强制退出"
echo "5. 或使用：kill -TERM \$PID"
echo

echo "=== 验证完成 ==="