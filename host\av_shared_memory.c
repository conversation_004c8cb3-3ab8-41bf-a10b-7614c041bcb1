#ifdef SIMPLE_BUILD
#include "av_host_simple.h"
#else
#include "av_host.h"
#endif
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <sys/mman.h>
#include <fcntl.h>

/* 创建共享内存 */
int av_create_shared_memory(av_host_service_t* service, size_t size, grant_ref_t* grant_ref) {
    if (!service || !grant_ref || size == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_create_shared_memory");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "libxc handle not initialized");
        return AV_ERROR_NOT_INITIALIZED;
    }

    av_log(AV_LOG_DEBUG, "Creating shared memory of size %zu bytes", size);

    /* 计算需要的页面数量 */
    size_t page_size = getpagesize();
    size_t num_pages = (size + page_size - 1) / page_size;
    
    av_log(AV_LOG_DEBUG, "Page size: %zu, Required pages: %zu", page_size, num_pages);

    /* 分配物理页面 */
    xen_pfn_t* pfn_list = malloc(num_pages * sizeof(xen_pfn_t));
    if (!pfn_list) {
        av_log(AV_LOG_ERROR, "Failed to allocate memory for PFN list");
        return AV_ERROR_MEMORY_ALLOC;
    }

    /* 使用xc_domain_populate_physmap分配页面 */
    int ret = xc_domain_populate_physmap_exact(service->xc_handle, 0, num_pages, 0, 0, pfn_list);
    if (ret != 0) {
        av_log(AV_LOG_ERROR, "Failed to populate physmap: %s", strerror(errno));
        free(pfn_list);
        return AV_ERROR_XEN_INTERFACE;
    }

    av_log(AV_LOG_DEBUG, "Physical pages allocated successfully");

    /* 创建grant reference */
    grant_ref_t gref;
    ret = xc_gnttab_grant_foreign_access(service->xc_handle, 0, pfn_list[0], 0);
    if (ret < 0) {
        av_log(AV_LOG_ERROR, "Failed to create grant reference: %s", strerror(errno));
        free(pfn_list);
        return AV_ERROR_XEN_INTERFACE;
    }

    gref = ret;
    *grant_ref = gref;

    free(pfn_list);

    av_log(AV_LOG_INFO, "Shared memory created successfully, grant reference: %u", gref);
    return AV_SUCCESS;
}

/* 映射共享内存 */
void* av_map_shared_memory(av_host_service_t* service, grant_ref_t grant_ref, size_t size) {
    if (!service || grant_ref == 0 || size == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_map_shared_memory");
        return NULL;
    }

    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "libxc handle not initialized");
        return NULL;
    }

    av_log(AV_LOG_DEBUG, "Mapping shared memory, grant reference: %u, size: %zu", grant_ref, size);

    /* 打开gnttab设备 */
    xengnttab_handle* gnt_handle = xengnttab_open(NULL, 0);
    if (!gnt_handle) {
        av_log(AV_LOG_ERROR, "Failed to open gnttab handle: %s", strerror(errno));
        return NULL;
    }

    /* 映射grant reference */
    void* mapped_addr = xengnttab_map_grant_ref(gnt_handle, 0, grant_ref, PROT_READ | PROT_WRITE);
    if (mapped_addr == MAP_FAILED || !mapped_addr) {
        av_log(AV_LOG_ERROR, "Failed to map grant reference %u: %s", grant_ref, strerror(errno));
        xengnttab_close(gnt_handle);
        return NULL;
    }

    av_log(AV_LOG_DEBUG, "Shared memory mapped successfully at address %p", mapped_addr);

    /* 初始化共享内存内容 */
    memset(mapped_addr, 0, size);

    /* 设置内存布局标识 */
    av_shared_memory_layout_t* layout = (av_shared_memory_layout_t*)mapped_addr;
    memset(layout->command, 0, sizeof(layout->command));
    memset(layout->reserved, 0, sizeof(layout->reserved));

    av_log(AV_LOG_INFO, "Shared memory mapped and initialized at %p", mapped_addr);
    return mapped_addr;
}

/* 清理共享内存 */
int av_cleanup_shared_memory(av_host_service_t* service, void* addr, grant_ref_t grant_ref, size_t size) {
    if (!service || !addr || grant_ref == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_cleanup_shared_memory");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_DEBUG, "Cleaning up shared memory at %p, grant reference: %u", addr, grant_ref);

    int ret = AV_SUCCESS;

    /* 取消内存映射 */
    if (munmap(addr, size) != 0) {
        av_log(AV_LOG_ERROR, "Failed to unmap shared memory: %s", strerror(errno));
        ret = AV_ERROR_XEN_INTERFACE;
    } else {
        av_log(AV_LOG_DEBUG, "Shared memory unmapped successfully");
    }

    /* 释放grant reference */
    if (service->xc_handle) {
        int gnt_ret = xc_gnttab_end_foreign_access(service->xc_handle, grant_ref);
        if (gnt_ret != 0) {
            av_log(AV_LOG_ERROR, "Failed to end foreign access for grant %u: %s", 
                   grant_ref, strerror(errno));
            ret = AV_ERROR_XEN_INTERFACE;
        } else {
            av_log(AV_LOG_DEBUG, "Grant reference %u released successfully", grant_ref);
        }
    }

    av_log(AV_LOG_INFO, "Shared memory cleanup completed");
    return ret;
}

/* 验证共享内存内容 */
int av_validate_shared_memory(void* addr, size_t size) {
    if (!addr || size < sizeof(av_shared_memory_layout_t)) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_validate_shared_memory");
        return AV_ERROR_INVALID_PARAM;
    }

    av_shared_memory_layout_t* layout = (av_shared_memory_layout_t*)addr;

    /* 检查命令字段是否为有效的字符串 */
    if (strnlen(layout->command, AV_COMMAND_SIZE) == AV_COMMAND_SIZE) {
        av_log(AV_LOG_WARN, "Command field may not be null-terminated");
        return AV_ERROR_PROTOCOL;
    }

    av_log(AV_LOG_DEBUG, "Shared memory validation successful");
    return AV_SUCCESS;
}

/* 读取共享内存命令 */
int av_read_shared_memory_command(void* addr, char* command, size_t command_size) {
    if (!addr || !command || command_size == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_read_shared_memory_command");
        return AV_ERROR_INVALID_PARAM;
    }

    av_shared_memory_layout_t* layout = (av_shared_memory_layout_t*)addr;

    /* 安全复制命令字符串 */
    size_t copy_size = (command_size - 1 < AV_COMMAND_SIZE) ? command_size - 1 : AV_COMMAND_SIZE;
    memcpy(command, layout->command, copy_size);
    command[copy_size] = '\0';

    /* 确保字符串以null结尾 */
    for (size_t i = 0; i < copy_size; i++) {
        if (layout->command[i] == '\0') {
            command[i] = '\0';
            break;
        }
    }

    av_log(AV_LOG_DEBUG, "Read command from shared memory: '%s'", command);
    return AV_SUCCESS;
}

/* 写入共享内存命令 */
int av_write_shared_memory_command(void* addr, const char* command) {
    if (!addr || !command) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_write_shared_memory_command");
        return AV_ERROR_INVALID_PARAM;
    }

    size_t command_len = strlen(command);
    if (command_len >= AV_COMMAND_SIZE) {
        av_log(AV_LOG_ERROR, "Command too long: %zu bytes (max: %d)", command_len, AV_COMMAND_SIZE - 1);
        return AV_ERROR_INVALID_PARAM;
    }

    av_shared_memory_layout_t* layout = (av_shared_memory_layout_t*)addr;

    /* 清空命令字段 */
    memset(layout->command, 0, AV_COMMAND_SIZE);

    /* 复制新命令 */
    memcpy(layout->command, command, command_len);

    av_log(AV_LOG_DEBUG, "Wrote command to shared memory: '%s'", command);
    return AV_SUCCESS;
}

/* 检查共享内存是否有新命令 */
int av_check_shared_memory_command(void* addr, const char* expected_command) {
    if (!addr || !expected_command) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_check_shared_memory_command");
        return AV_ERROR_INVALID_PARAM;
    }

    char current_command[AV_COMMAND_SIZE];
    int ret = av_read_shared_memory_command(addr, current_command, sizeof(current_command));
    if (ret != AV_SUCCESS) {
        return ret;
    }

    /* 比较命令 */
    if (strcmp(current_command, expected_command) == 0) {
        av_log(AV_LOG_DEBUG, "Found expected command: '%s'", expected_command);
        return AV_SUCCESS;
    }

    return AV_ERROR_PROTOCOL;
}

/* 清空共享内存命令 */
int av_clear_shared_memory_command(void* addr) {
    if (!addr) {
        av_log(AV_LOG_ERROR, "Invalid address for av_clear_shared_memory_command");
        return AV_ERROR_INVALID_PARAM;
    }

    av_shared_memory_layout_t* layout = (av_shared_memory_layout_t*)addr;
    memset(layout->command, 0, AV_COMMAND_SIZE);

    av_log(AV_LOG_DEBUG, "Cleared shared memory command");
    return AV_SUCCESS;
}

/* 获取共享内存统计信息 */
int av_get_shared_memory_stats(av_host_service_t* service, av_shared_memory_stats_t* stats) {
    if (!service || !stats) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_get_shared_memory_stats");
        return AV_ERROR_INVALID_PARAM;
    }

    memset(stats, 0, sizeof(av_shared_memory_stats_t));

    /* 统计当前活跃的共享内存区域 */
    pthread_mutex_lock(&service->context_mutex);

    for (size_t i = 0; i < service->context_count; i++) {
        av_context_t* ctx = &service->contexts[i];
        if (ctx->mapped_addr && ctx->shm_id != 0) {
            stats->active_regions++;
            stats->total_size += AV_SHARED_MEMORY_SIZE;
        }
    }

    pthread_mutex_unlock(&service->context_mutex);

    av_log(AV_LOG_DEBUG, "Shared memory stats - Active regions: %u, Total size: %zu bytes",
           stats->active_regions, stats->total_size);

    return AV_SUCCESS;
}

/* 测试共享内存功能 */
int av_test_shared_memory(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_INFO, "Starting shared memory functionality test");

    grant_ref_t grant_ref = 0;
    void* mapped_addr = NULL;
    int ret = AV_SUCCESS;

    /* 创建共享内存 */
    ret = av_create_shared_memory(service, AV_SHARED_MEMORY_SIZE, &grant_ref);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to create shared memory: %s", av_error_string(ret));
        return ret;
    }

    /* 映射共享内存 */
    mapped_addr = av_map_shared_memory(service, grant_ref, AV_SHARED_MEMORY_SIZE);
    if (!mapped_addr) {
        av_log(AV_LOG_ERROR, "Failed to map shared memory");
        ret = AV_ERROR_XEN_INTERFACE;
        goto cleanup;
    }

    /* 验证共享内存 */
    ret = av_validate_shared_memory(mapped_addr, AV_SHARED_MEMORY_SIZE);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Shared memory validation failed: %s", av_error_string(ret));
        goto cleanup;
    }

    /* 测试命令读写 */
    ret = av_write_shared_memory_command(mapped_addr, AV_SCAN_REQUEST);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to write command: %s", av_error_string(ret));
        goto cleanup;
    }

    ret = av_check_shared_memory_command(mapped_addr, AV_SCAN_REQUEST);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Command verification failed: %s", av_error_string(ret));
        goto cleanup;
    }

    /* 清空命令 */
    ret = av_clear_shared_memory_command(mapped_addr);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to clear command: %s", av_error_string(ret));
        goto cleanup;
    }

    av_log(AV_LOG_INFO, "Shared memory functionality test completed successfully");

cleanup:
    /* 清理资源 */
    if (mapped_addr) {
        int cleanup_ret = av_cleanup_shared_memory(service, mapped_addr, grant_ref, AV_SHARED_MEMORY_SIZE);
        if (cleanup_ret != AV_SUCCESS) {
            av_log(AV_LOG_WARN, "Shared memory cleanup failed: %s", av_error_string(cleanup_ret));
        }
    }

    return ret;
}