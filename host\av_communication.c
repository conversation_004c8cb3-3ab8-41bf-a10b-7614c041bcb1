#ifdef SIMPLE_BUILD
#include "av_host_simple.h"
#else
#include "av_host.h"
#endif
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <pthread.h>
#include <unistd.h>
#include <time.h>

/* 监听共享内存中的扫描请求 */
int av_monitor_scan_requests(av_host_service_t* service, void* shm_addr) {
    if (!service || !shm_addr) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_monitor_scan_requests");
        return AV_ERROR_INVALID_PARAM;
    }

    av_shared_memory_layout_t* shm_layout = (av_shared_memory_layout_t*)shm_addr;
    
    av_log(AV_LOG_DEBUG, "Starting to monitor shared memory for scan requests");

    /* 持续监听共享内存前16个字节 */
    while (!service->shutdown_requested) {
        /* 检查命令字段是否包含扫描请求 */
        if (av_check_shared_memory_command(shm_addr, AV_SCAN_REQUEST) == AV_SUCCESS) {
            av_log(AV_LOG_INFO, "Detected scan request in shared memory");
            
            /* 发送响应 */
            int ret = av_send_scan_response(service, shm_addr, AV_SCAN_RESPONSE);
            if (ret != AV_SUCCESS) {
                av_log(AV_LOG_ERROR, "Failed to send scan response: %s", av_error_string(ret));
                return ret;
            }
            
            /* 清除请求命令，避免重复处理 */
            av_clear_shared_memory_command(shm_addr);
            
            av_log(AV_LOG_DEBUG, "Scan request processed successfully");
        }
        
        /* 短暂休眠，避免过度占用CPU */
        usleep(10000); /* 10ms */
    }

    av_log(AV_LOG_DEBUG, "Stopped monitoring shared memory");
    return AV_SUCCESS;
}

/* 响应统计信息 */
typedef struct {
    uint64_t total_responses;
    uint64_t successful_responses;
    uint64_t failed_responses;
    uint64_t timeout_responses;
    uint64_t total_response_time_ms;
    uint64_t max_response_time_ms;
    uint64_t min_response_time_ms;
    time_t start_time;
} av_response_stats_t;

static av_response_stats_t g_response_stats = {0};

/* 初始化响应统计 */
void av_response_stats_init(void) {
    memset(&g_response_stats, 0, sizeof(g_response_stats));
    g_response_stats.start_time = time(NULL);
    g_response_stats.min_response_time_ms = UINT64_MAX;
    av_log(AV_LOG_INFO, "Response statistics initialized");
}

/* 记录响应统计 */
static void av_record_response_stats(int success, uint64_t response_time_ms) {
    g_response_stats.total_responses++;
    
    if (success) {
        g_response_stats.successful_responses++;
        g_response_stats.total_response_time_ms += response_time_ms;
        
        if (response_time_ms > g_response_stats.max_response_time_ms) {
            g_response_stats.max_response_time_ms = response_time_ms;
        }
        
        if (response_time_ms < g_response_stats.min_response_time_ms) {
            g_response_stats.min_response_time_ms = response_time_ms;
        }
        
        if (response_time_ms > 100) {
            g_response_stats.timeout_responses++;
        }
    } else {
        g_response_stats.failed_responses++;
    }
}

/* 报告响应统计 */
void av_response_stats_report(void) {
    time_t current_time = time(NULL);
    double uptime = difftime(current_time, g_response_stats.start_time);
    
    av_log(AV_LOG_INFO, "=== Response Statistics ===");
    av_log(AV_LOG_INFO, "Uptime: %.0f seconds", uptime);
    av_log(AV_LOG_INFO, "Total responses: %lu", g_response_stats.total_responses);
    av_log(AV_LOG_INFO, "Successful responses: %lu", g_response_stats.successful_responses);
    av_log(AV_LOG_INFO, "Failed responses: %lu", g_response_stats.failed_responses);
    av_log(AV_LOG_INFO, "Timeout responses (>100ms): %lu", g_response_stats.timeout_responses);
    
    if (g_response_stats.successful_responses > 0) {
        double avg_response_time = (double)g_response_stats.total_response_time_ms / g_response_stats.successful_responses;
        double success_rate = (double)g_response_stats.successful_responses * 100.0 / g_response_stats.total_responses;
        double timeout_rate = (double)g_response_stats.timeout_responses * 100.0 / g_response_stats.successful_responses;
        
        av_log(AV_LOG_INFO, "Average response time: %.2f ms", avg_response_time);
        av_log(AV_LOG_INFO, "Max response time: %lu ms", g_response_stats.max_response_time_ms);
        av_log(AV_LOG_INFO, "Min response time: %lu ms", g_response_stats.min_response_time_ms);
        av_log(AV_LOG_INFO, "Success rate: %.2f%%", success_rate);
        av_log(AV_LOG_INFO, "Timeout rate: %.2f%%", timeout_rate);
    }
    
    if (uptime > 0) {
        double responses_per_second = g_response_stats.total_responses / uptime;
        av_log(AV_LOG_INFO, "Responses per second: %.2f", responses_per_second);
    }
}

/* 发送扫描响应到共享内存 */
int av_send_scan_response(av_host_service_t* service, void* shm_addr, const char* response) {
    if (!service || !shm_addr || !response) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_send_scan_response");
        av_record_response_stats(0, 0);
        return AV_ERROR_INVALID_PARAM;
    }

    /* 记录开始时间，确保在100毫秒内响应 */
    struct timespec start_time, current_time;
    clock_gettime(CLOCK_MONOTONIC, &start_time);

    av_log(AV_LOG_DEBUG, "Sending scan response: %s", response);

    /* 验证共享内存有效性 */
    int ret = av_validate_shared_memory(shm_addr, AV_SHARED_MEMORY_SIZE);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Invalid shared memory for response: %s", av_error_string(ret));
        av_record_response_stats(0, 0);
        return ret;
    }

    /* 写入响应到共享内存 */
    ret = av_write_shared_memory_command(shm_addr, response);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to write response to shared memory: %s", av_error_string(ret));
        av_record_response_stats(0, 0);
        return ret;
    }

    /* 检查响应时间 */
    clock_gettime(CLOCK_MONOTONIC, &current_time);
    uint64_t response_time_ms = (current_time.tv_sec - start_time.tv_sec) * 1000 +
                               (current_time.tv_nsec - start_time.tv_nsec) / 1000000;

    /* 记录统计信息 */
    av_record_response_stats(1, response_time_ms);

    /* 性能检查和日志记录 */
    if (response_time_ms > 100) {
        av_log(AV_LOG_WARN, "Response time exceeded 100ms: %lu ms (PERFORMANCE ISSUE)", response_time_ms);
    } else if (response_time_ms > 50) {
        av_log(AV_LOG_INFO, "Response sent in %lu ms (approaching limit)", response_time_ms);
    } else {
        av_log(AV_LOG_DEBUG, "Response sent in %lu ms (good performance)", response_time_ms);
    }

    /* 额外的状态跟踪日志 */
    av_log(AV_LOG_DEBUG, "Response status: SUCCESS, Command: %s, Time: %lu ms", response, response_time_ms);

    return AV_SUCCESS;
}

/* 监听线程函数 */
void* av_monitor_thread_func(void* arg) {
    av_host_service_t* service = (av_host_service_t*)arg;
    
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter in monitor thread");
        return NULL;
    }

    av_log(AV_LOG_INFO, "Monitor thread started");

    /* 初始化响应统计 */
    av_response_stats_init();

    int health_check_counter = 0;
    int stats_report_counter = 0;

    /* 监听所有活跃的VM上下文 */
    while (!service->shutdown_requested) {
        /* 使用批量处理提高效率 */
        av_process_batch_responses(service);
        
        /* 定期健康检查 (每30秒) */
        if (++health_check_counter >= 3000) { /* 3000 * 10ms = 30s */
            av_response_health_check(service);
            av_optimize_response_performance(service);
            health_check_counter = 0;
        }
        
        /* 定期统计报告 (每5分钟) */
        if (++stats_report_counter >= 30000) { /* 30000 * 10ms = 5min */
            av_response_stats_report();
            stats_report_counter = 0;
        }
        
        /* 短暂休眠 */
        usleep(10000); /* 10ms */
    }

    /* 最终统计报告 */
    av_response_stats_report();
    av_log(AV_LOG_INFO, "Monitor thread stopped");
    return NULL;
}

/* 启动监听线程 */
int av_start_monitor_thread(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    if (service->monitor_thread != 0) {
        av_log(AV_LOG_WARN, "Monitor thread already running");
        return AV_ERROR_ALREADY_EXISTS;
    }

    av_log(AV_LOG_INFO, "Starting monitor thread...");

    int ret = pthread_create(&service->monitor_thread, NULL, av_monitor_thread_func, service);
    if (ret != 0) {
        av_log(AV_LOG_ERROR, "Failed to create monitor thread: %s", strerror(ret));
        return AV_ERROR_MEMORY_ALLOC;
    }

    av_log(AV_LOG_INFO, "Monitor thread started successfully");
    return AV_SUCCESS;
}

/* 停止监听线程 */
int av_stop_monitor_thread(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    if (service->monitor_thread == 0) {
        av_log(AV_LOG_DEBUG, "Monitor thread not running");
        return AV_SUCCESS;
    }

    av_log(AV_LOG_INFO, "Stopping monitor thread...");

    /* 设置关闭标志 */
    service->shutdown_requested = 1;

    /* 等待线程结束 */
    int ret = pthread_join(service->monitor_thread, NULL);
    if (ret != 0) {
        av_log(AV_LOG_ERROR, "Failed to join monitor thread: %s", strerror(ret));
        return AV_ERROR_PROTOCOL;
    }

    service->monitor_thread = 0;
    service->shutdown_requested = 0; /* 重置标志 */

    av_log(AV_LOG_INFO, "Monitor thread stopped successfully");
    return AV_SUCCESS;
}

/* 启用VM上下文的监听 */
int av_enable_vm_monitoring(av_host_service_t* service, const char* vm_uuid) {
    if (!service || !vm_uuid) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_enable_vm_monitoring");
        return AV_ERROR_INVALID_PARAM;
    }

    av_context_t* ctx = av_find_vm_context(service, vm_uuid);
    if (!ctx) {
        av_log(AV_LOG_ERROR, "VM context not found: %s", vm_uuid);
        return AV_ERROR_VM_NOT_FOUND;
    }

    if (!ctx->mapped_addr) {
        av_log(AV_LOG_ERROR, "Shared memory not mapped for VM %s", vm_uuid);
        return AV_ERROR_NOT_INITIALIZED;
    }

    pthread_mutex_lock(&service->context_mutex);
    ctx->monitoring_active = 1;
    pthread_mutex_unlock(&service->context_mutex);

    av_log(AV_LOG_INFO, "Enabled monitoring for VM %s", vm_uuid);
    return AV_SUCCESS;
}

/* 禁用VM上下文的监听 */
int av_disable_vm_monitoring(av_host_service_t* service, const char* vm_uuid) {
    if (!service || !vm_uuid) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_disable_vm_monitoring");
        return AV_ERROR_INVALID_PARAM;
    }

    av_context_t* ctx = av_find_vm_context(service, vm_uuid);
    if (!ctx) {
        av_log(AV_LOG_ERROR, "VM context not found: %s", vm_uuid);
        return AV_ERROR_VM_NOT_FOUND;
    }

    pthread_mutex_lock(&service->context_mutex);
    ctx->monitoring_active = 0;
    pthread_mutex_unlock(&service->context_mutex);

    av_log(AV_LOG_INFO, "Disabled monitoring for VM %s", vm_uuid);
    return AV_SUCCESS;
}

/* 响应类型定义已在头文件中 */

/* 响应上下文结构 */
typedef struct {
    av_response_type_t type;
    char vm_uuid[AV_UUID_STRING_LENGTH];
    struct timespec request_time;
    struct timespec response_time;
    int success;
    char error_message[256];
} av_response_context_t;

/* 发送带类型的扫描响应 */
int av_send_typed_scan_response(av_host_service_t* service, void* shm_addr, 
                               const char* vm_uuid, av_response_type_t type) {
    if (!service || !shm_addr || !vm_uuid) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_send_typed_scan_response");
        return AV_ERROR_INVALID_PARAM;
    }

    av_response_context_t ctx;
    memset(&ctx, 0, sizeof(ctx));
    
    /* 初始化响应上下文 */
    ctx.type = type;
    strncpy(ctx.vm_uuid, vm_uuid, sizeof(ctx.vm_uuid) - 1);
    clock_gettime(CLOCK_MONOTONIC, &ctx.request_time);

    const char* response_cmd;
    switch (type) {
        case AV_RESPONSE_TYPE_ACK:
            response_cmd = AV_SCAN_RESPONSE;
            break;
        case AV_RESPONSE_TYPE_DATA:
            response_cmd = "SCAN_DATA";
            break;
        case AV_RESPONSE_TYPE_ERROR:
            response_cmd = "SCAN_ERR";
            break;
        case AV_RESPONSE_TYPE_BUSY:
            response_cmd = "SCAN_BUSY";
            break;
        default:
            av_log(AV_LOG_ERROR, "Unknown response type: %d", type);
            return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_DEBUG, "Sending typed response to VM %s: %s", vm_uuid, response_cmd);

    /* 发送响应 */
    int ret = av_send_scan_response(service, shm_addr, response_cmd);
    
    /* 记录响应时间 */
    clock_gettime(CLOCK_MONOTONIC, &ctx.response_time);
    ctx.success = (ret == AV_SUCCESS);

    /* 计算响应时间 */
    uint64_t response_time_ms = (ctx.response_time.tv_sec - ctx.request_time.tv_sec) * 1000 +
                               (ctx.response_time.tv_nsec - ctx.request_time.tv_nsec) / 1000000;

    /* 详细的状态跟踪日志 */
    if (ctx.success) {
        av_log(AV_LOG_INFO, "Response sent successfully - VM: %s, Type: %d, Time: %lu ms", 
               vm_uuid, type, response_time_ms);
    } else {
        av_log(AV_LOG_ERROR, "Response failed - VM: %s, Type: %d, Error: %s", 
               vm_uuid, type, av_error_string(ret));
        snprintf(ctx.error_message, sizeof(ctx.error_message), "%s", av_error_string(ret));
    }

    return ret;
}

/* 批量响应处理 */
int av_process_batch_responses(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    int processed_count = 0;
    int success_count = 0;
    struct timespec batch_start, batch_end;
    
    clock_gettime(CLOCK_MONOTONIC, &batch_start);
    
    av_log(AV_LOG_DEBUG, "Starting batch response processing");

    pthread_mutex_lock(&service->context_mutex);
    
    /* 遍历所有活跃的VM上下文 */
    for (size_t i = 0; i < service->context_count; i++) {
        av_context_t* ctx = &service->contexts[i];
        
        if (!ctx->monitoring_active || !ctx->mapped_addr) {
            continue;
        }

        /* 检查是否有待处理的请求 */
        if (av_check_shared_memory_command(ctx->mapped_addr, AV_SCAN_REQUEST) == AV_SUCCESS) {
            processed_count++;
            
            av_log(AV_LOG_DEBUG, "Processing request from VM %s", ctx->vm_uuid);
            
            /* 发送响应 */
            int ret = av_send_typed_scan_response(service, ctx->mapped_addr, 
                                                ctx->vm_uuid, AV_RESPONSE_TYPE_ACK);
            if (ret == AV_SUCCESS) {
                success_count++;
                /* 清除请求 */
                av_clear_shared_memory_command(ctx->mapped_addr);
            }
        }
    }
    
    pthread_mutex_unlock(&service->context_mutex);
    
    clock_gettime(CLOCK_MONOTONIC, &batch_end);
    
    uint64_t batch_time_ms = (batch_end.tv_sec - batch_start.tv_sec) * 1000 +
                            (batch_end.tv_nsec - batch_start.tv_nsec) / 1000000;

    if (processed_count > 0) {
        av_log(AV_LOG_INFO, "Batch processing completed - Processed: %d, Success: %d, Time: %lu ms", 
               processed_count, success_count, batch_time_ms);
    }

    return AV_SUCCESS;
}

/* 响应健康检查 */
int av_response_health_check(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_DEBUG, "Performing response health check");

    /* 检查响应统计 */
    if (g_response_stats.total_responses > 0) {
        double timeout_rate = (double)g_response_stats.timeout_responses * 100.0 / g_response_stats.total_responses;
        double failure_rate = (double)g_response_stats.failed_responses * 100.0 / g_response_stats.total_responses;
        
        /* 健康状态评估 */
        if (timeout_rate > 10.0) {
            av_log(AV_LOG_WARN, "High timeout rate detected: %.2f%% (threshold: 10%%)", timeout_rate);
        }
        
        if (failure_rate > 5.0) {
            av_log(AV_LOG_WARN, "High failure rate detected: %.2f%% (threshold: 5%%)", failure_rate);
        }
        
        if (g_response_stats.max_response_time_ms > 200) {
            av_log(AV_LOG_WARN, "Maximum response time too high: %lu ms (threshold: 200ms)", 
                   g_response_stats.max_response_time_ms);
        }
    }

    /* 检查活跃的VM上下文 */
    pthread_mutex_lock(&service->context_mutex);
    
    int active_contexts = 0;
    for (size_t i = 0; i < service->context_count; i++) {
        if (service->contexts[i].monitoring_active) {
            active_contexts++;
        }
    }
    
    pthread_mutex_unlock(&service->context_mutex);
    
    av_log(AV_LOG_INFO, "Health check completed - Active contexts: %d", active_contexts);
    
    return AV_SUCCESS;
}

/* 优化响应性能 */
int av_optimize_response_performance(av_host_service_t* service) {
    if (!service) {
        av_log(AV_LOG_ERROR, "Invalid service parameter");
        return AV_ERROR_INVALID_PARAM;
    }

    av_log(AV_LOG_INFO, "Optimizing response performance");

    /* 基于统计数据调整性能参数 */
    if (g_response_stats.total_responses > 100) {
        double avg_response_time = (double)g_response_stats.total_response_time_ms / g_response_stats.successful_responses;
        
        if (avg_response_time > 50.0) {
            av_log(AV_LOG_INFO, "Average response time high (%.2f ms), suggesting optimizations", avg_response_time);
            
            /* 这里可以实现具体的优化策略 */
            /* 例如：调整监听频率、使用内存预分配等 */
        }
    }

    return AV_SUCCESS;
}