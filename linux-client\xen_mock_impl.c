#include "av_linux_client.h"
#include <stdlib.h>
#include <string.h>

#ifndef HAVE_XEN_LIBS
/* 模拟Xen函数实现 */

xc_interface* xc_interface_open(void* logger, void* dombuild_logger, unsigned open_flags) {
    (void)logger; (void)dombuild_logger; (void)open_flags;
    return (xc_interface*)malloc(sizeof(int));  /* 返回模拟句柄 */
}

int xc_interface_close(xc_interface* xch) {
    if (xch) {
        free(xch);
    }
    return 0;
}

void* xc_map_foreign_range(xc_interface* xch, domid_t dom, int size, int prot, unsigned long mfn) {
    (void)xch; (void)dom; (void)prot; (void)mfn;
    return malloc(size);  /* 返回模拟内存映射 */
}

struct xs_handle* xs_open(unsigned long flags) {
    (void)flags;
    return (struct xs_handle*)malloc(sizeof(int));  /* 返回模拟句柄 */
}

void xs_close(struct xs_handle* xsh) {
    if (xsh) {
        free(xsh);
    }
}

char* xs_read(struct xs_handle* xsh, xs_transaction_t t, const char* path, unsigned int* len) {
    (void)xsh; (void)t; (void)path;
    char* result = strdup("12345678-1234-1234-1234-123456789abc");
    if (len) {
        *len = strlen(result);
    }
    return result;
}

bool xs_watch(struct xs_handle* xsh, const char* path, const char* token) {
    (void)xsh; (void)path; (void)token;
    return true;  /* 模拟成功 */
}

int xs_fileno(struct xs_handle* xsh) {
    (void)xsh;
    return 0;  /* 返回模拟文件描述符 */
}

char** xs_read_watch(struct xs_handle* xsh, unsigned int* num) {
    (void)xsh;
    char** result = malloc(2 * sizeof(char*));
    result[0] = strdup("/mock/path");
    result[1] = strdup("mock_token");
    if (num) {
        *num = 2;
    }
    return result;
}

#endif /* !HAVE_XEN_LIBS */