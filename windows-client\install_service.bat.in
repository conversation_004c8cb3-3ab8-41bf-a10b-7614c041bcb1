@echo off
REM XenServer防病毒客户端服务安装脚本

echo Installing XenServer Antivirus Client Service...

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

REM 停止服务（如果正在运行）
sc query "XenServerAntivirusClient" >nul 2>&1
if %errorLevel% equ 0 (
    echo Stopping existing service...
    sc stop "XenServerAntivirusClient"
    timeout /t 5 /nobreak >nul
)

REM 删除现有服务（如果存在）
sc query "XenServerAntivirusClient" >nul 2>&1
if %errorLevel% equ 0 (
    echo Removing existing service...
    sc delete "XenServerAntivirusClient"
    timeout /t 2 /nobreak >nul
)

REM 安装新服务
echo Installing service...
sc create "XenServerAntivirusClient" ^
    binPath= "@CMAKE_BINARY_DIR@\bin\av_windows_client.exe" ^
    DisplayName= "XenServer Antivirus Client Service" ^
    Description= "XenServer agentless antivirus client service" ^
    start= auto

if %errorLevel% neq 0 (
    echo ERROR: Failed to install service.
    pause
    exit /b 1
)

REM 启动服务
echo Starting service...
sc start "XenServerAntivirusClient"

if %errorLevel% neq 0 (
    echo WARNING: Service installed but failed to start.
    echo Check the Windows Event Log for details.
) else (
    echo Service installed and started successfully.
)

echo.
echo Installation complete.
pause