#ifndef TEST_COMMON_H
#define TEST_COMMON_H

#include "../common/av_common.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>

/* 测试框架宏定义 */
#define TEST_ASSERT(condition, message) \
    do { \
        if (!(condition)) { \
            printf("FAIL: %s - %s\n", __func__, message); \
            return -1; \
        } \
    } while(0)

#define TEST_ASSERT_EQUAL(expected, actual, message) \
    do { \
        if ((expected) != (actual)) { \
            printf("FAIL: %s - %s (expected: %d, actual: %d)\n", \
                   __func__, message, (int)(expected), (int)(actual)); \
            return -1; \
        } \
    } while(0)

#define TEST_ASSERT_STRING_EQUAL(expected, actual, message) \
    do { \
        if (strcmp((expected), (actual)) != 0) { \
            printf("FAIL: %s - %s (expected: '%s', actual: '%s')\n", \
                   __func__, message, expected, actual); \
            return -1; \
        } \
    } while(0)

#define RUN_TEST(test_func) \
    do { \
        printf("Running %s... ", #test_func); \
        if (test_func() == 0) { \
            printf("PASS\n"); \
            tests_passed++; \
        } else { \
            tests_failed++; \
        } \
        tests_total++; \
    } while(0)

/* 测试统计变量 */
extern int tests_total;
extern int tests_passed;
extern int tests_failed;

/* 测试工具函数 */
void test_init(void);
void test_summary(void);
int test_create_temp_file(const char* content, char* filename, size_t filename_size);
void test_cleanup_temp_file(const char* filename);

/* 模拟函数声明 */
typedef struct {
    int should_fail;
    int call_count;
    void* return_value;
} mock_function_t;

void mock_reset(mock_function_t* mock);
void mock_set_return_value(mock_function_t* mock, void* value);
void mock_set_should_fail(mock_function_t* mock, int should_fail);

#endif /* TEST_COMMON_H */