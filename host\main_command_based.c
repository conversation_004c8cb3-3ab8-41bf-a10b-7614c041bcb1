/*
 * XenServer宿主机防病毒服务 - 基于系统命令的版本
 * 使用xe/xl命令获取VM信息，避免libxenctrl权限问题
 */

#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <pthread.h>
#include <sys/wait.h>
#include <getopt.h>
#include <time.h>

/* 项目头文件 */
#include "../common/av_common.h"

/* 版本信息 */
#undef AV_VERSION
#define AV_VERSION "1.0.0-cmd"
#define AV_BUILD_INFO "Command-Based (xe/xl)"

/* VM信息结构体 */
typedef struct {
    int domid;
    char name[256];
    char state[32];
    int memory_mb;
    int vcpus;
    char uuid[64];
} vm_info_t;

/* 服务结构体 */
typedef struct {
    pthread_t vm_monitor_thread;
    volatile int shutdown_requested;
    int max_vms;
    int use_xe_command;  /* 1=使用xe命令, 0=使用xl命令 */
} av_host_service_t;

/* 全局变量 */
static av_host_service_t g_service;
static volatile int g_shutdown_requested = 0;
static int g_log_level = AV_LOG_INFO;

/* 执行系统命令并获取输出 */
static char* execute_command(const char* command) {
    FILE* fp;
    char* result = NULL;
    char buffer[1024];
    size_t result_size = 0;
    size_t total_size = 0;
    
    av_log(AV_LOG_DEBUG, "执行命令: %s", command);
    
    fp = popen(command, "r");
    if (fp == NULL) {
        av_log(AV_LOG_ERROR, "无法执行命令: %s", command);
        return NULL;
    }
    
    /* 读取命令输出 */
    while (fgets(buffer, sizeof(buffer), fp) != NULL) {
        size_t buffer_len = strlen(buffer);
        char* new_result = realloc(result, total_size + buffer_len + 1);
        if (new_result == NULL) {
            av_log(AV_LOG_ERROR, "内存分配失败");
            free(result);
            pclose(fp);
            return NULL;
        }
        result = new_result;
        strcpy(result + total_size, buffer);
        total_size += buffer_len;
    }
    
    int status = pclose(fp);
    if (status != 0) {
        av_log(AV_LOG_WARN, "命令执行返回非零状态: %d", status);
    }
    
    return result;
}

/* 解析xe vm-list输出 */
static int parse_xe_vm_list(const char* output, vm_info_t* vms, int max_vms) {
    int vm_count = 0;
    char* output_copy = strdup(output);
    char* line = strtok(output_copy, "\n");
    
    av_log(AV_LOG_DEBUG, "解析xe vm-list输出...");
    
    while (line != NULL && vm_count < max_vms) {
        /* 跳过空行和标题行 */
        if (strlen(line) < 10 || strstr(line, "uuid") != NULL) {
            line = strtok(NULL, "\n");
            continue;
        }
        
        /* 解析VM信息行 */
        char uuid[64] = {0};
        char name[256] = {0};
        char state[32] = {0};
        
        /* xe vm-list的输出格式通常是: uuid ( RO) : xxx */
        if (sscanf(line, "uuid ( RO): %63s", uuid) == 1) {
            /* 获取这个VM的详细信息 */
            char detail_cmd[512];
            snprintf(detail_cmd, sizeof(detail_cmd), 
                    "xe vm-param-get param-name=name-label uuid=%s 2>/dev/null", uuid);
            char* vm_name = execute_command(detail_cmd);
            if (vm_name) {
                /* 移除换行符 */
                char* newline = strchr(vm_name, '\n');
                if (newline) *newline = '\0';
                strncpy(name, vm_name, sizeof(name) - 1);
                free(vm_name);
            }
            
            snprintf(detail_cmd, sizeof(detail_cmd), 
                    "xe vm-param-get param-name=power-state uuid=%s 2>/dev/null", uuid);
            char* vm_state = execute_command(detail_cmd);
            if (vm_state) {
                char* newline = strchr(vm_state, '\n');
                if (newline) *newline = '\0';
                strncpy(state, vm_state, sizeof(state) - 1);
                free(vm_state);
            }
            
            /* 只记录运行中的VM */
            if (strcmp(state, "running") == 0) {
                strncpy(vms[vm_count].uuid, uuid, sizeof(vms[vm_count].uuid) - 1);
                strncpy(vms[vm_count].name, name, sizeof(vms[vm_count].name) - 1);
                strncpy(vms[vm_count].state, state, sizeof(vms[vm_count].state) - 1);
                vms[vm_count].domid = vm_count + 1;  /* xe不直接提供domid */
                vm_count++;
                
                av_log(AV_LOG_DEBUG, "找到运行中的VM: %s (%s)", name, uuid);
            }
        }
        
        line = strtok(NULL, "\n");
    }
    
    free(output_copy);
    av_log(AV_LOG_INFO, "通过xe命令找到 %d 个运行中的VM", vm_count);
    return vm_count;
}

/* 解析xl list输出 */
static int parse_xl_list(const char* output, vm_info_t* vms, int max_vms) {
    int vm_count = 0;
    char* output_copy = strdup(output);
    char* line = strtok(output_copy, "\n");
    
    av_log(AV_LOG_DEBUG, "解析xl list输出...");
    
    while (line != NULL && vm_count < max_vms) {
        /* 跳过标题行 */
        if (strstr(line, "Name") != NULL || strstr(line, "Domain-0") != NULL) {
            line = strtok(NULL, "\n");
            continue;
        }
        
        /* 解析VM信息行 */
        char name[256];
        int domid, memory, vcpus;
        char state[32];
        
        if (sscanf(line, "%255s %d %d %d %31s", name, &domid, &memory, &vcpus, state) >= 4) {
            /* 跳过Domain-0 */
            if (domid == 0) {
                line = strtok(NULL, "\n");
                continue;
            }
            
            strncpy(vms[vm_count].name, name, sizeof(vms[vm_count].name) - 1);
            vms[vm_count].domid = domid;
            vms[vm_count].memory_mb = memory;
            vms[vm_count].vcpus = vcpus;
            strncpy(vms[vm_count].state, state, sizeof(vms[vm_count].state) - 1);
            vm_count++;
            
            av_log(AV_LOG_DEBUG, "找到VM: %s (domid=%d, 内存=%dMB)", name, domid, memory);
        }
        
        line = strtok(NULL, "\n");
    }
    
    free(output_copy);
    av_log(AV_LOG_INFO, "通过xl命令找到 %d 个VM", vm_count);
    return vm_count;
}

/* 使用系统命令获取VM列表 */
static int get_vm_list_via_commands(vm_info_t* vms, int max_vms, int use_xe) {
    char* output;
    int vm_count = 0;
    
    if (use_xe) {
        av_log(AV_LOG_DEBUG, "使用xe命令获取VM列表...");
        output = execute_command("xe vm-list power-state=running 2>/dev/null");
        if (output) {
            vm_count = parse_xe_vm_list(output, vms, max_vms);
            free(output);
        } else {
            av_log(AV_LOG_WARN, "xe命令执行失败，尝试xl命令");
            use_xe = 0;  /* 回退到xl命令 */
        }
    }
    
    if (!use_xe) {
        av_log(AV_LOG_DEBUG, "使用xl命令获取VM列表...");
        output = execute_command("xl list 2>/dev/null");
        if (output) {
            vm_count = parse_xl_list(output, vms, max_vms);
            free(output);
        } else {
            av_log(AV_LOG_ERROR, "xl命令也执行失败");
        }
    }
    
    return vm_count;
}

/* 检测使用哪种命令 */
static int detect_command_type(void) {
    /* 优先检测xe命令（Citrix Hypervisor） */
    char* xe_output = execute_command("xe host-list 2>/dev/null");
    if (xe_output) {
        free(xe_output);
        av_log(AV_LOG_INFO, "检测到Citrix Hypervisor环境，使用xe命令");
        return 1;  /* 使用xe命令 */
    }
    
    /* 检测xl命令（标准Xen） */
    char* xl_output = execute_command("xl info 2>/dev/null");
    if (xl_output) {
        free(xl_output);
        av_log(AV_LOG_INFO, "检测到标准Xen环境，使用xl命令");
        return 0;  /* 使用xl命令 */
    }
    
    av_log(AV_LOG_WARN, "未检测到xe或xl命令，默认使用xl");
    return 0;
}

/* VM监控线程 */
static void* vm_monitor_thread(void* arg) {
    av_host_service_t* service = (av_host_service_t*)arg;
    vm_info_t* vms = malloc(service->max_vms * sizeof(vm_info_t));
    
    if (!vms) {
        av_log(AV_LOG_ERROR, "无法分配VM信息内存");
        return NULL;
    }
    
    av_log(AV_LOG_INFO, "VM监控线程已启动");
    
    while (!service->shutdown_requested) {
        /* 获取VM列表 */
        int vm_count = get_vm_list_via_commands(vms, service->max_vms, service->use_xe_command);
        
        if (vm_count > 0) {
            av_log(AV_LOG_INFO, "当前监控 %d 个VM:", vm_count);
            for (int i = 0; i < vm_count; i++) {
                av_log(AV_LOG_INFO, "  VM %d: %s (domid=%d, 状态=%s)", 
                       i + 1, vms[i].name, vms[i].domid, vms[i].state);
            }
        } else {
            av_log(AV_LOG_WARN, "未找到运行中的VM");
        }
        
        /* 等待5秒或直到收到关闭信号 */
        for (int i = 0; i < 50 && !service->shutdown_requested; i++) {
            usleep(100000);  /* 100ms */
        }
    }
    
    free(vms);
    av_log(AV_LOG_INFO, "VM监控线程已停止");
    return NULL;
}

/* 信号处理器 */
static void signal_handler(int sig) {
    av_log(AV_LOG_INFO, "收到信号 %d，正在优雅关闭...", sig);
    g_shutdown_requested = 1;
    g_service.shutdown_requested = 1;
}

/* 显示帮助信息 */
static void show_help(const char* program_name) {
    printf("XenServer Antivirus Host Service - Command-Based Version\n");
    printf("版本: %s\n", AV_VERSION);
    printf("构建: %s\n\n", AV_BUILD_INFO);
    printf("用法: %s [选项]\n\n", program_name);
    printf("选项:\n");
    printf("  -h, --help     显示帮助信息\n");
    printf("  -v, --verbose  启用详细日志\n");
    printf("  -t, --test     运行测试模式\n");
    printf("  --version      显示版本信息\n");
    printf("  --max-vms N    设置最大VM监控数量 (默认: 100)\n");
    printf("  --use-xe       强制使用xe命令\n");
    printf("  --use-xl       强制使用xl命令\n\n");
    printf("特性:\n");
    printf("  - 使用系统命令获取VM信息，避免libxenctrl权限问题\n");
    printf("  - 自动检测Citrix Hypervisor或标准Xen环境\n");
    printf("  - 支持xe和xl命令\n");
    printf("  - 无需复杂的设备权限配置\n\n");
}

/* 主函数 */
int main(int argc, char* argv[]) {
    int opt;
    int test_mode = 0;
    int max_vms = 100;
    int force_xe = 0, force_xl = 0;
    
    static struct option long_options[] = {
        {"help", no_argument, 0, 'h'},
        {"verbose", no_argument, 0, 'v'},
        {"test", no_argument, 0, 't'},
        {"version", no_argument, 0, 1000},
        {"max-vms", required_argument, 0, 1001},
        {"use-xe", no_argument, 0, 1002},
        {"use-xl", no_argument, 0, 1003},
        {0, 0, 0, 0}
    };
    
    /* 解析命令行参数 */
    while ((opt = getopt_long(argc, argv, "hvt", long_options, NULL)) != -1) {
        switch (opt) {
            case 'h':
                show_help(argv[0]);
                return 0;
            case 'v':
                g_log_level = AV_LOG_DEBUG;
                break;
            case 't':
                test_mode = 1;
                break;
            case 1000:  /* --version */
                printf("XenServer Antivirus Host Service\n");
                printf("版本: %s\n", AV_VERSION);
                printf("构建: %s\n", AV_BUILD_INFO);
                printf("编译: %s %s\n", __DATE__, __TIME__);
                return 0;
            case 1001:  /* --max-vms */
                max_vms = atoi(optarg);
                break;
            case 1002:  /* --use-xe */
                force_xe = 1;
                break;
            case 1003:  /* --use-xl */
                force_xl = 1;
                break;
            default:
                show_help(argv[0]);
                return 1;
        }
    }
    
    /* 设置日志级别 */
    av_set_log_level(g_log_level);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Host Service 启动中...");
    av_log(AV_LOG_INFO, "版本: %s, 构建: %s", AV_VERSION, AV_BUILD_INFO);
    av_log(AV_LOG_INFO, "编译: %s %s", __DATE__, __TIME__);
    av_log(AV_LOG_INFO, "最大VM数: %d", max_vms);
    
    /* 检查root权限 */
    if (geteuid() != 0) {
        av_log(AV_LOG_ERROR, "必须以root权限运行");
        return 1;
    }
    
    /* 初始化服务 */
    memset(&g_service, 0, sizeof(g_service));
    g_service.max_vms = max_vms;
    
    /* 检测命令类型 */
    if (force_xe) {
        g_service.use_xe_command = 1;
        av_log(AV_LOG_INFO, "强制使用xe命令");
    } else if (force_xl) {
        g_service.use_xe_command = 0;
        av_log(AV_LOG_INFO, "强制使用xl命令");
    } else {
        g_service.use_xe_command = detect_command_type();
    }
    
    /* 测试模式 */
    if (test_mode) {
        av_log(AV_LOG_INFO, "运行测试模式...");
        
        vm_info_t* test_vms = malloc(max_vms * sizeof(vm_info_t));
        if (test_vms) {
            int vm_count = get_vm_list_via_commands(test_vms, max_vms, g_service.use_xe_command);
            av_log(AV_LOG_INFO, "测试结果: 找到 %d 个VM", vm_count);
            
            for (int i = 0; i < vm_count; i++) {
                printf("VM %d: %s (domid=%d)\n", i + 1, test_vms[i].name, test_vms[i].domid);
            }
            
            free(test_vms);
            return (vm_count >= 0) ? 0 : 1;
        } else {
            av_log(AV_LOG_ERROR, "测试失败: 内存分配错误");
            return 1;
        }
    }
    
    /* 设置信号处理器 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    /* 启动VM监控线程 */
    if (pthread_create(&g_service.vm_monitor_thread, NULL, vm_monitor_thread, &g_service) != 0) {
        av_log(AV_LOG_ERROR, "无法创建VM监控线程");
        return 1;
    }
    
    av_log(AV_LOG_INFO, "服务启动成功，正在监控VM...");
    
    /* 主循环 */
    while (!g_shutdown_requested) {
        sleep(1);
    }
    
    av_log(AV_LOG_INFO, "正在关闭...");
    
    /* 等待监控线程结束 */
    pthread_join(g_service.vm_monitor_thread, NULL);
    
    av_log(AV_LOG_INFO, "XenServer Antivirus Host Service 已停止");
    return 0;
}
