# XenServer Antivirus Linux Client

## 概述

这是XenServer防病毒系统的Linux虚拟机客户端实现。该客户端运行在Linux虚拟机内部，通过XenStore和共享内存与XenServer宿主机上的防病毒服务进行通信。

## 功能特性

- **UUID获取**: 自动获取虚拟机UUID用于身份识别
- **XenStore监听**: 监听XenStore路径变化，获取共享内存信息
- **内存映射**: 映射宿主机分配的共享内存到客户端地址空间
- **通信协议**: 实现与宿主机的双向通信协议
- **多线程支持**: 使用多线程处理XenStore监听和通信
- **错误处理**: 完善的错误处理和资源清理机制

## 文件结构

```
linux-client/
├── av_linux_client.h      # 头文件定义
├── av_linux_client.c      # 主要实现
├── main.c                 # 主程序入口
├── Makefile              # 编译配置
├── Makefile.test         # 测试编译配置
└── README.md             # 本文档
```

## 编译要求

### 系统要求
- Linux操作系统
- GCC编译器
- Xen开发库 (libxen-dev)
- pkg-config

### 安装依赖 (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install -y build-essential pkg-config libxen-dev
```

### 编译步骤
```bash
# 检查依赖
make check-deps

# 编译程序
make

# 清理编译文件
make clean

# 安装到系统
sudo make install

# 卸载程序
sudo make uninstall
```

## 使用方法

### 基本用法
```bash
# 以root权限运行（需要访问Xen接口）
sudo ./bin/av_linux_client

# 显示帮助信息
./bin/av_linux_client --help

# 启用详细日志
sudo ./bin/av_linux_client --verbose

# 以守护进程模式运行
sudo ./bin/av_linux_client --daemon

# 指定日志文件
sudo ./bin/av_linux_client --log-file /var/log/av_client.log
```

### 命令行选项
- `-h, --help`: 显示帮助信息
- `-v, --verbose`: 启用详细日志输出
- `-d, --daemon`: 以守护进程模式运行
- `-l, --log-level LEVEL`: 设置日志级别 (0=ERROR, 1=WARNING, 2=INFO, 3=DEBUG)
- `-f, --log-file FILE`: 指定日志文件路径

## 工作原理

1. **初始化阶段**:
   - 打开libxenctrl和XenStore连接
   - 获取虚拟机UUID
   - 初始化同步对象

2. **监听阶段**:
   - 启动XenStore监听线程
   - 监听共享内存ID的XenStore路径
   - 等待宿主机分配共享内存

3. **通信阶段**:
   - 映射共享内存到客户端地址空间
   - 发送"SCAN_REQ"请求到共享内存
   - 等待宿主机响应"SCAN_ACK"
   - 循环进行通信

4. **清理阶段**:
   - 解除内存映射
   - 关闭XenStore连接
   - 释放所有资源

## 通信协议

客户端与宿主机通过4KB共享内存进行通信：

```
共享内存布局:
+----------------+
| 命令字段(16B)   |  "SCAN_REQ" 或 "SCAN_ACK"
+----------------+
| 保留字段(48B)   |  用于未来扩展
+----------------+
| 数据区域(4032B) |  传输扫描数据或结果
+----------------+
```

## 错误处理

程序包含完善的错误处理机制：
- 自动重试机制
- 资源清理保证
- 详细的错误日志
- 优雅的关闭处理

## 调试和故障排除

### 常见问题

1. **权限错误**: 确保以root权限运行
2. **Xen库缺失**: 安装libxen-dev包
3. **连接失败**: 检查是否在Xen虚拟机中运行
4. **通信超时**: 检查宿主机服务是否正常运行

### 调试方法
```bash
# 启用调试日志
sudo ./bin/av_linux_client -v -l 3

# 检查系统日志
sudo journalctl -f

# 检查XenStore状态
sudo xenstore-ls /local/domain/0/antivirus/
```

## 开发说明

### 代码结构
- `av_linux_client.h`: 定义所有接口和数据结构
- `av_linux_client.c`: 实现核心功能
- `main.c`: 程序入口和命令行处理

### 关键函数
- `av_get_vm_uuid()`: 获取虚拟机UUID
- `av_xenstore_monitor_thread()`: XenStore监听线程
- `av_map_grant_memory()`: 映射共享内存
- `av_linux_client_run_communication()`: 主通信循环

### 编译注意事项
- 在实际XenServer环境中编译时，Xen头文件和库会自动可用
- 在开发环境中可能需要安装额外的Xen开发包
- 某些函数可能需要根据具体的Xen版本进行调整

## 许可证

本项目遵循相应的开源许可证。详情请参考项目根目录的LICENSE文件。