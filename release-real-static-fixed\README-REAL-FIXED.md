# XenServer Antivirus Real Static Build (Fixed Version)

这是XenServer防病毒系统的真实版本静态编译，包含以下修复：

## 修复内容

### 1. 权限问题修复
- 改进了xc_domain_getinfolist的错误处理
- 添加了详细的权限检查和错误报告
- 提供了更清晰的错误信息和解决建议

### 2. 信号处理修复
- 修复了Ctrl+C无法终止程序的问题
- 使用可中断的nanosleep替代普通sleep
- 改进了信号处理器，支持强制退出
- 确保所有线程都能正确响应关闭信号

## 文件说明

- `xenserver-antivirus-host-real-static-fixed`: 宿主机服务（真实静态编译）
- `xenserver-antivirus-client-real-static-fixed`: Linux客户端（真实静态编译）

## 使用方法

### 宿主机服务
```bash
# 在XenServer宿主机上运行
sudo ./xenserver-antivirus-host-real-static-fixed

# 查看帮助
./xenserver-antivirus-host-real-static-fixed --help

# 以守护进程模式运行
sudo ./xenserver-antivirus-host-real-static-fixed -d
```

### Linux客户端
```bash
# 在Linux虚拟机中运行
sudo ./xenserver-antivirus-client-real-static-fixed

# 查看帮助
./xenserver-antivirus-client-real-static-fixed --help
```

## 依赖要求

### 宿主机服务
- 必须在XenServer Dom0环境中运行
- 需要libxenctrl和libxenstore库
- 必须以root权限运行

### Linux客户端
- 需要在Xen虚拟机中运行
- 需要libxenctrl和libxenstore库
- 必须以root权限运行

## 信号处理

程序现在正确支持以下信号：
- Ctrl+C (SIGINT): 优雅关闭
- SIGTERM: 优雅关闭
- 连续3次信号: 强制退出

## 故障排除

如果遇到权限问题：
1. 确保以root权限运行
2. 检查是否在正确的环境中（Dom0 vs 虚拟机）
3. 验证Xen服务状态：`systemctl status xenstored`
4. 检查设备权限：`ls -la /dev/xen/`

如果程序无法终止：
1. 尝试Ctrl+C
2. 如果无响应，连续按3次Ctrl+C强制退出
3. 或使用：`kill -TERM <pid>`

## 注意事项

这是真实版本的编译，需要在实际的Xen环境中运行。
如果在非Xen环境中测试，请使用模拟版本。
