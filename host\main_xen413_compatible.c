/*
 * XenServer宿主机防病毒服务 - Xen 4.13兼容版本
 * 专门针对Xen 4.13.4环境优化
 */

#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <pthread.h>
#include <sys/mman.h>
#include <sys/select.h>
#include <sys/time.h>
#include <getopt.h>
#include <time.h>

/* Xen头文件 */
#include <xenctrl.h>
#include <xenstore.h>
#include <xengnttab.h>

/* 项目头文件 */
#include "../common/av_common.h"

/* 版本信息 */
#define AV_VERSION "1.0.0-xen413"

/* 错误码定义 */
#define AV_SUCCESS 0
#define AV_ERROR_INIT -1
#define AV_ERROR_MEMORY -2
#define AV_ERROR_XEN -3
#define AV_ERROR_TIMEOUT -4
#define AV_ERROR_INVALID_PARAM -5
#define AV_ERROR_MEMORY_ALLOC -6

/* 宿主机服务结构体 */
typedef struct {
    xc_interface* xc_handle;        /* libxenctrl句柄 */
    struct xs_handle* xs_handle;    /* XenStore句柄 */
    xengnttab_handle* gnttab_handle; /* Grant table句柄 */
    pthread_t vm_monitor_thread;    /* VM监控线程 */
    volatile int shutdown_requested; /* 关闭请求标志 */
    int max_vms;                    /* 最大VM数量 */
} av_host_service_t;

/* 全局变量 */
static av_host_service_t g_service;
static volatile int g_shutdown_requested = 0;

/* 日志级别 */
typedef enum {
    AV_LOG_ERROR = 0,
    AV_LOG_WARN = 1,
    AV_LOG_INFO = 2,
    AV_LOG_DEBUG = 3
} av_log_level_t;

static int g_log_level = AV_LOG_INFO;

/* 日志函数 */
void av_log(av_log_level_t level, const char* format, ...) {
    if (level > g_log_level) return;
    
    const char* level_str[] = {"ERROR", "WARN", "INFO", "DEBUG"};
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    
    printf("[%s] %04d-%02d-%02d %02d:%02d:%02d [PID:%d]: ",
           level_str[level],
           tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
           tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec,
           getpid());
    
    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);
    printf("\n");
    fflush(stdout);
}

/* 信号处理函数 */
void signal_handler(int sig) {
    switch (sig) {
        case SIGINT:
        case SIGTERM:
            av_log(AV_LOG_INFO, "Received signal %d, shutting down gracefully...", sig);
            g_shutdown_requested = 1;
            g_service.shutdown_requested = 1;
            break;
    }
}

/* 获取运行中的VM列表 - Xen 4.13兼容版本 */
int av_list_running_vms(av_host_service_t* service, domid_t** domids, int* count) {
    if (!service || !domids || !count) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_list_running_vms");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "libxc handle not initialized");
        return AV_ERROR_INIT;
    }

    *domids = NULL;
    *count = 0;

    /* 为Xen 4.13调整参数类型 */
    const int max_domains = service->max_vms;
    xc_domaininfo_t* domain_info = malloc(max_domains * sizeof(xc_domaininfo_t));
    
    if (!domain_info) {
        av_log(AV_LOG_ERROR, "Failed to allocate memory for domain info");
        return AV_ERROR_MEMORY_ALLOC;
    }

    /* 使用正确的参数类型调用 - Xen 4.13兼容 */
    int num_domains = xc_domain_getinfolist(service->xc_handle, 0, max_domains, domain_info);
    if (num_domains < 0) {
        int saved_errno = errno;
        av_log(AV_LOG_ERROR, "Failed to get domain list: %s (errno: %d)", strerror(saved_errno), saved_errno);
        
        if (saved_errno == EPERM || saved_errno == EACCES) {
            av_log(AV_LOG_ERROR, "Permission denied - ensure running as root with proper Xen privileges");
            av_log(AV_LOG_ERROR, "Check if user is in 'xen' group and has access to /dev/xen/xenstore");
        } else if (saved_errno == ENOENT || saved_errno == ENODEV) {
            av_log(AV_LOG_ERROR, "Xen hypervisor not found - ensure Xen is running and properly configured");
        } else if (saved_errno == ECONNREFUSED) {
            av_log(AV_LOG_ERROR, "Connection to Xen hypervisor refused - check xenstored daemon");
        }
        
        free(domain_info);
        return AV_ERROR_XEN;
    }

    if (num_domains == 0) {
        av_log(AV_LOG_INFO, "No domains found");
        free(domain_info);
        return AV_SUCCESS;
    }

    /* 分配domid数组 */
    domid_t* result_domids = malloc(num_domains * sizeof(domid_t));
    if (!result_domids) {
        av_log(AV_LOG_ERROR, "Failed to allocate memory for domid array");
        free(domain_info);
        return AV_ERROR_MEMORY_ALLOC;
    }

    /* 提取运行中的域ID */
    int running_count = 0;
    for (int i = 0; i < num_domains; i++) {
        /* 跳过Dom0 */
        if (domain_info[i].domid == 0) {
            continue;
        }
        
        /* 只包含运行中的域 */
        if (domain_info[i].flags & XEN_DOMINF_running) {
            result_domids[running_count] = domain_info[i].domid;
            running_count++;
            av_log(AV_LOG_DEBUG, "Found running domain: %d", domain_info[i].domid);
        }
    }

    free(domain_info);

    if (running_count == 0) {
        free(result_domids);
        av_log(AV_LOG_INFO, "No running VMs found (excluding Dom0)");
        return AV_SUCCESS;
    }

    *domids = result_domids;
    *count = running_count;
    
    av_log(AV_LOG_DEBUG, "Successfully listed %d running VMs", running_count);
    return AV_SUCCESS;
}

/* XenStore读取函数 - Xen 4.13兼容版本 */
int av_xenstore_read(av_host_service_t* service, const char* path, char* buffer, size_t buffer_size) {
    if (!service || !path || !buffer || buffer_size == 0) {
        av_log(AV_LOG_ERROR, "Invalid parameters for av_xenstore_read");
        return AV_ERROR_INVALID_PARAM;
    }

    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "XenStore handle not initialized");
        return AV_ERROR_INIT;
    }

    unsigned int len = 0;
    void* value_ptr = xs_read(service->xs_handle, XBT_NULL, path, &len);
    
    if (!value_ptr) {
        av_log(AV_LOG_DEBUG, "Failed to read XenStore path: %s", path);
        return AV_ERROR_XEN;
    }

    char* value = (char*)value_ptr;
    
    if (len >= buffer_size) {
        av_log(AV_LOG_WARN, "XenStore value too large for buffer (need %u, have %zu)", len, buffer_size);
        free(value);
        return AV_ERROR_MEMORY;
    }

    strncpy(buffer, value, buffer_size - 1);
    buffer[buffer_size - 1] = '\0';
    
    free(value);
    av_log(AV_LOG_DEBUG, "Successfully read XenStore path %s: %s", path, buffer);
    return AV_SUCCESS;
}

/* VM监控线程 */
void* vm_monitor_thread(void* arg) {
    av_host_service_t* service = (av_host_service_t*)arg;

    av_log(AV_LOG_INFO, "VM monitor thread started");

    while (!service->shutdown_requested) {
        av_log(AV_LOG_DEBUG, "Monitoring VMs...");

        /* 获取运行中的VM列表 */
        av_log(AV_LOG_DEBUG, "Getting list of running VMs...");
        domid_t* domids = NULL;
        int count = 0;

        int ret = av_list_running_vms(service, &domids, &count);
        if (ret == AV_SUCCESS) {
            if (count > 0) {
                av_log(AV_LOG_INFO, "Found %d running VMs", count);

                /* 检查每个VM的防病毒客户端状态 */
                for (int i = 0; i < count; i++) {
                    char xenstore_path[256];
                    char vm_path[512];

                    snprintf(xenstore_path, sizeof(xenstore_path),
                            "/local/domain/%d/vm", domids[i]);

                    if (av_xenstore_read(service, xenstore_path, vm_path, sizeof(vm_path)) == AV_SUCCESS) {
                        av_log(AV_LOG_DEBUG, "VM %d path: %s", domids[i], vm_path);
                    }
                }

                free(domids);
            } else {
                av_log(AV_LOG_INFO, "No running VMs found");
            }
        } else {
            av_log(AV_LOG_ERROR, "Failed to list running VMs: %s",
                   ret == AV_ERROR_XEN ? "Xen error" :
                   ret == AV_ERROR_MEMORY_ALLOC ? "Memory allocation error" : "Unknown error");
        }

        /* 等待5秒后再次检查 */
        for (int i = 0; i < 50 && !service->shutdown_requested; i++) {
            usleep(100000); /* 100ms */
        }
    }

    av_log(AV_LOG_INFO, "VM monitor thread exiting");
    return NULL;
}

/* 初始化宿主机服务 */
int av_host_service_init(av_host_service_t* service) {
    if (!service) {
        return AV_ERROR_INIT;
    }

    memset(service, 0, sizeof(av_host_service_t));
    service->max_vms = 100; /* 默认最大100个VM */

    /* 打开libxenctrl - 使用Xen 4.13兼容参数 */
    service->xc_handle = xc_interface_open(NULL, NULL, 0);
    if (!service->xc_handle) {
        av_log(AV_LOG_ERROR, "Failed to open xc interface: %s", strerror(errno));
        return AV_ERROR_XEN;
    }

    /* 打开XenStore */
    service->xs_handle = xs_open(0);
    if (!service->xs_handle) {
        av_log(AV_LOG_ERROR, "Failed to open XenStore: %s", strerror(errno));
        xc_interface_close(service->xc_handle);
        return AV_ERROR_XEN;
    }

    /* 打开Grant Table */
    service->gnttab_handle = xengnttab_open(NULL, 0);
    if (!service->gnttab_handle) {
        av_log(AV_LOG_ERROR, "Failed to open grant table: %s", strerror(errno));
        xs_close(service->xs_handle);
        xc_interface_close(service->xc_handle);
        return AV_ERROR_XEN;
    }

    av_log(AV_LOG_INFO, "Host service initialized successfully");
    return AV_SUCCESS;
}

/* 启动宿主机服务 */
int av_host_service_start(av_host_service_t* service) {
    if (!service) {
        return AV_ERROR_INIT;
    }

    /* 启动VM监控线程 */
    int ret = pthread_create(&service->vm_monitor_thread, NULL, vm_monitor_thread, service);
    if (ret != 0) {
        av_log(AV_LOG_ERROR, "Failed to create VM monitor thread: %s", strerror(ret));
        return AV_ERROR_INIT;
    }

    av_log(AV_LOG_INFO, "Host service started successfully");
    return AV_SUCCESS;
}

/* 停止宿主机服务 */
int av_host_service_stop(av_host_service_t* service) {
    if (!service) {
        return AV_ERROR_INIT;
    }

    /* 请求关闭 */
    service->shutdown_requested = 1;

    /* 等待VM监控线程结束 */
    int ret = pthread_join(service->vm_monitor_thread, NULL);
    if (ret != 0) {
        av_log(AV_LOG_WARN, "Failed to join VM monitor thread: %s", strerror(ret));
    }

    av_log(AV_LOG_INFO, "Host service stopped successfully");
    return AV_SUCCESS;
}

/* 清理宿主机服务 */
void av_host_service_cleanup(av_host_service_t* service) {
    if (!service) {
        return;
    }

    if (service->gnttab_handle) {
        xengnttab_close(service->gnttab_handle);
        service->gnttab_handle = NULL;
    }

    if (service->xs_handle) {
        xs_close(service->xs_handle);
        service->xs_handle = NULL;
    }

    if (service->xc_handle) {
        xc_interface_close(service->xc_handle);
        service->xc_handle = NULL;
    }

    av_log(AV_LOG_INFO, "Host service cleaned up");
}

/* 显示帮助信息 */
void show_help(const char* program_name) {
    printf("XenServer Antivirus Host Service v%s\n", AV_VERSION);
    printf("Usage: %s [OPTIONS]\n\n", program_name);
    printf("Options:\n");
    printf("  -h, --help           Show this help message\n");
    printf("  -v, --verbose        Enable verbose logging\n");
    printf("  -d, --daemon         Run as daemon\n");
    printf("  -l, --log-level N    Set log level (0=ERROR, 1=WARN, 2=INFO, 3=DEBUG)\n");
    printf("  -m, --max-vms N      Set maximum number of VMs to monitor (default: 100)\n");
    printf("  -f, --log-file FILE  Log to file instead of stdout\n");
    printf("\n");
    printf("Examples:\n");
    printf("  %s                   Run in foreground with default settings\n", program_name);
    printf("  %s -v -l 3           Run with verbose debug logging\n", program_name);
    printf("  %s -d -f /var/log/xenserver-antivirus.log  Run as daemon with file logging\n", program_name);
    printf("\n");
    printf("Requirements:\n");
    printf("  - Must run as root\n");
    printf("  - Must run on XenServer Dom0\n");
    printf("  - Requires Xen libraries (libxenctrl, libxenstore, libxengnttab)\n");
    printf("  - Compatible with Xen 4.13.x\n");
    printf("\n");
    printf("Signal Handling:\n");
    printf("  SIGINT/SIGTERM       Graceful shutdown\n");
    printf("  SIGPIPE              Ignored\n");
}

/* 主函数 */
int main(int argc, char* argv[]) {
    int daemon_mode = 0;
    int max_vms = 100;
    char* log_file = NULL;

    /* 解析命令行参数 */
    static struct option long_options[] = {
        {"help", no_argument, 0, 'h'},
        {"verbose", no_argument, 0, 'v'},
        {"daemon", no_argument, 0, 'd'},
        {"log-level", required_argument, 0, 'l'},
        {"max-vms", required_argument, 0, 'm'},
        {"log-file", required_argument, 0, 'f'},
        {0, 0, 0, 0}
    };

    int c;
    while ((c = getopt_long(argc, argv, "hvdl:m:f:", long_options, NULL)) != -1) {
        switch (c) {
            case 'h':
                show_help(argv[0]);
                return 0;
            case 'v':
                g_log_level = AV_LOG_DEBUG;
                break;
            case 'd':
                daemon_mode = 1;
                break;
            case 'l':
                g_log_level = atoi(optarg);
                if (g_log_level < 0 || g_log_level > 3) {
                    fprintf(stderr, "Invalid log level: %s\n", optarg);
                    return 1;
                }
                break;
            case 'm':
                max_vms = atoi(optarg);
                if (max_vms <= 0 || max_vms > 1000) {
                    fprintf(stderr, "Invalid max VMs: %s\n", optarg);
                    return 1;
                }
                break;
            case 'f':
                log_file = optarg;
                break;
            default:
                show_help(argv[0]);
                return 1;
        }
    }

    /* 重定向日志到文件 */
    if (log_file) {
        if (freopen(log_file, "a", stdout) == NULL) {
            fprintf(stderr, "Failed to open log file: %s\n", log_file);
            return 1;
        }
        if (freopen(log_file, "a", stderr) == NULL) {
            fprintf(stderr, "Failed to redirect stderr to log file\n");
            return 1;
        }
    }

    av_log(AV_LOG_INFO, "XenServer Antivirus Host Service starting...");
    av_log(AV_LOG_INFO, "Version: %s, Build: %s %s", AV_VERSION, __DATE__, __TIME__);
    av_log(AV_LOG_INFO, "Max VMs: %d", max_vms);

    /* 检查运行权限 */
    if (geteuid() != 0) {
        av_log(AV_LOG_ERROR, "This program must be run as root");
        return 1;
    }

    /* 守护进程化 */
    if (daemon_mode) {
        if (daemon(0, 0) != 0) {
            av_log(AV_LOG_ERROR, "Failed to daemonize: %s", strerror(errno));
            return 1;
        }
        av_log(AV_LOG_INFO, "Running as daemon");
    }

    /* 设置信号处理 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGPIPE, SIG_IGN);  /* 忽略SIGPIPE信号 */

    /* 初始化服务 */
    g_service.max_vms = max_vms;
    int ret = av_host_service_init(&g_service);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to initialize host service");
        return 1;
    }

    /* 启动服务 */
    ret = av_host_service_start(&g_service);
    if (ret != AV_SUCCESS) {
        av_log(AV_LOG_ERROR, "Failed to start host service");
        av_host_service_cleanup(&g_service);
        return 1;
    }

    av_log(AV_LOG_INFO, "Service started successfully, monitoring VMs...");

    /* 主循环 */
    while (!g_shutdown_requested) {
        sleep(1);
    }

    av_log(AV_LOG_INFO, "Shutting down...");

    /* 停止服务 */
    av_host_service_stop(&g_service);
    av_host_service_cleanup(&g_service);

    av_log(AV_LOG_INFO, "Service stopped successfully");
    return 0;
}
