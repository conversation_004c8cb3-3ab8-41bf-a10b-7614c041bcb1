# Makefile for XenServer Antivirus Host Service - Xen 4.13 Compatible Version
# Optimized for Xen 4.13.4 environment

CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE
INCLUDES = -I../common -I.
LIBS = -lxenctrl -lxenstore -lxengnttab -lpthread

# Static linking flags
STATIC_CFLAGS = -static
STATIC_LIBS = -lxenctrl -lxenstore -lxengnttab -lxencall -lxentoollog -lxenforeignmemory -lxendevicemodel -lxentoolcore -lpthread

# Directories
OBJDIR = obj_xen413
BINDIR = bin
COMMONDIR = ../common

# Source files
SOURCES = main_xen413_compatible.c $(COMMONDIR)/av_common.c
OBJECTS = $(OBJDIR)/main_xen413_compatible.o $(OBJDIR)/av_common.o

# Target executables
TARGET_DYNAMIC = $(BINDIR)/xenserver-antivirus-host-xen413
TARGET_STATIC = $(BINDIR)/xenserver-antivirus-host-xen413-static

# Default target
all: check-deps $(TARGET_DYNAMIC)

# Static version
static: check-deps $(TARGET_STATIC)

# Both versions
both: check-deps $(TARGET_DYNAMIC) $(TARGET_STATIC)

# Check dependencies
check-deps:
	@echo "Checking Xen 4.13 dependencies..."
	@pkg-config --exists xencontrol || (echo "ERROR: libxenctrl not found" && exit 1)
	@pkg-config --exists xenstore || (echo "ERROR: libxenstore not found" && exit 1)
	@pkg-config --exists xengnttab || (echo "ERROR: libxengnttab not found" && exit 1)
	@echo "All dependencies found"

# Create directories
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(BINDIR):
	mkdir -p $(BINDIR)

# Compile main source
$(OBJDIR)/main_xen413_compatible.o: main_xen413_compatible.c | $(OBJDIR)
	@echo "Compiling main_xen413_compatible.c for Xen 4.13..."
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Compile common source
$(OBJDIR)/av_common.o: $(COMMONDIR)/av_common.c | $(OBJDIR)
	@echo "Compiling av_common.c..."
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Link dynamic version
$(TARGET_DYNAMIC): $(OBJECTS) | $(BINDIR)
	@echo "Linking $(TARGET_DYNAMIC) (dynamic)..."
	$(CC) $(OBJECTS) -o $@ $(LIBS)
	@echo "Build complete: $@"
	@echo "Built with Xen libraries for version 4.13.x"
	@file $@

# Link static version
$(TARGET_STATIC): $(OBJECTS) | $(BINDIR)
	@echo "Linking $(TARGET_STATIC) (static)..."
	$(CC) $(STATIC_CFLAGS) $(OBJECTS) -o $@ $(STATIC_LIBS)
	@echo "Build complete: $@"
	@echo "Built with static Xen libraries for version 4.13.x"
	@file $@

# Clean build files
clean:
	@echo "Cleaning Xen 4.13 build files..."
	rm -rf $(OBJDIR) $(BINDIR)/xenserver-antivirus-host-xen413*

# Install (requires root)
install: $(TARGET_DYNAMIC)
	@echo "Installing XenServer Antivirus Host Service..."
	install -m 755 $(TARGET_DYNAMIC) /usr/local/bin/
	@echo "Installation complete"

# Uninstall
uninstall:
	@echo "Uninstalling XenServer Antivirus Host Service..."
	rm -f /usr/local/bin/xenserver-antivirus-host-xen413
	@echo "Uninstall complete"

# Test build environment
test-env:
	@echo "=== Xen 4.13 Environment Test ==="
	@echo "Compiler: $(CC)"
	@$(CC) --version | head -1
	@echo ""
	@echo "Xen version check:"
	@xl info | grep xen_version || echo "xl command not available"
	@echo ""
	@echo "Library check:"
	@pkg-config --modversion xencontrol 2>/dev/null || echo "xencontrol: not found via pkg-config"
	@pkg-config --modversion xenstore 2>/dev/null || echo "xenstore: not found via pkg-config"
	@pkg-config --modversion xengnttab 2>/dev/null || echo "xengnttab: not found via pkg-config"
	@echo ""
	@echo "Header files:"
	@find /usr/include -name "xenctrl.h" 2>/dev/null || echo "xenctrl.h: not found"
	@find /usr/include -name "xenstore.h" 2>/dev/null || echo "xenstore.h: not found"
	@find /usr/include -name "xengnttab.h" 2>/dev/null || echo "xengnttab.h: not found"
	@echo ""
	@echo "Library files:"
	@find /usr/lib* -name "libxenctrl.*" 2>/dev/null || echo "libxenctrl: not found"
	@find /usr/lib* -name "libxenstore.*" 2>/dev/null || echo "libxenstore: not found"
	@find /usr/lib* -name "libxengnttab.*" 2>/dev/null || echo "libxengnttab: not found"

# Run tests
test: $(TARGET_DYNAMIC)
	@echo "Running basic functionality test..."
	@echo "Testing help output:"
	./$(TARGET_DYNAMIC) --help
	@echo ""
	@echo "Testing version info:"
	./$(TARGET_DYNAMIC) --help | grep -i version

# Debug build
debug: CFLAGS += -DDEBUG -O0
debug: $(TARGET_DYNAMIC)

# Release build
release: CFLAGS += -DNDEBUG -O3
release: $(TARGET_DYNAMIC)

# Show build configuration
show-config:
	@echo "=== Build Configuration ==="
	@echo "CC: $(CC)"
	@echo "CFLAGS: $(CFLAGS)"
	@echo "INCLUDES: $(INCLUDES)"
	@echo "LIBS: $(LIBS)"
	@echo "STATIC_LIBS: $(STATIC_LIBS)"
	@echo "Sources: $(SOURCES)"
	@echo "Target (dynamic): $(TARGET_DYNAMIC)"
	@echo "Target (static): $(TARGET_STATIC)"
	@echo "=========================="

.PHONY: all static both check-deps clean install uninstall test-env test debug release show-config
