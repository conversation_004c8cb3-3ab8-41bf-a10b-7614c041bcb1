#include "test_common.h"
#include "../host/av_host.h"
#include <string.h>
#include <sys/mman.h>

/* 模拟的共享内存区域 */
static char mock_shared_memory[AV_SHARED_MEMORY_SIZE];

/* 测试共享内存布局验证 */
int test_shared_memory_validation(void) {
    /* 测试有效的共享内存 */
    memset(mock_shared_memory, 0, sizeof(mock_shared_memory));
    TEST_ASSERT_EQUAL(AV_SUCCESS, av_validate_shared_memory(mock_shared_memory, sizeof(mock_shared_memory)), 
                     "Valid shared memory should pass validation");
    
    /* 测试无效参数 */
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_validate_shared_memory(NULL, sizeof(mock_shared_memory)), 
                     "NULL address should fail validation");
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_validate_shared_memory(mock_shared_memory, 0), 
                     "Zero size should fail validation");
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_validate_shared_memory(mock_shared_memory, 10), 
                     "Size too small should fail validation");
    
    return 0;
}

/* 测试命令读写功能 */
int test_command_read_write(void) {
    memset(mock_shared_memory, 0, sizeof(mock_shared_memory));
    
    /* 测试写入命令 */
    int ret = av_write_shared_memory_command(mock_shared_memory, AV_SCAN_REQUEST);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Writing command should succeed");
    
    /* 测试读取命令 */
    char read_command[AV_COMMAND_SIZE];
    ret = av_read_shared_memory_command(mock_shared_memory, read_command, sizeof(read_command));
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Reading command should succeed");
    TEST_ASSERT_STRING_EQUAL(AV_SCAN_REQUEST, read_command, "Read command should match written command");
    
    /* 测试检查命令 */
    ret = av_check_shared_memory_command(mock_shared_memory, AV_SCAN_REQUEST);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Command check should succeed for matching command");
    
    ret = av_check_shared_memory_command(mock_shared_memory, AV_SCAN_RESPONSE);
    TEST_ASSERT_EQUAL(AV_ERROR_PROTOCOL, ret, "Command check should fail for non-matching command");
    
    /* 测试清空命令 */
    ret = av_clear_shared_memory_command(mock_shared_memory);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Clearing command should succeed");
    
    ret = av_read_shared_memory_command(mock_shared_memory, read_command, sizeof(read_command));
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Reading after clear should succeed");
    TEST_ASSERT_STRING_EQUAL("", read_command, "Command should be empty after clear");
    
    return 0;
}

/* 测试命令写入边界条件 */
int test_command_boundary_conditions(void) {
    memset(mock_shared_memory, 0, sizeof(mock_shared_memory));
    
    /* 测试空命令 */
    int ret = av_write_shared_memory_command(mock_shared_memory, "");
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Writing empty command should succeed");
    
    /* 测试最大长度命令 */
    char max_command[AV_COMMAND_SIZE];
    memset(max_command, 'A', AV_COMMAND_SIZE - 1);
    max_command[AV_COMMAND_SIZE - 1] = '\0';
    
    ret = av_write_shared_memory_command(mock_shared_memory, max_command);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Writing max length command should succeed");
    
    /* 测试过长命令 */
    char long_command[AV_COMMAND_SIZE + 10];
    memset(long_command, 'B', sizeof(long_command) - 1);
    long_command[sizeof(long_command) - 1] = '\0';
    
    ret = av_write_shared_memory_command(mock_shared_memory, long_command);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Writing too long command should fail");
    
    /* 测试NULL命令 */
    ret = av_write_shared_memory_command(mock_shared_memory, NULL);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Writing NULL command should fail");
    
    return 0;
}

/* 测试读取命令的边界条件 */
int test_read_command_boundary_conditions(void) {
    memset(mock_shared_memory, 0, sizeof(mock_shared_memory));
    
    /* 写入测试命令 */
    av_write_shared_memory_command(mock_shared_memory, "TEST");
    
    /* 测试小缓冲区读取 */
    char small_buffer[3];
    int ret = av_read_shared_memory_command(mock_shared_memory, small_buffer, sizeof(small_buffer));
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Reading with small buffer should succeed");
    TEST_ASSERT_STRING_EQUAL("TE", small_buffer, "Should read truncated command");
    
    /* 测试NULL缓冲区 */
    ret = av_read_shared_memory_command(mock_shared_memory, NULL, 10);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Reading with NULL buffer should fail");
    
    /* 测试零大小缓冲区 */
    char buffer[10];
    ret = av_read_shared_memory_command(mock_shared_memory, buffer, 0);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Reading with zero size buffer should fail");
    
    return 0;
}

/* 测试共享内存布局结构 */
int test_shared_memory_layout(void) {
    memset(mock_shared_memory, 0, sizeof(mock_shared_memory));
    
    av_shared_memory_layout_t* layout = (av_shared_memory_layout_t*)mock_shared_memory;
    
    /* 验证结构大小 */
    TEST_ASSERT(sizeof(av_shared_memory_layout_t) <= AV_SHARED_MEMORY_SIZE, 
               "Layout structure should fit in shared memory");
    
    /* 测试命令字段 */
    strcpy(layout->command, "TESTCMD");
    
    char read_cmd[AV_COMMAND_SIZE];
    int ret = av_read_shared_memory_command(mock_shared_memory, read_cmd, sizeof(read_cmd));
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Reading from layout should succeed");
    TEST_ASSERT_STRING_EQUAL("TESTCMD", read_cmd, "Should read correct command from layout");
    
    /* 测试保留字段初始化 */
    for (int i = 0; i < AV_RESERVED_SIZE; i++) {
        TEST_ASSERT_EQUAL(0, layout->reserved[i], "Reserved field should be zero initialized");
    }
    
    return 0;
}

/* 测试无效参数处理 */
int test_invalid_parameters(void) {
    /* 测试所有函数的NULL参数处理 */
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_validate_shared_memory(NULL, 100), 
                     "validate_shared_memory should reject NULL address");
    
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_write_shared_memory_command(NULL, "test"), 
                     "write_command should reject NULL address");
    
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_read_shared_memory_command(NULL, NULL, 0), 
                     "read_command should reject NULL parameters");
    
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_check_shared_memory_command(NULL, "test"), 
                     "check_command should reject NULL address");
    
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, av_clear_shared_memory_command(NULL), 
                     "clear_command should reject NULL address");
    
    return 0;
}

/* 测试命令协议常量 */
int test_protocol_constants(void) {
    /* 验证协议常量定义 */
    TEST_ASSERT(strlen(AV_SCAN_REQUEST) < AV_COMMAND_SIZE, 
               "SCAN_REQUEST should fit in command field");
    TEST_ASSERT(strlen(AV_SCAN_RESPONSE) < AV_COMMAND_SIZE, 
               "SCAN_RESPONSE should fit in command field");
    
    /* 验证常量内容 */
    TEST_ASSERT_STRING_EQUAL("SCAN_REQ", AV_SCAN_REQUEST, "SCAN_REQUEST constant should be correct");
    TEST_ASSERT_STRING_EQUAL("SCAN_ACK", AV_SCAN_RESPONSE, "SCAN_RESPONSE constant should be correct");
    
    return 0;
}

/* 主测试函数 */
int main(void) {
    test_init();
    
    /* 运行测试 */
    RUN_TEST(test_shared_memory_validation);
    RUN_TEST(test_command_read_write);
    RUN_TEST(test_command_boundary_conditions);
    RUN_TEST(test_read_command_boundary_conditions);
    RUN_TEST(test_shared_memory_layout);
    RUN_TEST(test_invalid_parameters);
    RUN_TEST(test_protocol_constants);
    
    /* 输出测试结果 */
    test_summary();
    
    return (tests_failed > 0) ? 1 : 0;
}