#include "test_common.h"
#include "../host/av_host.h"
#include <string.h>
#include <pthread.h>
#include <unistd.h>
#include <time.h>

/* 测试用的共享内存模拟 */
static av_shared_memory_layout_t test_shared_memory;

/* 测试响应时间性能 */
int test_response_time_performance(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 初始化测试共享内存 */
    memset(&test_shared_memory, 0, sizeof(test_shared_memory));
    
    const char* test_uuid = "12345678-1234-1234-1234-123456789abc";
    
    /* 测试多次响应，验证100ms性能要求 */
    for (int i = 0; i < 50; i++) {
        /* 写入请求 */
        av_write_shared_memory_command(&test_shared_memory, AV_SCAN_REQUEST);
        
        /* 测量响应时间 */
        struct timespec start, end;
        clock_gettime(CLOCK_MONOTONIC, &start);
        
        int ret = av_send_scan_response(&service, &test_shared_memory, AV_SCAN_RESPONSE);
        
        clock_gettime(CLOCK_MONOTONIC, &end);
        
        TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Response should succeed");
        
        /* 计算响应时间 */
        long response_time_ms = (end.tv_sec - start.tv_sec) * 1000 +
                               (end.tv_nsec - start.tv_nsec) / 1000000;
        
        /* 验证响应时间要求 */
        TEST_ASSERT(response_time_ms < 100, "Response time should be under 100ms");
        
        /* 验证响应内容 */
        ret = av_check_shared_memory_command(&test_shared_memory, AV_SCAN_RESPONSE);
        TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should find scan response");
        
        /* 清除命令准备下次测试 */
        av_clear_shared_memory_command(&test_shared_memory);
    }
    
    return 0;
}

/* 测试带类型的响应 */
int test_typed_responses(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 初始化测试共享内存 */
    memset(&test_shared_memory, 0, sizeof(test_shared_memory));
    
    const char* test_uuid = "12345678-1234-1234-1234-123456789abc";
    
    /* 测试ACK响应 */
    int ret = av_send_typed_scan_response(&service, &test_shared_memory, 
                                         test_uuid, AV_RESPONSE_TYPE_ACK);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "ACK response should succeed");
    
    ret = av_check_shared_memory_command(&test_shared_memory, AV_SCAN_RESPONSE);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should find ACK response");
    
    av_clear_shared_memory_command(&test_shared_memory);
    
    /* 测试ERROR响应 */
    ret = av_send_typed_scan_response(&service, &test_shared_memory, 
                                     test_uuid, AV_RESPONSE_TYPE_ERROR);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "ERROR response should succeed");
    
    ret = av_check_shared_memory_command(&test_shared_memory, "SCAN_ERR");
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should find ERROR response");
    
    av_clear_shared_memory_command(&test_shared_memory);
    
    /* 测试BUSY响应 */
    ret = av_send_typed_scan_response(&service, &test_shared_memory, 
                                     test_uuid, AV_RESPONSE_TYPE_BUSY);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "BUSY response should succeed");
    
    ret = av_check_shared_memory_command(&test_shared_memory, "SCAN_BUSY");
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should find BUSY response");
    
    return 0;
}

/* 测试响应统计功能 */
int test_response_statistics(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 初始化响应统计 */
    av_response_stats_init();
    
    /* 初始化测试共享内存 */
    memset(&test_shared_memory, 0, sizeof(test_shared_memory));
    
    /* 发送多个响应来测试统计 */
    for (int i = 0; i < 10; i++) {
        av_write_shared_memory_command(&test_shared_memory, AV_SCAN_REQUEST);
        
        int ret = av_send_scan_response(&service, &test_shared_memory, AV_SCAN_RESPONSE);
        TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Response should succeed");
        
        av_clear_shared_memory_command(&test_shared_memory);
        
        /* 短暂延迟模拟真实场景 */
        usleep(1000); /* 1ms */
    }
    
    /* 生成统计报告（验证不会崩溃） */
    av_response_stats_report();
    
    return 0;
}

/* 测试批量响应处理 */
int test_batch_response_processing(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 初始化服务 */
    pthread_mutex_init(&service.context_mutex, NULL);
    
    const char* test_uuid1 = "12345678-1234-1234-1234-123456789abc";
    const char* test_uuid2 = "*************-4321-4321-cba987654321";
    
    /* 添加多个VM上下文 */
    av_add_vm_context(&service, test_uuid1);
    av_add_vm_context(&service, test_uuid2);
    
    /* 设置映射地址和启用监听 */
    av_context_t* ctx1 = av_find_vm_context(&service, test_uuid1);
    av_context_t* ctx2 = av_find_vm_context(&service, test_uuid2);
    
    TEST_ASSERT(ctx1 != NULL, "Should find first VM context");
    TEST_ASSERT(ctx2 != NULL, "Should find second VM context");
    
    /* 为测试目的，使用同一个共享内存 */
    ctx1->mapped_addr = &test_shared_memory;
    ctx2->mapped_addr = &test_shared_memory;
    ctx1->monitoring_active = 1;
    ctx2->monitoring_active = 1;
    
    /* 模拟有待处理的请求 */
    av_write_shared_memory_command(&test_shared_memory, AV_SCAN_REQUEST);
    
    /* 执行批量处理 */
    int ret = av_process_batch_responses(&service);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Batch processing should succeed");
    
    /* 清理 */
    av_remove_vm_context(&service, test_uuid1);
    av_remove_vm_context(&service, test_uuid2);
    pthread_mutex_destroy(&service.context_mutex);
    
    return 0;
}

/* 测试响应健康检查 */
int test_response_health_check(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 初始化服务 */
    pthread_mutex_init(&service.context_mutex, NULL);
    
    /* 执行健康检查 */
    int ret = av_response_health_check(&service);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Health check should succeed");
    
    /* 执行性能优化 */
    ret = av_optimize_response_performance(&service);
    TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Performance optimization should succeed");
    
    /* 清理 */
    pthread_mutex_destroy(&service.context_mutex);
    
    return 0;
}

/* 测试并发响应处理 */
int test_concurrent_responses(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 初始化测试共享内存 */
    memset(&test_shared_memory, 0, sizeof(test_shared_memory));
    
    const char* test_uuid = "12345678-1234-1234-1234-123456789abc";
    
    /* 模拟并发请求处理 */
    for (int i = 0; i < 5; i++) {
        /* 写入请求 */
        av_write_shared_memory_command(&test_shared_memory, AV_SCAN_REQUEST);
        
        /* 并发发送响应 */
        int ret = av_send_typed_scan_response(&service, &test_shared_memory, 
                                             test_uuid, AV_RESPONSE_TYPE_ACK);
        TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Concurrent response should succeed");
        
        /* 验证响应 */
        ret = av_check_shared_memory_command(&test_shared_memory, AV_SCAN_RESPONSE);
        TEST_ASSERT_EQUAL(AV_SUCCESS, ret, "Should find response");
        
        /* 清除准备下次测试 */
        av_clear_shared_memory_command(&test_shared_memory);
    }
    
    return 0;
}

/* 测试错误响应处理 */
int test_error_response_handling(void) {
    av_host_service_t service;
    memset(&service, 0, sizeof(service));
    
    /* 测试NULL参数 */
    int ret = av_send_scan_response(NULL, &test_shared_memory, AV_SCAN_RESPONSE);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL service");
    
    ret = av_send_scan_response(&service, NULL, AV_SCAN_RESPONSE);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL shared memory");
    
    ret = av_send_scan_response(&service, &test_shared_memory, NULL);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL response");
    
    /* 测试带类型响应的错误处理 */
    ret = av_send_typed_scan_response(NULL, &test_shared_memory, "test-uuid", AV_RESPONSE_TYPE_ACK);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL service");
    
    ret = av_send_typed_scan_response(&service, NULL, "test-uuid", AV_RESPONSE_TYPE_ACK);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL shared memory");
    
    ret = av_send_typed_scan_response(&service, &test_shared_memory, NULL, AV_RESPONSE_TYPE_ACK);
    TEST_ASSERT_EQUAL(AV_ERROR_INVALID_PARAM, ret, "Should reject NULL UUID");
    
    return 0;
}

/* 主测试函数 */
int main(void) {
    test_init();
    
    /* 运行测试 */
    RUN_TEST(test_response_time_performance);
    RUN_TEST(test_typed_responses);
    RUN_TEST(test_response_statistics);
    RUN_TEST(test_batch_response_processing);
    RUN_TEST(test_response_health_check);
    RUN_TEST(test_concurrent_responses);
    RUN_TEST(test_error_response_handling);
    
    /* 输出测试结果 */
    test_summary();
    
    return (tests_failed > 0) ? 1 : 0;
}