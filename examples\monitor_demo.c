#include "../host/av_host.h"
#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <unistd.h>

/* 全局服务实例 */
static av_host_service_t g_service;
static volatile int g_running = 1;

/* 信号处理函数 */
void signal_handler(int sig) {
    (void)sig;
    printf("\nReceived shutdown signal, stopping service...\n");
    g_running = 0;
}

int main(int argc, char* argv[]) {
    if (argc != 2) {
        printf("Usage: %s <vm_uuid>\n", argv[0]);
        printf("Example: %s 12345678-1234-1234-1234-123456789abc\n", argv[0]);
        return 1;
    }

    const char* vm_uuid = argv[1];
    
    /* 设置信号处理 */
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    printf("=== XenServer Antivirus Monitor Demo ===\n");
    printf("Monitoring VM: %s\n", vm_uuid);

    /* 初始化服务 */
    printf("Initializing host service...\n");
    int ret = av_host_service_init(&g_service);
    if (ret != AV_SUCCESS) {
        printf("Failed to initialize service: %s\n", av_error_string(ret));
        return 1;
    }

    /* 添加VM上下文 */
    printf("Adding VM context...\n");
    ret = av_add_vm_context(&g_service, vm_uuid);
    if (ret != AV_SUCCESS) {
        printf("Failed to add VM context: %s\n", av_error_string(ret));
        av_host_service_cleanup(&g_service);
        return 1;
    }

    /* 查找VM域ID */
    domid_t domid;
    printf("Finding VM by UUID...\n");
    ret = av_find_vm_by_uuid(&g_service, vm_uuid, &domid);
    if (ret != AV_SUCCESS) {
        printf("Failed to find VM: %s\n", av_error_string(ret));
        av_host_service_cleanup(&g_service);
        return 1;
    }

    printf("Found VM with domain ID: %u\n", domid);

    /* 验证VM状态 */
    printf("Verifying VM state...\n");
    ret = av_verify_domain_state(&g_service, domid);
    if (ret != AV_SUCCESS) {
        printf("VM state verification failed: %s\n", av_error_string(ret));
        av_host_service_cleanup(&g_service);
        return 1;
    }

    printf("VM is running and healthy\n");

    /* 创建共享内存 */
    printf("Creating shared memory...\n");
    grant_ref_t grant_ref;
    ret = av_create_shared_memory(&g_service, AV_SHARED_MEMORY_SIZE, &grant_ref);
    if (ret != AV_SUCCESS) {
        printf("Failed to create shared memory: %s\n", av_error_string(ret));
        av_host_service_cleanup(&g_service);
        return 1;
    }

    printf("Created shared memory with grant reference: %u\n", grant_ref);

    /* 映射共享内存 */
    printf("Mapping shared memory...\n");
    void* shm_addr = av_map_shared_memory(&g_service, grant_ref, AV_SHARED_MEMORY_SIZE);
    if (!shm_addr) {
        printf("Failed to map shared memory\n");
        av_host_service_cleanup(&g_service);
        return 1;
    }

    printf("Mapped shared memory at address: %p\n", shm_addr);

    /* 更新VM上下文 */
    av_context_t* ctx = av_find_vm_context(&g_service, vm_uuid);
    if (ctx) {
        ctx->domain_id = domid;
        ctx->shm_grant_ref = grant_ref;
        ctx->mapped_addr = shm_addr;
    }

    /* 写入共享内存ID到XenStore */
    printf("Writing shared memory ID to XenStore...\n");
    ret = av_xenstore_write_shm_id(&g_service, vm_uuid, grant_ref);
    if (ret != AV_SUCCESS) {
        printf("Failed to write to XenStore: %s\n", av_error_string(ret));
        av_cleanup_shared_memory(&g_service, shm_addr, grant_ref, AV_SHARED_MEMORY_SIZE);
        av_host_service_cleanup(&g_service);
        return 1;
    }

    printf("Shared memory ID written to XenStore successfully\n");

    /* 启用VM监听 */
    printf("Enabling VM monitoring...\n");
    ret = av_enable_vm_monitoring(&g_service, vm_uuid);
    if (ret != AV_SUCCESS) {
        printf("Failed to enable monitoring: %s\n", av_error_string(ret));
        av_cleanup_shared_memory(&g_service, shm_addr, grant_ref, AV_SHARED_MEMORY_SIZE);
        av_host_service_cleanup(&g_service);
        return 1;
    }

    /* 启动服务 */
    printf("Starting host service...\n");
    ret = av_host_service_start(&g_service);
    if (ret != AV_SUCCESS) {
        printf("Failed to start service: %s\n", av_error_string(ret));
        av_cleanup_shared_memory(&g_service, shm_addr, grant_ref, AV_SHARED_MEMORY_SIZE);
        av_host_service_cleanup(&g_service);
        return 1;
    }

    printf("Service started successfully. Monitoring for scan requests...\n");
    printf("Press Ctrl+C to stop.\n\n");

    /* 主循环 */
    while (g_running) {
        sleep(1);
        
        /* 可以在这里添加状态报告 */
        static int counter = 0;
        if (++counter % 30 == 0) { /* 每30秒报告一次 */
            printf("Service running... (uptime: %d seconds)\n", counter);
            
            /* 执行健康检查 */
            av_response_health_check(&g_service);
        }
        
        /* 每5分钟生成统计报告 */
        if (counter % 300 == 0) {
            printf("\n=== Performance Report ===\n");
            av_response_stats_report();
            printf("==========================\n\n");
        }
    }

    /* 清理资源 */
    printf("\nShutting down...\n");
    
    av_disable_vm_monitoring(&g_service, vm_uuid);
    av_host_service_stop(&g_service);
    av_cleanup_shared_memory(&g_service, shm_addr, grant_ref, AV_SHARED_MEMORY_SIZE);
    av_host_service_cleanup(&g_service);

    printf("Service stopped successfully\n");
    return 0;
}