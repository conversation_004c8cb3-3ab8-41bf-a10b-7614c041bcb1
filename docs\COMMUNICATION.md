# 通信协议实现文档

## 概述

本文档描述了XenServer无代理防病毒系统中宿主机与虚拟机之间的通信协议实现，重点介绍内存监听机制的设计和使用方法。

## 架构设计

### 通信流程

1. **初始化阶段**
   - 宿主机服务创建4KB共享内存区域
   - 将共享内存ID写入XenStore路径 `/guest/<vm_uuid>/data/av_shm_id`
   - 启动监听线程，持续监控共享内存

2. **请求处理阶段**
   - 虚拟机客户端向共享内存前16字节写入 `SCAN_REQ` 命令
   - 宿主机监听线程检测到请求
   - 在100毫秒内向共享内存写入 `SCAN_ACK` 响应
   - 清除请求命令，准备下次通信

3. **清理阶段**
   - 虚拟机客户端读取响应后解除内存映射
   - 宿主机服务停止时清理所有共享内存资源

## 核心组件

### 1. 内存监听机制

#### 主要函数

```c
/* 监听共享内存中的扫描请求 */
int av_monitor_scan_requests(av_host_service_t* service, void* shm_addr);

/* 发送扫描响应到共享内存 */
int av_send_scan_response(av_host_service_t* service, void* shm_addr, const char* response);

/* 监听线程函数 */
void* av_monitor_thread_func(void* arg);
```

#### 监听线程管理

```c
/* 启动监听线程 */
int av_start_monitor_thread(av_host_service_t* service);

/* 停止监听线程 */
int av_stop_monitor_thread(av_host_service_t* service);
```

#### VM监听控制

```c
/* 启用VM上下文的监听 */
int av_enable_vm_monitoring(av_host_service_t* service, const char* vm_uuid);

/* 禁用VM上下文的监听 */
int av_disable_vm_monitoring(av_host_service_t* service, const char* vm_uuid);
```

### 2. 共享内存操作

#### 内存布局

```c
typedef struct {
    char command[16];        /* 命令字段: "SCAN_REQ" 或 "SCAN_ACK" */
    char reserved[48];       /* 保留字段，用于未来扩展 */
    /* 剩余空间可用于传输扫描数据或结果 */
} av_shared_memory_layout_t;
```

#### 操作函数

```c
/* 读取共享内存命令 */
int av_read_shared_memory_command(void* addr, char* command, size_t command_size);

/* 写入共享内存命令 */
int av_write_shared_memory_command(void* addr, const char* command);

/* 检查共享内存命令 */
int av_check_shared_memory_command(void* addr, const char* expected_command);

/* 清除共享内存命令 */
int av_clear_shared_memory_command(void* addr);
```

## 性能特性

### 响应时间要求

- **目标响应时间**: < 100毫秒
- **监听频率**: 每10毫秒检查一次
- **线程安全**: 使用互斥锁保护上下文访问

### 资源管理

- **内存使用**: 每个VM 4KB共享内存
- **线程开销**: 单个监听线程处理所有VM
- **CPU占用**: 低优先级轮询，避免过度占用

## 使用示例

### 基本使用流程

```c
#include "av_host.h"

int main() {
    av_host_service_t service;
    const char* vm_uuid = "12345678-1234-1234-1234-123456789abc";
    
    /* 1. 初始化服务 */
    av_host_service_init(&service);
    
    /* 2. 添加VM上下文 */
    av_add_vm_context(&service, vm_uuid);
    
    /* 3. 查找VM并创建共享内存 */
    domid_t domid;
    av_find_vm_by_uuid(&service, vm_uuid, &domid);
    
    grant_ref_t grant_ref;
    av_create_shared_memory(&service, AV_SHARED_MEMORY_SIZE, &grant_ref);
    
    void* shm_addr = av_map_shared_memory(&service, grant_ref, AV_SHARED_MEMORY_SIZE);
    
    /* 4. 更新上下文并写入XenStore */
    av_context_t* ctx = av_find_vm_context(&service, vm_uuid);
    ctx->domain_id = domid;
    ctx->shm_grant_ref = grant_ref;
    ctx->mapped_addr = shm_addr;
    
    av_xenstore_write_shm_id(&service, vm_uuid, grant_ref);
    
    /* 5. 启用监听并启动服务 */
    av_enable_vm_monitoring(&service, vm_uuid);
    av_host_service_start(&service);
    
    /* 6. 运行服务... */
    
    /* 7. 清理资源 */
    av_host_service_stop(&service);
    av_cleanup_shared_memory(&service, shm_addr, grant_ref, AV_SHARED_MEMORY_SIZE);
    av_host_service_cleanup(&service);
    
    return 0;
}
```

### 示例程序

项目提供了完整的示例程序 `examples/monitor_demo.c`，演示了如何使用监听机制：

```bash
# 编译示例程序
make examples

# 运行示例程序
sudo ./examples/bin/monitor_demo 12345678-1234-1234-1234-123456789abc
```

## 错误处理

### 常见错误码

- `AV_ERROR_INVALID_PARAM`: 无效参数
- `AV_ERROR_NOT_INITIALIZED`: 服务未初始化
- `AV_ERROR_VM_NOT_FOUND`: 虚拟机未找到
- `AV_ERROR_TIMEOUT`: 操作超时
- `AV_ERROR_PROTOCOL`: 协议错误

### 错误恢复机制

1. **重试机制**: 对临时性错误自动重试
2. **资源清理**: 发生错误时自动清理已分配资源
3. **状态恢复**: 服务重启后自动恢复监听状态
4. **日志记录**: 详细记录所有错误信息

## 测试验证

### 单元测试

```bash
# 运行通信协议测试
make -C tests test-communication
```

测试覆盖：
- 共享内存命令操作
- 监听请求处理
- VM监听控制
- 无效参数处理
- 响应时间性能

### 集成测试

使用示例程序进行端到端测试：

1. 启动宿主机监听服务
2. 模拟虚拟机发送扫描请求
3. 验证响应时间和正确性
4. 测试异常情况处理

## 配置选项

### 编译时配置

```c
#define AV_SHARED_MEMORY_SIZE 4096    /* 共享内存大小 */
#define AV_COMMAND_SIZE 16            /* 命令字段大小 */
#define AV_SCAN_REQUEST "SCAN_REQ"    /* 扫描请求命令 */
#define AV_SCAN_RESPONSE "SCAN_ACK"   /* 扫描响应命令 */
```

### 运行时配置

- 监听频率：可通过修改 `usleep(10000)` 调整
- 响应超时：可通过修改响应时间检查逻辑调整
- 日志级别：通过环境变量或配置文件设置

## 故障排除

### 常见问题

1. **监听线程无法启动**
   - 检查权限：确保以root权限运行
   - 检查资源：确保系统有足够内存和线程资源

2. **共享内存创建失败**
   - 检查Xen环境：确保Xen hypervisor正在运行
   - 检查libxc库：确保正确安装和链接

3. **响应时间过长**
   - 检查系统负载：高负载可能影响响应时间
   - 调整监听频率：减少轮询间隔

4. **XenStore写入失败**
   - 检查XenStore服务：确保XenStore daemon正在运行
   - 检查路径权限：确保有权限写入指定路径

### 调试技巧

1. **启用调试日志**
   ```c
   av_log(AV_LOG_DEBUG, "Debug message");
   ```

2. **使用GDB调试**
   ```bash
   gdb ./examples/bin/monitor_demo
   ```

3. **检查系统状态**
   ```bash
   # 检查Xen状态
   xl list
   
   # 检查XenStore
   xenstore-ls /guest
   
   # 检查进程状态
   ps aux | grep monitor_demo
   ```

## 扩展开发

### 添加新命令

1. 在 `av_common.h` 中定义新命令常量
2. 在监听逻辑中添加命令处理
3. 更新测试用例验证新功能

### 性能优化

1. **批量处理**: 一次处理多个请求
2. **异步I/O**: 使用异步I/O减少阻塞
3. **内存池**: 预分配内存池减少分配开销

### 安全增强

1. **访问控制**: 验证请求来源的合法性
2. **数据加密**: 对敏感数据进行加密传输
3. **审计日志**: 记录所有通信活动