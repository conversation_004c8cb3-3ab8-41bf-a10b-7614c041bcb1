#!/bin/bash
# XenServer防病毒Linux客户端部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

# 检查是否在Xen环境中
check_xen_environment() {
    log_info "Checking Xen environment..."
    
    if [[ ! -d /proc/xen ]]; then
        log_error "Not running in a Xen environment"
        log_error "/proc/xen directory not found"
        exit 1
    fi
    
    if [[ ! -d /dev/xen ]]; then
        log_warn "/dev/xen directory not found, but /proc/xen exists"
        log_warn "This might be a PV guest without device access"
    fi
    
    log_info "✓ Xen environment detected"
}

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."
    
    # 检查必要的库
    if ! ldconfig -p | grep -q libxenctrl; then
        log_error "libxenctrl not found"
        log_info "Try: apt install libxen-dev"
        exit 1
    fi
    
    if ! ldconfig -p | grep -q libxenstore; then
        log_error "libxenstore not found"
        log_info "Try: apt install libxen-dev"
        exit 1
    fi
    
    log_info "✓ All dependencies found"
}

# 安装客户端
install_client() {
    local binary_path="$1"
    local install_path="/usr/local/bin/av_linux_client"
    
    if [[ ! -f "$binary_path" ]]; then
        log_error "Binary file not found: $binary_path"
        exit 1
    fi
    
    log_info "Installing client to $install_path..."
    cp "$binary_path" "$install_path"
    chmod +x "$install_path"
    
    log_info "✓ Client installed successfully"
}

# 创建systemd服务
create_service() {
    local service_file="/etc/systemd/system/xenserver-antivirus-client.service"
    
    log_info "Creating systemd service..."
    
    cat > "$service_file" << 'EOF'
[Unit]
Description=XenServer Antivirus Client
After=network.target

[Service]
Type=forking
ExecStart=/usr/local/bin/av_linux_client -d -f /var/log/av_client.log
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=5
User=root

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    log_info "✓ Systemd service created"
}

# 测试客户端
test_client() {
    local client_path="/usr/local/bin/av_linux_client"
    
    log_info "Testing client..."
    
    # 测试帮助信息
    if ! "$client_path" --help > /dev/null 2>&1; then
        log_error "Client help test failed"
        return 1
    fi
    
    # 测试初始化（会失败但不应该崩溃）
    log_info "Testing client initialization (expected to fail in non-privileged mode)..."
    timeout 5s "$client_path" -v 2>&1 | head -10 || true
    
    log_info "✓ Client basic tests passed"
}

# 主函数
main() {
    log_info "XenServer Antivirus Client Deployment Script"
    log_info "============================================="
    
    # 检查参数
    if [[ $# -lt 1 ]]; then
        echo "Usage: $0 <binary_path> [options]"
        echo "Options:"
        echo "  --service    Create systemd service"
        echo "  --start      Start the service after installation"
        exit 1
    fi
    
    local binary_path="$1"
    local create_service_flag=false
    local start_service_flag=false
    
    # 解析选项
    shift
    while [[ $# -gt 0 ]]; do
        case $1 in
            --service)
                create_service_flag=true
                shift
                ;;
            --start)
                start_service_flag=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_root
    check_xen_environment
    check_dependencies
    install_client "$binary_path"
    test_client
    
    if [[ "$create_service_flag" == true ]]; then
        create_service
        
        if [[ "$start_service_flag" == true ]]; then
            log_info "Starting service..."
            systemctl enable xenserver-antivirus-client
            systemctl start xenserver-antivirus-client
            
            sleep 2
            if systemctl is-active --quiet xenserver-antivirus-client; then
                log_info "✓ Service started successfully"
            else
                log_error "Service failed to start"
                systemctl status xenserver-antivirus-client
            fi
        fi
    fi
    
    log_info "============================================="
    log_info "Deployment completed successfully!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Test the client: sudo /usr/local/bin/av_linux_client --help"
    log_info "2. Run in foreground: sudo /usr/local/bin/av_linux_client -v"
    log_info "3. Check logs: tail -f /var/log/av_client.log"
    
    if [[ "$create_service_flag" == true ]]; then
        log_info "4. Service commands:"
        log_info "   - Status: systemctl status xenserver-antivirus-client"
        log_info "   - Start:  systemctl start xenserver-antivirus-client"
        log_info "   - Stop:   systemctl stop xenserver-antivirus-client"
    fi
}

# 运行主函数
main "$@"