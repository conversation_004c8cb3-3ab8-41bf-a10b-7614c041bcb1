# XenServer宿主机防病毒服务Makefile

# 编译器和编译选项
CC = gcc
CFLAGS = -Wall -Wextra -std=gnu99 -O2 -g -D_GNU_SOURCE -D__XEN_TOOLS__=1

# 获取Xen库的编译和链接标志
XEN_CFLAGS := $(shell pkg-config --cflags xencontrol xenstore 2>/dev/null)
XEN_LDFLAGS := $(shell pkg-config --libs xencontrol xenstore 2>/dev/null)
CFLAGS += $(XEN_CFLAGS)
LDFLAGS = $(XEN_LDFLAGS) -lxengnttab -lpthread

# 目录定义
SRCDIR = .
COMMONDIR = ../common
OBJDIR = obj
BINDIR = bin

# 源文件和目标文件
COMMON_SOURCES = $(COMMONDIR)/av_common.c
HOST_SOURCES = main.c av_service.c av_vm_discovery.c av_xenstore.c av_error_handling.c av_shared_memory.c av_communication.c av_resource_manager.c
SOURCES = $(COMMON_SOURCES) $(HOST_SOURCES)
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/xenserver-antivirus-host

# 头文件搜索路径
INCLUDES = -I$(COMMONDIR) -I/usr/include/xen

# 默认目标
all: directories $(TARGET)

# 创建必要的目录
directories:
	@mkdir -p $(OBJDIR)/$(COMMONDIR)
	@mkdir -p $(BINDIR)

# 链接目标程序
$(TARGET): $(OBJECTS)
	@echo "Linking $@..."
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)
	@echo "Build complete: $@"

# 编译源文件
$(OBJDIR)/%.o: %.c
	@echo "Compiling $<..."
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 清理编译文件
clean:
	@echo "Cleaning build files..."
	rm -rf $(OBJDIR) $(BINDIR)

# 安装程序
install: $(TARGET)
	@echo "Installing $(TARGET)..."
	sudo cp $(TARGET) /usr/local/bin/xenserver-antivirus-host
	sudo chmod +x /usr/local/bin/xenserver-antivirus-host
	
	# 创建配置目录
	sudo mkdir -p /etc/xenserver-antivirus
	sudo cp ../examples/host.conf /etc/xenserver-antivirus/host.conf.example
	
	# 安装服务脚本
	sudo cp ../scripts/xenserver-antivirus-host /etc/init.d/
	sudo chmod +x /etc/init.d/xenserver-antivirus-host
	
	# 安装systemd服务文件
	sudo cp ../scripts/xenserver-antivirus-host.service /etc/systemd/system/
	sudo systemctl daemon-reload
	
	# 创建日志目录
	sudo mkdir -p /var/log/xenserver-antivirus
	
	@echo "Installation complete"
	@echo "To start the service:"
	@echo "  systemctl start xenserver-antivirus-host"
	@echo "  systemctl enable xenserver-antivirus-host"

# 卸载程序
uninstall:
	@echo "Uninstalling xenserver-antivirus-host..."
	
	# 停止并禁用服务
	-sudo systemctl stop xenserver-antivirus-host
	-sudo systemctl disable xenserver-antivirus-host
	
	# 移除文件
	sudo rm -f /usr/local/bin/xenserver-antivirus-host
	sudo rm -f /etc/init.d/xenserver-antivirus-host
	sudo rm -f /etc/systemd/system/xenserver-antivirus-host.service
	sudo systemctl daemon-reload
	
	@echo "Uninstall complete"
	@echo "Configuration files in /etc/xenserver-antivirus/ were preserved"

# 检查依赖
check-deps:
	@echo "Checking dependencies..."
	@pkg-config --exists xencontrol || (echo "ERROR: libxenctrl not found" && exit 1)
	@pkg-config --exists xenstore || (echo "ERROR: libxenstore not found" && exit 1)
	@echo "All dependencies found"

# 运行程序（需要root权限）
run: $(TARGET)
	@echo "Running $(TARGET) (requires root privileges)..."
	sudo $(TARGET)

# 调试版本
debug: CFLAGS += -DDEBUG -g3
debug: $(TARGET)

# 发布版本
release: CFLAGS += -DNDEBUG -O3
release: clean $(TARGET)

# 代码格式化
format:
	@echo "Formatting code..."
	find . -name "*.c" -o -name "*.h" | xargs clang-format -i

# 静态分析
analyze:
	@echo "Running static analysis..."
	cppcheck --enable=all --std=c99 $(SRCDIR) $(COMMONDIR)

# 帮助信息
help:
	@echo "Available targets:"
	@echo "  all        - Build the program (default)"
	@echo "  clean      - Remove build files"
	@echo "  install    - Install the program to /usr/local/bin"
	@echo "  uninstall  - Remove the installed program"
	@echo "  check-deps - Check for required dependencies"
	@echo "  run        - Build and run the program"
	@echo "  debug      - Build debug version"
	@echo "  release    - Build optimized release version"
	@echo "  format     - Format source code"
	@echo "  analyze    - Run static code analysis"
	@echo "  help       - Show this help message"

.PHONY: all clean install uninstall check-deps run debug release format analyze help directories